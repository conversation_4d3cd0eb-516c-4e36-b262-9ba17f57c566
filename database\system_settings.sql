-- =====================================================================
-- System Settings Table for UTHM Attendance System
-- This table stores global system configuration settings
-- =====================================================================

-- Create the system_settings table with minimal required columns
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert the default course registration setting (enabled by default)
INSERT IGNORE INTO system_settings (setting_key, setting_value) 
VALUES ('course_registration_enabled', '1');

-- Verify the setup
SELECT 
    setting_key,
    setting_value,
    CASE 
        WHEN setting_value = '1' THEN 'ENABLED'
        ELSE 'DISABLED'
    END as status,
    updated_at
FROM system_settings 
WHERE setting_key = 'course_registration_enabled';

-- =====================================================================
-- USAGE NOTES:
-- =====================================================================
-- 
-- This SQL script creates the necessary database structure for the
-- global course registration toggle feature.
--
-- To use this script:
-- 1. Open phpMyAdmin
-- 2. Select your 'uthm_attendance' database
-- 3. Go to the SQL tab
-- 4. Copy and paste this entire script
-- 5. Click "Go" to execute
--
-- The script will:
-- - Create the system_settings table if it doesn't exist
-- - Insert the default registration setting (enabled)
-- - Show the current status for verification
--
-- After running this script, the course registration toggle feature
-- will be fully functional in the admin panel.
--
-- =====================================================================
