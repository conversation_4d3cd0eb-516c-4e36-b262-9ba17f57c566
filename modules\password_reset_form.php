<?php
/**
 * Secure Password Reset Form
 * Handles token-based password reset from email links
 */

session_start();
require '../config/config.php';
require_once __DIR__ . '/../includes/PasswordResetService.php';

// Get token from URL and clean it
$rawToken = $_GET['token'] ?? '';
$token = trim(urldecode($rawToken)); // Properly decode URL-encoded token
$message = '';
$messageType = 'error';
$tokenValid = false;
$tokenData = null;

try {
    $passwordResetService = new PasswordResetService($conn);

    // Validate token
    if (!empty($token)) {
        // Check if table exists first
        $tableCheck = $conn->query("SHOW TABLES LIKE 'password_reset_tokens'");
        if ($tableCheck->num_rows === 0) {
            $message = "Password reset system is not properly configured. Please contact your administrator.";
        } else {
            $tokenData = $passwordResetService->validateToken($token);
            if ($tokenData) {
                $tokenValid = true;
            } else {
                $message = "Invalid or expired password reset link. Please request a new password reset.";
            }
        }
    } else {
        $message = "No password reset token provided.";
    }

} catch (Exception $e) {
    $message = "System error: " . $e->getMessage();
    error_log("Password reset form error: " . $e->getMessage());
}

// Handle password reset form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $tokenValid) {
    $newPassword = $_POST['new_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    if (empty($newPassword) || empty($confirmPassword)) {
        $message = "Please fill in both password fields.";
        $messageType = 'error';
    } elseif ($newPassword !== $confirmPassword) {
        $message = "Passwords do not match. Please try again.";
        $messageType = 'error';
    } else {
        // Reset password using the service
        $resetResult = $passwordResetService->resetPassword($token, $newPassword);
        
        if ($resetResult['success']) {
            $message = "Password reset successfully! You can now log in with your new password.";
            $messageType = 'success';
            $tokenValid = false; // Hide form after successful reset
            
            // Clear any first-login session data
            unset($_SESSION['first_login_user_id'], $_SESSION['first_login_role']);
            
        } else {
            $message = $resetResult['message'];
            $messageType = 'error';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - UTHM Attendance</title>
    
    <!-- ─── GLOBAL CSS (same as other dashboards) ─── -->
    <link rel="stylesheet" href="../dashboard/css/base-styles.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-header.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-sidebar.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-footer.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-dashboard-styles.css" />
    
    <!-- ─── ICONS ─── -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        /* ─────────── PASSWORD RESET FORM SPECIFIC STYLES ─────────── */
        .reset-container {
            max-width: 500px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        .reset-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-md);
        }

        .reset-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
        }

        .reset-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-md);
        }

        .reset-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .reset-subtitle {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .alert-message {
            padding: var(--spacing-md);
            border-radius: var(--border-radius);
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .alert-success {
            background-color: #d1f2eb;
            border: 1px solid #7dcea0;
            color: #0e6b47;
        }

        .alert-error {
            background-color: #fadbd8;
            border: 1px solid #f1948a;
            color: #922b21;
        }
        .token-info {
            background: var(--bg-light);
            padding: var(--spacing-md);
            border-radius: var(--border-radius);
            margin-bottom: var(--spacing-lg);
            border: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .form-group {
            margin-bottom: var(--spacing-lg);
        }

        .form-label {
            display: block;
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .form-input {
            width: 100%;
            padding: var(--spacing-md);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: #ffffff;
            border: 2px solid #3b82f6;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            min-height: 48px;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
            margin-bottom: var(--spacing-md);
        }

        .submit-btn:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            border-color: #2563eb;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }

        .secondary-btn {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            border-color: #6b7280;
        }

        .secondary-btn:hover {
            background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
            border-color: #4b5563;
        }

        .password-requirements {
            background: var(--bg-light);
            padding: var(--spacing-md);
            border-radius: var(--border-radius);
            margin: var(--spacing-md) 0;
            border: 1px solid var(--border-color);
            font-size: 0.9rem;
        }

        .password-requirements h6 {
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .password-requirements ul {
            margin: 0;
            padding-left: var(--spacing-lg);
        }

        .password-requirements li {
            margin-bottom: var(--spacing-xs);
            color: var(--text-secondary);
        }

        .back-link {
            display: block;
            text-align: center;
            margin-top: var(--spacing-lg);
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .back-link:hover {
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-card">
            <div class="reset-header">
                <div class="reset-icon">
                    <i class="fas fa-key"></i>
                </div>
                <h1 class="reset-title">Reset Your Password</h1>
                <p class="reset-subtitle">UTHM Attendance System</p>
            </div>

            <?php if ($message): ?>
                <div class="alert-message alert-<?php echo $messageType; ?>">
                    <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-circle'; ?>"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <?php if ($tokenValid && $tokenData): ?>
                <div class="token-info">
                    <i class="fas fa-user"></i>
                    <div>
                        <strong>Account:</strong> <?php echo htmlspecialchars($tokenData['user_id']); ?> (<?php echo ucfirst($tokenData['user_role']); ?>)
                    </div>
                </div>

                <form method="POST" id="resetForm">
                    <div class="form-group">
                        <label for="new_password" class="form-label">
                            <i class="fas fa-lock"></i>
                            New Password:
                        </label>
                        <input type="password" class="form-input" id="new_password" name="new_password" required>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password" class="form-label">
                            <i class="fas fa-lock"></i>
                            Confirm New Password:
                        </label>
                        <input type="password" class="form-input" id="confirm_password" name="confirm_password" required>
                    </div>

                    <div class="password-requirements">
                        <h6>
                            <i class="fas fa-info-circle"></i>
                            Password Requirements:
                        </h6>
                        <ul>
                            <li>At least 8 characters long</li>
                            <li>Contains at least one uppercase letter</li>
                            <li>Contains at least one lowercase letter</li>
                            <li>Contains at least one number</li>
                            <li>Contains at least one special character</li>
                        </ul>
                    </div>

                    <button type="submit" class="submit-btn">
                        <i class="fas fa-save"></i>
                        Reset Password
                    </button>
                </form>
            <?php endif; ?>

            <?php if ($messageType === 'success' || !$tokenValid): ?>
                <a href="../index.php" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    Back to Login
                </a>
            <?php endif; ?>

            <div style="text-align: center; margin-top: var(--spacing-lg); color: var(--text-secondary); font-size: 0.8rem;">
                <i class="fas fa-clock"></i>
                Generated on <?php echo date('Y-m-d H:i:s'); ?>
            </div>
        </div>
    </div>

    <script>
        // Password validation
        document.getElementById('resetForm')?.addEventListener('submit', function(e) {
            const password = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Passwords do not match!');
                return;
            }
            
            // Basic password strength check
            if (password.length < 8) {
                e.preventDefault();
                alert('Password must be at least 8 characters long!');
                return;
            }
        });
    </script>
</body>
</html>
