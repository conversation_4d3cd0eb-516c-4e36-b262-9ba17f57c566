<?php
/**
 * ===================================================================
 * SECURE LOGOUT IMPLEMENTATION
 * Comprehensive session destruction for all user roles
 * ===================================================================
 */

// Start session to access session data
session_start();

// Log the logout event for security audit
if (isset($_SESSION['user_id']) && isset($_SESSION['role'])) {
    $user_id = $_SESSION['user_id'];
    $role = $_SESSION['role'];
    $timestamp = date('Y-m-d H:i:s');
    error_log("LOGOUT: User ID $user_id (Role: $role) logged out at $timestamp");
}

// Comprehensive session cleanup
function secureSessionDestroy() {
    // 1. Unset all session variables
    session_unset();

    // 2. Destroy the session
    session_destroy();

    // 3. Clear session cookie securely
    if (isset($_COOKIE[session_name()])) {
        setcookie(
            session_name(),           // Session cookie name
            '',                       // Empty value
            time() - 3600,           // Expire in the past
            '/',                     // Path
            '',                      // Domain (empty for current domain)
            isset($_SERVER['HTTPS']), // Secure flag (true if HTTPS)
            true                     // HttpOnly flag
        );
    }

    // 4. Clear any other authentication-related cookies
    $authCookies = ['remember_token', 'auth_token', 'user_session'];
    foreach ($authCookies as $cookieName) {
        if (isset($_COOKIE[$cookieName])) {
            setcookie(
                $cookieName,
                '',
                time() - 3600,
                '/',
                '',
                isset($_SERVER['HTTPS']),
                true
            );
        }
    }

    // 5. Regenerate session ID to prevent session fixation
    session_start();
    session_regenerate_id(true);
    session_destroy();
}

// Execute secure session destruction
secureSessionDestroy();

// Redirect to login page with cache prevention headers
header("Cache-Control: no-cache, no-store, must-revalidate");
header("Pragma: no-cache");
header("Expires: 0");
header("Location: index.php?logout=success");
exit();
?>
