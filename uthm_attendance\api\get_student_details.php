<?php
header('Content-Type: application/json');
require '../config/config.php';

// Check if student_id is provided
if (!isset($_GET['student_id']) || empty($_GET['student_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Student ID is required']);
    exit();
}

$student_id = trim($_GET['student_id']);

try {
    // Fetch student details
    $stmt = $conn->prepare("SELECT Email FROM student WHERE UserID = ?");
    $stmt->bind_param("s", $student_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $student = $result->fetch_assoc();
        echo json_encode([
            'success' => true,
            'email' => $student['Email']
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'email' => 'Not found'
        ]);
    }
    
    $stmt->close();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database error']);
}

$conn->close();
?>
