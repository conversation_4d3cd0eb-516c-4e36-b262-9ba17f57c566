<?php
session_start();
require '../config/config.php';

if (!isset($_SESSION['first_login_user_id']) || !isset($_SESSION['first_login_role'])) {
    header("Location: ../index.php");
    exit();
}

$user_id = $_SESSION['first_login_user_id'];
$role = $_SESSION['first_login_role'];
$message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];

    if (empty($new_password) || empty($confirm_password)) {
        $message = "Please fill in both fields.";
    } elseif ($new_password !== $confirm_password) {
        $message = "Passwords do not match!";
    } elseif (
        strlen($new_password) < 8 ||
        !preg_match('/[A-Z]/', $new_password) ||
        !preg_match('/[a-z]/', $new_password) ||
        !preg_match('/[0-9]/', $new_password) ||
        !preg_match('/[^a-zA-Z0-9]/', $new_password)
    ) {
        $message = "Password must be at least 8 characters and include uppercase, lowercase, and a number.";
    } else {
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
        $table = ($role === 'student') ? 'student' : 'lecturer';
        $id_field = ($role === 'student') ? 'StudentID' : 'LectID';

        $stmt = $conn->prepare("UPDATE $table SET Password = ?, FirstLogin = 0 WHERE $id_field = ?");
        $stmt->bind_param("si", $hashed_password, $user_id);
        if ($stmt->execute()) {
            // Log password change for security audit
            error_log("PASSWORD CHANGE: $role ID $user_id changed password at " . date('Y-m-d H:i:s'));

            // Secure session cleanup after password change
            session_unset();
            session_destroy();

            // Clear session cookie
            if (isset($_COOKIE[session_name()])) {
                setcookie(
                    session_name(),
                    '',
                    time() - 3600,
                    '/',
                    '',
                    isset($_SERVER['HTTPS']),
                    true
                );
            }

            // Start new session and regenerate ID
            session_start();
            session_regenerate_id(true);

            header("Cache-Control: no-cache, no-store, must-revalidate");
            header("Pragma: no-cache");
            header("Expires: 0");
            header("Location: ../index.php?message=password_updated");
            exit();

        } else {
            $message = "Failed to update password.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Change Password</title>
    <link rel="stylesheet" href="../test-styles.css">
    <script src="https://kit.fontawesome.com/your-own-kit-code.js" crossorigin="anonymous"></script>
    <style>
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            overflow-y: auto;
        }

        .change-password-wrapper {
            max-width: 500px;
            margin: 60px auto;
            padding: 30px;
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .change-password-wrapper h2 {
            text-align: center;
            color: #0078d4;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
            position: relative;
        }

        label {
            font-weight: bold;
            display: block;
            margin-bottom: 5px;
            color: #333;
        }

        input[type="password"] {
            width: 100%;
            padding: 12px;
            border-radius: 5px;
            border: 1px solid #ccc;
            font-size: 14px;
        }

        .toggle-password-icon {
            position: absolute;
            top: 38px;
            right: 10px;
            cursor: pointer;
            color: #0078d4;
        }

        .btn {
            width: 100%;
            padding: 12px;
            background-color: #0078d4;
            color: white;
            font-weight: bold;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        .btn:hover {
            background-color: #00509e;
        }

        .strength {
            font-size: 13px;
            margin-top: 5px;
        }

        .strength.weak { color: red; }
        .strength.medium { color: orange; }
        .strength.strong { color: green; }

        .message {
            text-align: center;
            color: red;
            margin-bottom: 10px;
        }

        .header-logo {
            text-align: center;
            margin-top: 30px;
        }

        .header-logo img {
            height: 60px;
        }
    </style>
</head>
<body>

<div class="header-logo">
    <img src="../assets/images/logo-uthm2.png" alt="UTHM Logo">
</div>

<div class="change-password-wrapper">
    <h2>Set Your New Password</h2>

    <?php if ($message): ?>
        <p class="message"><?= $message ?></p>
    <?php endif; ?>

    <form method="POST">
        <div class="form-group">
            <label for="new_password">New Password</label>
            <input type="password" name="new_password" id="new_password" required>
            <i class="fas fa-eye toggle-password-icon" onclick="togglePassword('new_password', this)"></i>
            <div id="strengthMessage" class="strength"></div>
        </div>

        <div class="form-group">
            <label for="confirm_password">Confirm Password</label>
            <input type="password" name="confirm_password" id="confirm_password" required>
            <i class="fas fa-eye toggle-password-icon" onclick="togglePassword('confirm_password', this)"></i>
        </div>

        <button type="submit" class="btn">Update Password</button>
    </form>
</div>

<footer>
    <p>UNIVERSITI TUN HUSSEIN ONN MALAYSIA</p>
</footer>

<script>
    const passwordInput = document.getElementById('new_password');
    const strengthMessage = document.getElementById('strengthMessage');

    passwordInput.addEventListener('input', function () {
        const val = passwordInput.value;
        let strength = '';
        if (val.length < 8 || !val.match(/[A-Z]/) || !val.match(/[a-z]/) || !val.match(/[0-9]/) || !val.match(/[^a-zA-Z0-9]/)) {
            strength = 'Weak';
            strengthMessage.className = 'strength weak';
        } else if (val.length >= 10 && val.match(/[A-Z]/) && val.match(/[a-z]/) && val.match(/[0-9]/) && val.match(/[^a-zA-Z0-9]/)) {
            strength = 'Strong';
            strengthMessage.className = 'strength strong';
        } else {
            strength = 'Medium';
            strengthMessage.className = 'strength medium';
        }
        strengthMessage.textContent = "Password Strength: " + strength;
    });

    function togglePassword(id, icon) {
        const input = document.getElementById(id);
        const isVisible = input.type === "text";
        input.type = isVisible ? "password" : "text";
        icon.classList.toggle("fa-eye");
        icon.classList.toggle("fa-eye-slash");
    }
</script>
</body>
</html>