<?php
// ─────────────────────────────────────────────────────────────────────────────
//  modules/courses_details.php
//  - Course Details Page: allows lecturers to view comprehensive course information
//  - Shows course details and enrolled students list
//  - Uses the same header/sidebar/footer structure as other dashboard pages
// ─────────────────────────────────────────────────────────────────────────────

session_start();
require '../config/config.php';

// ─────────────────────────────────────────────────────────────────────────────
// 1) SECURITY CHECK: only 'lecturer' role can access this page
// ─────────────────────────────────────────────────────────────────────────────
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'lecturer') {
    header("Location: ../index.php");
    exit();
}

// ─────────────────────────────────────────────────────────────────────────────
// 2) DATABASE CAPABILITIES DETECTION
// ─────────────────────────────────────────────────────────────────────────────
function checkDatabaseCapabilities($conn) {
    $capabilities = [
        'course_code' => false,
        'course_status' => false,
        'course_assignments' => false,
        'registration_section' => false
    ];
    
    // Check for Course_Code column
    $result = $conn->query("SHOW COLUMNS FROM course LIKE 'Course_Code'");
    $capabilities['course_code'] = ($result && $result->num_rows > 0);
    
    // Check for Status column in course table
    $result = $conn->query("SHOW COLUMNS FROM course LIKE 'Status'");
    $capabilities['course_status'] = ($result && $result->num_rows > 0);
    
    // Check for course_assignments table
    $result = $conn->query("SHOW TABLES LIKE 'course_assignments'");
    $capabilities['course_assignments'] = ($result && $result->num_rows > 0);
    
    // Check for Section column in course_registration table
    $result = $conn->query("SHOW COLUMNS FROM course_registration LIKE 'Section'");
    $capabilities['registration_section'] = ($result && $result->num_rows > 0);
    
    return $capabilities;
}

$dbCapabilities = checkDatabaseCapabilities($conn);

// ─────────────────────────────────────────────────────────────────────────────
// 3) FETCH LECTURER INFO
// ─────────────────────────────────────────────────────────────────────────────
$lecturer_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT Name, UserID, profile_pic FROM lecturer WHERE LectID = ?");
$stmt->bind_param("i", $lecturer_id);
$stmt->execute();
$res = $stmt->get_result();
$lecturer = $res->fetch_assoc();
$lecturer_name = $lecturer['Name'] ?? 'Lecturer Name';
$lecturer_userid = $lecturer['UserID'] ?? 'N/A';

// Handle profile picture
$profile_pic = $lecturer['profile_pic'] ?? null;
$photo_url = '../assets/images/user1.png'; // default
if ($profile_pic && file_exists("../uploads/profile_pics/" . $profile_pic)) {
    $photo_url = "../uploads/profile_pics/" . $profile_pic;
}
$stmt->close();

// ─────────────────────────────────────────────────────────────────────────────
// 4) FETCH LECTURER'S COURSES FOR DROPDOWN
// ─────────────────────────────────────────────────────────────────────────────
$courses = [];

if ($dbCapabilities['course_assignments']) {
    // New system: fetch course assignments with sections
    $courseStmt = $conn->prepare("
        SELECT
            ca.AssignmentID,
            c.CourseID,
            " . ($dbCapabilities['course_code'] ? "c.Course_Code," : "c.CourseID as Course_Code,") . "
            c.Course_Name AS CourseName,
            ca.Section,
            ca.Status
        FROM course_assignments ca
        JOIN course c ON ca.CourseID = c.CourseID
        WHERE ca.LectID = ? AND ca.Status = 'Active'
        " . ($dbCapabilities['course_status'] ? "AND c.Status = 'Active'" : "") . "
        ORDER BY c.Course_Name ASC, ca.Section ASC
    ");
    $courseStmt->bind_param("i", $lecturer_id);
    $courseStmt->execute();
    $courseResult = $courseStmt->get_result();
    
    while ($row = $courseResult->fetch_assoc()) {
        $courses[] = [
            'AssignmentID' => $row['AssignmentID'],
            'CourseID' => $row['CourseID'],
            'Course_Code' => $row['Course_Code'],
            'CourseName' => $row['CourseName'],
            'Section' => $row['Section'],
            'DisplayName' => $row['Course_Code'] . ' - ' . $row['CourseName'] . ' (' . $row['Section'] . ')'
        ];
    }
    $courseStmt->close();
} else {
    // Legacy system: direct course assignments
    $courseStmt = $conn->prepare("
        SELECT
            c.CourseID,
            " . ($dbCapabilities['course_code'] ? "c.Course_Code," : "c.CourseID as Course_Code,") . "
            c.Course_Name AS CourseName
        FROM course c
        WHERE c.LectID = ?
        " . ($dbCapabilities['course_status'] ? "AND c.Status = 'Active'" : "") . "
        ORDER BY c.Course_Name ASC
    ");
    $courseStmt->bind_param("i", $lecturer_id);
    $courseStmt->execute();
    $courseResult = $courseStmt->get_result();
    
    while ($row = $courseResult->fetch_assoc()) {
        $courses[] = [
            'CourseID' => $row['CourseID'],
            'Course_Code' => $row['Course_Code'],
            'CourseName' => $row['CourseName'],
            'DisplayName' => $row['Course_Code'] . ' - ' . $row['CourseName']
        ];
    }
    $courseStmt->close();
}

// ─────────────────────────────────────────────────────────────────────────────
// 5) HANDLE COURSE SELECTION AND FETCH STUDENTS
// ─────────────────────────────────────────────────────────────────────────────
$selectedCourse = null;
$students = [];
$selectedAssignmentId = isset($_GET['assignment_id']) ? intval($_GET['assignment_id']) : 0;
$selectedCourseId = isset($_GET['course_id']) ? intval($_GET['course_id']) : 0;

if ($selectedAssignmentId > 0 && $dbCapabilities['course_assignments']) {
    // Find selected course by assignment ID
    foreach ($courses as $course) {
        if ($course['AssignmentID'] == $selectedAssignmentId) {
            $selectedCourse = $course;
            break;
        }
    }
    
    if ($selectedCourse) {
        // Fetch students for this course section
        if ($dbCapabilities['registration_section']) {
            $studentStmt = $conn->prepare("
                SELECT
                    s.StudentID,
                    s.Name,
                    s.UserID as MatricNumber,
                    s.ID_Number as ICNumber
                FROM student s
                JOIN course_registration cr ON s.StudentID = cr.StudentID
                WHERE cr.CourseID = ? AND cr.Section = ?
                ORDER BY s.Name ASC
            ");
            $studentStmt->bind_param("is", $selectedCourse['CourseID'], $selectedCourse['Section']);
        } else {
            $studentStmt = $conn->prepare("
                SELECT
                    s.StudentID,
                    s.Name,
                    s.UserID as MatricNumber,
                    s.ID_Number as ICNumber
                FROM student s
                JOIN course_registration cr ON s.StudentID = cr.StudentID
                WHERE cr.CourseID = ?
                ORDER BY s.Name ASC
            ");
            $studentStmt->bind_param("i", $selectedCourse['CourseID']);
        }
        
        $studentStmt->execute();
        $studentResult = $studentStmt->get_result();
        
        while ($row = $studentResult->fetch_assoc()) {
            $students[] = $row;
        }
        $studentStmt->close();
    }
} elseif ($selectedCourseId > 0) {
    // Find selected course by course ID (legacy system)
    foreach ($courses as $course) {
        if ($course['CourseID'] == $selectedCourseId) {
            $selectedCourse = $course;
            break;
        }
    }
    
    if ($selectedCourse) {
        // Fetch students for this course
        $studentStmt = $conn->prepare("
            SELECT
                s.StudentID,
                s.Name,
                s.UserID as MatricNumber,
                s.ID_Number as ICNumber
            FROM student s
            JOIN course_registration cr ON s.StudentID = cr.StudentID
            WHERE cr.CourseID = ?
            ORDER BY s.Name ASC
        ");
        $studentStmt->bind_param("i", $selectedCourse['CourseID']);
        $studentStmt->execute();
        $studentResult = $studentStmt->get_result();
        
        while ($row = $studentResult->fetch_assoc()) {
            $students[] = $row;
        }
        $studentStmt->close();
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Course Details - Lecturer Dashboard</title>

  <!-- FontAwesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Base + Modular CSS -->
  <link rel="stylesheet" href="../dashboard/css/base-styles.css">
  <link rel="stylesheet" href="../dashboard/css/lecturer-header.css">
  <link rel="stylesheet" href="../dashboard/css/lecturer-sidebar.css"> 
  <link rel="stylesheet" href="../dashboard/css/lecturer-footer.css">
  <link rel="stylesheet" href="../dashboard/css/lecturer-dashboard-styles.css">

</head>
<body>
  <!-- ─────────── HEADER ─────────── -->
  <div class="header">
    <div class="header-left">
      <img src="../assets/images/logo-uthm2.png" alt="UTHM Logo" class="logo">
    </div>
    <div class="header-right">
      <span class="user-id"><?= htmlspecialchars($lecturer_userid) ?></span>
    </div>
  </div>

  <!-- ─────────── MAIN CONTAINER (SIDEBAR + CONTENT) ─────────── -->
  <div class="container">
    <!-- ─────────── SIDEBAR ─────────── -->
    <div class="sidebar">
      <div class="profile">
        <img src="<?= $photo_url ?>" alt="Profile" class="profile-pic">
        <p class="profile-name"><?= htmlspecialchars($lecturer_name) ?></p>
        <p class="profile-id"><?= htmlspecialchars($lecturer_userid) ?></p>
      </div>
      <ul class="menu">
        <li><a href="../dashboard/lecturer.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
        <li><a href="profile_lecturer.php"><i class="fas fa-user"></i> Profile</a></li>
        <li><a href="qr_generate.php"><i class="fas fa-qrcode"></i> Generate QR Code</a></li>
        <li><a href="../dashboard/attendance_report.php"><i class="fas fa-book"></i> Attendance Report</a></li>
        <li><a href="courses_details.php" class="active"><i class="fas fa-graduation-cap"></i> Course Details</a></li>
        <li><a href="../dashboard/blockchain_records.php"><i class="fas fa-link"></i> Blockchain Report</a></li>
        <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
      </ul>
    </div>

    <!-- ─────────── MAIN CONTENT ─────────── -->
    <div class="main-content">
      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">Course Details</h1>
        <p class="page-subtitle">View comprehensive information about your assigned courses and enrolled students.</p>
      </div>

      <!-- ─────────────────── COURSE SELECTION ─────────────────── -->
      <div class="section-header">
        <h2 class="section-title"><i class="fas fa-graduation-cap"></i> Select Course</h2>
        <p class="section-subtitle">Choose a course to view detailed information and student enrollment.</p>
      </div>

      <div class="filter-section">
        <form method="GET" action="courses_details.php" class="filter-form">
          <div class="filter-controls">
            <!-- Course Selection -->
            <div class="form-group">
              <label for="course_select">Course</label>
              <select name="<?= $dbCapabilities['course_assignments'] ? 'assignment_id' : 'course_id' ?>" id="course_select" class="form-input" onchange="this.form.submit()">
                <option value="0">Select a course...</option>
                <?php foreach ($courses as $course): ?>
                  <?php
                    if ($dbCapabilities['course_assignments']) {
                        $value = $course['AssignmentID'];
                        $isSelected = ($selectedAssignmentId === $value) ? 'selected' : '';
                    } else {
                        $value = $course['CourseID'];
                        $isSelected = ($selectedCourseId === $value) ? 'selected' : '';
                    }
                  ?>
                  <option value="<?= $value ?>" <?= $isSelected ?>><?= htmlspecialchars($course['DisplayName']) ?></option>
                <?php endforeach; ?>
              </select>
            </div>
          </div>
        </form>
      </div>

      <?php if ($selectedCourse): ?>
        <!-- ─────────────────── COURSE INFORMATION ─────────────────── -->
        <div class="section-header">
          <h2 class="section-title"><i class="fas fa-info-circle"></i> Course Information</h2>
          <p class="section-subtitle">Detailed information about the selected course.</p>
        </div>

        <div class="course-info-container">
          <div class="info-grid">
            <div class="info-item">
              <label class="info-label">Course Code</label>
              <span class="info-value course-code-badge"><?= htmlspecialchars($selectedCourse['Course_Code']) ?></span>
            </div>
            <div class="info-item">
              <label class="info-label">Course Name</label>
              <span class="info-value"><?= htmlspecialchars($selectedCourse['CourseName']) ?></span>
            </div>
            <?php if ($dbCapabilities['course_assignments']): ?>
            <div class="info-item">
              <label class="info-label">Section</label>
              <span class="info-value section-badge"><?= htmlspecialchars($selectedCourse['Section']) ?></span>
            </div>
            <?php endif; ?>
            <div class="info-item">
              <label class="info-label">Lecturer</label>
              <span class="info-value"><?= htmlspecialchars($lecturer_name) ?></span>
            </div>
            <div class="info-item">
              <label class="info-label">Total Students</label>
              <span class="info-value student-count">
                <i class="fas fa-users"></i>
                <?= count($students) ?>
              </span>
            </div>
          </div>
        </div>

        <!-- ─────────────────── ENROLLED STUDENTS ─────────────────── -->
        <div class="section-header">
          <h2 class="section-title"><i class="fas fa-users"></i> Enrolled Students</h2>
          <p class="section-subtitle">List of all students registered for this course<?= $dbCapabilities['course_assignments'] ? ' section' : '' ?>.</p>
        </div>

        <div class="students-container">
          <?php if (empty($students)): ?>
            <div class="no-data-msg">
              <i class="fas fa-user-slash"></i>
              No students are currently enrolled in this course<?= $dbCapabilities['course_assignments'] ? ' section' : '' ?>.
            </div>
          <?php else: ?>
            <div class="table-container">
              <table class="students-table">
                <thead>
                  <tr>
                    <th>No.</th>
                    <th>Student Name</th>
                    <th>Matric Number</th>
                    <th>IC Number</th>
                  </tr>
                </thead>
                <tbody>
                  <?php foreach ($students as $index => $student): ?>
                    <tr>
                      <td data-label="No."><?= $index + 1 ?></td>
                      <td data-label="Student Name">
                        <i class="fas fa-user" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                        <strong><?= htmlspecialchars($student['Name']) ?></strong>
                      </td>
                      <td data-label="Matric Number">
                        <span class="matric-badge"><?= htmlspecialchars($student['MatricNumber']) ?></span>
                      </td>
                      <td data-label="IC Number"><?= htmlspecialchars($student['ICNumber']) ?></td>
                    </tr>
                  <?php endforeach; ?>
                </tbody>
              </table>
            </div>
          <?php endif; ?>
        </div>
      <?php endif; ?>

    </div> <!-- end .main-content -->
  </div>   <!-- end .container -->

  <!-- ─────────── FOOTER ─────────── -->
  <footer>
    <p>UNIVERSITI TUN HUSSEIN ONN MALAYSIA</p>
  </footer>

</body>
</html>
