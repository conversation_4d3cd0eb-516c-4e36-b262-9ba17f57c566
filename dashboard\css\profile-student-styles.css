/* ===================================================================
   Profile-Student Styles
   Supplements student-dashboard-enhanced.css
   ================================================================== */

/* ─── LAYOUT FOR TWO-COLUMN CARDS ─── */
.card-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px,1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}
.card-col {
  width: 100%;
}

/* ─── DETAILS CARD ─── */
.detail-card {
  background: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  box-shadow: var(--shadow-sm);
  padding: 1.5rem;
}
.detail-card .text-center {
  text-align: center;
  margin-bottom: 1rem;
}
.profile-photo {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 3px solid var(--primary-light);
  object-fit: cover;
  margin-bottom: 0.75rem;
}
.detail-row {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--gray-100);
}
.detail-row:last-child {
  border-bottom: none;
}
.detail-label {
  font-weight: 600;
  color: var(--gray-600);
}
.detail-value {
  color: var(--gray-800);
  text-align: right;
}

/* ─── EDIT & PASSWORD CARDS ─── */
.edit-card,
.password-card {
  background: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  box-shadow: var(--shadow-sm);
  padding: 1.5rem;
}
.card-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--gray-900);
}
.card-title i {
  color: var(--primary);
}

/* ─── FORM ELEMENTS ─── */
.form-row {
  margin-bottom: 1rem;
}
.form-row label {
  display: block;
  margin-bottom: 0.25rem;
  color: var(--gray-600);
  font-weight: 500;
}
.input-icon {
  position: relative;
}
.input-icon i {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
}
.input-icon input {
  width: 100%;
  padding: 0.5rem 1rem;
  padding-left: 2.5rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius);
  background: var(--gray-50);
  transition: border-color 0.2s;
}
.input-icon input:focus {
  outline: none;
  border-color: var(--primary);
  background: var(--white);
}

/* ─── BUTTONS ─── */
.btn-submit {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--primary);
  color: var(--white);
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s, transform 0.2s;
}
.btn-submit i {
  font-size: 1rem;
}
.btn-submit:hover {
  background: var(--primary-light);
  transform: translateY(-2px);
}

/* ─── POPUP MESSAGE ─── */
.popup-message {
  position: fixed;
  top: 2rem;
  left: 50%;
  transform: translateX(-50%);
  background: #DCFCE7;
  color: #065F46;
  padding: 0.75rem 1.25rem;
  border-radius: var(--radius);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  gap: 1rem;
  z-index: 1000;
}
.popup-message .close-btn {
  margin-left: auto;
  background: none;
  border: none;
  font-size: 1.25rem;
  color: inherit;
  cursor: pointer;
}

/* ─── RESPONSIVE ─── */
@media (max-width: 768px) {
  .card-row {
    grid-template-columns: 1fr;
  }
}
/* ─────────── POPUP MESSAGE VARIANTS ─────────── */
.popup-message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}
.popup-message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* ─────────── INLINE FORM ERROR ─────────── */
.form-error {
  color: var(--danger);
  font-size: var(--text-sm);
  margin-top: var(--space-xs);
}
