<?php
session_start();
require '../config/config.php';
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header("Location: ../index.php");
    exit();
}
$student_id = $_SESSION['user_id'];
$error      = '';
$success    = '';

// ── PROFILE UPDATE ──
if (isset($_POST['update_profile'])) {
    $phone   = trim($_POST['phone']);
    $address = trim($_POST['address']);

    // build dynamic update for Address & Phone
    $fields = "PhoneNumber = ?, Address = ?";
    $types  = "ss";
    $params = [&$phone, &$address];

    // photo?
    if (!empty($_FILES['photo']['name']) && $_FILES['photo']['error']===UPLOAD_ERR_OK) {
        $ext     = strtolower(pathinfo($_FILES['photo']['name'], PATHINFO_EXTENSION));
        $allowed = ['jpg','jpeg','png'];
        if (in_array($ext, $allowed)) {
            $newName = "stu_{$student_id}_" . time() . ".{$ext}";
            $dest    = __DIR__ . "/../uploads/{$newName}";
            if (move_uploaded_file($_FILES['photo']['tmp_name'], $dest)) {
                $fields   .= ", Photo = ?";
                $types    .= "s";
                $params[] = &$newName;
            } else {
                $error = "Could not save photo.";
            }
        } else {
            $error = "Photo must be JPG/PNG.";
        }
    }

    if (!$error) {
        $types   .= "i";
        $params[] = &$student_id;
        $sql      = "UPDATE student SET {$fields} WHERE StudentID = ?";
        $stmt     = $conn->prepare($sql);
        $stmt->bind_param($types, ...$params);
        $stmt->execute()
            ? $success = "Profile updated."
            : $error   = "Update failed.";
        $stmt->close();
    }
}

// ── PASSWORD CHANGE ──
if (isset($_POST['update_password'])) {
    $cur = $_POST['current_password'];
    $new = $_POST['new_password'];
    $cfm = $_POST['confirm_password'];

    // fetch current hash
    $q = $conn->prepare("SELECT Password FROM student WHERE StudentID=?");
    $q->bind_param("i",$student_id);
    $q->execute();
    $hash = $q->get_result()->fetch_assoc()['Password'];
    $q->close();

    if (!password_verify($cur,$hash)) {
        $error = "Current password incorrect.";
    } elseif ($new!==$cfm || $new==='') {
        $error = "New passwords must match.";
    } else {
         $hash = password_hash($new, PASSWORD_DEFAULT);
        $stmt = $conn->prepare("UPDATE student SET Password = ? WHERE StudentID = ?");
        $stmt->bind_param("si", $hash, $student_id);
        if ($stmt->execute()) {
            $success = "Password changed successfully.";
        } else {
            $error = "Failed to update password.";
        }
        $stmt->close();
    }
}

// ── FETCH LATEST STUDENT INFO ──
$s = $conn->prepare("
    SELECT Name,UserID,Email,PhoneNumber,ID_Type,ID_Number,Gender,Faculty,Address,Photo
    FROM student WHERE StudentID=?
");
$s->bind_param("i",$student_id);
$s->execute();
$student = $s->get_result()->fetch_assoc();
$s->close();

// photo URL fallback
$photo_url = $student['Photo']
  ? "../uploads/".rawurlencode($student['Photo'])
  : "../assets/images/user1.png";
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <title>My Profile • UTHM Attendance</title>

 <!-- FontAwesome for icons -->
  <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Google Font (Inter) -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap"
        rel="stylesheet">

  <!-- COMMON STYLES (header/sidebar/footer) -->
  <link rel="stylesheet" href="../dashboard/css/base-styles.css">
  <link rel="stylesheet" href="../dashboard/css/lecturer-header.css">
  <link rel="stylesheet" href="../dashboard/css/lecturer-sidebar.css">
  <link rel="stylesheet" href="../dashboard/css/lecturer-footer.css">

  <!-- DASHBOARD PALETTE + UTILS -->
  <link rel="stylesheet" href="../dashboard/css/student-dashboard-enhanced.css">

  <!-- PROFILE-PAGE SPECIFIC -->
  <link rel="stylesheet" href="../dashboard/css/profile-student-styles.css">

  <!-- FontAwesome -->
  <link rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    integrity="sha512-p7Qd4cD57rpm9N4UCz5Z7rv2b/XiT3WY46zlwF6nN+qxG1MWV7P+WsWF9NutN8HuJ/sKDbmkrq+j1yZzQ+rt6g=="
    crossorigin="anonymous" referrerpolicy="no-referrer" />
</head>
<body>

  <!-- HEADER -->
  <div class="header">
    <div class="header-left">
      <img src="../assets/images/logo-uthm2.png" class="logo" alt="UTHM Logo">
    </div>
    <div class="header-right">
      <a href="../modules/qr_scan.php" class="qr-button"><i class="fas fa-qrcode"></i> Scan QR</a>
      <span class="user-id"><?= htmlspecialchars($student['UserID']) ?></span>
    </div>
  </div>

  <div class="container">
    <!-- SIDEBAR -->
    <div class="sidebar">
      <div class="profile">
        <img src="<?= $photo_url ?>" class="profile-pic" alt="Photo">
        <p class="profile-name"><?= htmlspecialchars($student['Name']) ?></p>
        <p class="profile-id"><?= htmlspecialchars($student['UserID']) ?></p>
      </div>
      <ul class="menu">
        <li><a href="../dashboard/student.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
        <li><a href="../modules/profile_student.php" class="active"><i class="fas fa-user"></i> Profile</a></li>
        <li><a href="../modules/register_courses.php"><i class="fas fa-edit"></i> Register Courses</a></li>
        <li><a href="../modules/report.php"><i class="fas fa-book"></i> Attendance Details</a></li>
        <li><a href="../dashboard/student_blockchain_records.php"><i class="fas fa-link"></i> Blockchain Records</a></li>
        <li><a href="../dashboard/student_transaction_lookup.php"><i class="fas fa-search"></i> Transaction Lookup</a></li>
        <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
      </ul>
    </div>

    <!-- MAIN CONTENT -->
    <main class="main-content">
      <div class="page-header">
        <div class="header-content">
          <div class="header-text">
            <h1>My Profile</h1>
            <p>View and update your personal details below.</p>
          </div>
          <div class="header-date">
            <i class="fas fa-calendar-alt"></i>
            <span><?= date('l, F j, Y') ?></span>
          </div>
        </div>
      </div>

       <!-- POPUP MESSAGE -->
      <?php if ($error || $success): ?>
        <div class="popup-message <?= $error ? 'error' : 'success' ?>" id="popup-message">
          <?= htmlspecialchars($error ?: $success) ?>
          <button class="close-btn" onclick="closePopup()">&times;</button>
        </div>
      <?php endif; ?>

      <div class="card-row">
        <!-- DETAILS CARD -->
        <div class="card-col">
          <div class="detail-card">
            <div class="text-center">
              <img src="<?= $photo_url ?>" class="profile-photo" alt="Photo">
              <h3><?= htmlspecialchars($student['Name']) ?></h3>
              <p>User ID: <?= htmlspecialchars($student['UserID']) ?></p>
            </div>
            <hr>
            <?php foreach ([
              'Email'     => $student['Email'],
              'Phone'     => $student['PhoneNumber'],
              'ID Type'   => $student['ID_Type'],
              'ID Number' => $student['ID_Number'],
              'Gender'    => $student['Gender'],
              'Faculty'   => $student['Faculty'],
              'Address'   => $student['Address']?:'—',
            ] as $label=>$val): ?>
              <div class="detail-row">
                <div class="detail-label"><?= $label ?></div>
                <div class="detail-value"><?= nl2br(htmlspecialchars($val)) ?></div>
              </div>
            <?php endforeach; ?>
          </div>
        </div>

        <!-- EDIT FORMS -->
        <div class="card-col">
          <!-- Profile Update -->
          <div class="edit-card">
            <h2 class="card-title"><i class="fas fa-user-edit"></i> Update Profile</h2>
            <form method="POST" enctype="multipart/form-data">
              <input type="hidden" name="update_profile">

              <div class="form-row">
                <label for="phone">Phone Number</label>
                <div class="input-icon">
                  <i class="fas fa-phone"></i>
                  <input type="tel" id="phone" name="phone"
                    value="<?= htmlspecialchars($student['PhoneNumber']) ?>"
                    placeholder="012-3456789" required>
                </div>
              </div>

              <div class="form-row">
                <label for="address">Address</label>
                <div class="input-icon">
                  <i class="fas fa-home"></i>
                  <input type="text" id="address" name="address"
                    value="<?= htmlspecialchars($student['Address']) ?>"
                    placeholder="Your mailing address">
                </div>
              </div>

              <div class="form-row">
                <label for="photo">Profile Photo</label>
                <input type="file" id="photo" name="photo" accept=".jpg,.jpeg,.png">
              </div>

              <div class="form-row">
                <button type="submit" class="btn-submit">
                  <i class="fas fa-save"></i> Save Changes
                </button>
              </div>
            </form>
          </div>

          <!-- Change Password Card -->
          <div class="password-card">
            <h2 class="card-title"><i class="fas fa-key"></i> Change Password</h2>
            <!-- Add id to the form -->
            <form method="POST" id="passwordForm">
              <div class="form-row">
                <label for="current_password">Current Password</label>
                <div class="input-icon">
                  <i class="fas fa-lock"></i>
                  <input type="password" name="current_password" id="current_password" required>
                </div>
              </div>
              <div class="form-row">
                <label for="new_password">New Password</label>
                <div class="input-icon">
                  <i class="fas fa-lock"></i>
                  <input type="password" name="new_password" id="new_password" required>
                </div>
              </div>
              <div class="form-row">
                <label for="confirm_password">Confirm New Password</label>
                <div class="input-icon">
                  <i class="fas fa-lock"></i>
                  <input type="password" name="confirm_password" id="confirm_password" required>
                </div>
              </div>
              <div class="form-row">
                <button type="submit" name="update_password" class="btn-submit">
                  <i class="fas fa-check"></i> Update Password
                </button>
              </div>
            </form>
          </div>
        </div>
      </div><!-- /.card-row -->
    </div><!-- /.main-content -->
  </div><!-- /.container -->

  <footer class="footer">
    <p>UNIVERSITI TUN HUSSEIN ONN MALAYSIA</p>
  </footer>

   <script>
    // Close popup after 4s or on &times;
    function closePopup() {
      const pop = document.getElementById('popup-message');
      if (pop) pop.style.display = 'none';
    }
    document.addEventListener('DOMContentLoaded', () => {
      const pop = document.getElementById('popup-message');
      if (pop) setTimeout(closePopup, 4000);

      // Password‐strength & match validation
      const form = document.getElementById('passwordForm');
      form.addEventListener('submit', e => {
        // remove old inline errors
        document.querySelectorAll('.form-error').forEach(el=>el.remove());

        const newPw     = document.getElementById('new_password').value;
        const confirmPw = document.getElementById('confirm_password').value;
        // at least 8 chars, uppercase, number & special
        const pwRule = /^(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*]).{8,}$/;

        let valid = true;
        if (!pwRule.test(newPw)) {
          showError('new_password',
            'Must be ≥8 chars, include uppercase, number & symbol.'
          );
          valid = false;
        }
        if (newPw !== confirmPw) {
          showError('confirm_password','Passwords do not match.');
          valid = false;
        }
        if (!valid) e.preventDefault();
      });

      function showError(inputId, msg) {
        const input = document.getElementById(inputId);
        const div = document.createElement('div');
        div.className = 'form-error';
        div.textContent = msg;
        input.parentNode.appendChild(div);
      }
    });
  </script>
</body>
</html>
