<?php
// ─────────────────────────────────────────────────────────────────────────────
//  modules/qr_generate.php
//  - Lecturer can select a course, class details, and generate a dynamic QR code.
//  - The QR image updates every 10 seconds via AJAX (update_qr_code.php).
//  - Styled consistently with attendance_report.php and lecturer.php.
// ─────────────────────────────────────────────────────────────────────────────

session_start();
require '../config/config.php';       // Database connection
require '../phpqrcode/qrlib.php';      // phpqrcode library

// ─────────────────────────────────────────────────────────────────────────────
// 1) SECURITY CHECK: Only 'lecturer' role can access this page
// ─────────────────────────────────────────────────────────────────────────────
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'lecturer') {
    header("Location: ../index.php");
    exit();
}

// ─────────────────────────────────────────────────────────────────────────────
// 2) FETCH LECTURER INFO (for header/sidebar display)
// ─────────────────────────────────────────────────────────────────────────────
$lecturer_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT Name, UserID, profile_pic FROM lecturer WHERE LectID = ?");
$stmt->bind_param("i", $lecturer_id);
$stmt->execute();
$res = $stmt->get_result();
$lecturer = $res->fetch_assoc();
$lecturer_name   = $lecturer['Name']   ?? 'Lecturer Name';
$lecturer_userid = $lecturer['UserID'] ?? 'N/A';
$stmt->close();

// ─────────────────────────────────────────────────────────────────────────────
// 3) DATABASE CAPABILITIES DETECTION
// ─────────────────────────────────────────────────────────────────────────────
function checkDatabaseCapabilities($conn) {
    $capabilities = [
        'course_code' => false,
        'course_status' => false,
        'course_assignments' => false,
        'registration_section' => false
    ];

    // Check if Course_Code column exists
    $checkCodeColumn = "SHOW COLUMNS FROM course LIKE 'Course_Code'";
    $result = $conn->query($checkCodeColumn);
    $capabilities['course_code'] = $result && $result->num_rows > 0;

    // Check if Status column exists
    $checkStatusColumn = "SHOW COLUMNS FROM course LIKE 'Status'";
    $result = $conn->query($checkStatusColumn);
    $capabilities['course_status'] = $result && $result->num_rows > 0;

    // Check if course_assignments table exists
    $checkAssignmentsTable = "SHOW TABLES LIKE 'course_assignments'";
    $result = $conn->query($checkAssignmentsTable);
    $capabilities['course_assignments'] = $result && $result->num_rows > 0;

    // Check if Section column exists in course_registration
    $checkSectionColumn = "SHOW COLUMNS FROM course_registration LIKE 'Section'";
    $result = $conn->query($checkSectionColumn);
    $capabilities['registration_section'] = $result && $result->num_rows > 0;

    return $capabilities;
}

$dbCapabilities = checkDatabaseCapabilities($conn);

// ─────────────────────────────────────────────────────────────────────────────
// 4) FETCH COURSES AND SECTIONS FOR SEPARATE DROPDOWNS
// ─────────────────────────────────────────────────────────────────────────────
$courses = [];
$courseSections = []; // Array to store course-section mappings

if ($dbCapabilities['course_assignments']) {
    // New system: fetch unique courses and their sections separately

    // First, get unique courses
    $courseStmt = $conn->prepare("
        SELECT DISTINCT
            c.CourseID,
            " . ($dbCapabilities['course_code'] ? "c.Course_Code," : "c.CourseID as Course_Code,") . "
            c.Course_Name
        FROM course_assignments ca
        JOIN course c ON ca.CourseID = c.CourseID
        WHERE ca.LectID = ? AND ca.Status = 'Active'
        " . ($dbCapabilities['course_status'] ? "AND c.Status = 'Active'" : "") . "
        ORDER BY c.Course_Name ASC
    ");
    $courseStmt->bind_param("i", $lecturer_id);
    $courseStmt->execute();
    $courseRes = $courseStmt->get_result();
    while ($row = $courseRes->fetch_assoc()) {
        $courses[] = [
            'CourseID'    => $row['CourseID'],
            'Course_Code' => $row['Course_Code'],
            'CourseName'  => $row['Course_Name'],
            'DisplayName' => $row['Course_Code'] . ' - ' . $row['Course_Name']
        ];
    }
    $courseStmt->close();

    // Then, get all course-section assignments for JavaScript
    $sectionStmt = $conn->prepare("
        SELECT
            ca.AssignmentID,
            c.CourseID,
            ca.Section
        FROM course_assignments ca
        JOIN course c ON ca.CourseID = c.CourseID
        WHERE ca.LectID = ? AND ca.Status = 'Active'
        " . ($dbCapabilities['course_status'] ? "AND c.Status = 'Active'" : "") . "
        ORDER BY c.CourseID ASC, ca.Section ASC
    ");
    $sectionStmt->bind_param("i", $lecturer_id);
    $sectionStmt->execute();
    $sectionRes = $sectionStmt->get_result();
    while ($row = $sectionRes->fetch_assoc()) {
        $courseId = $row['CourseID'];
        if (!isset($courseSections[$courseId])) {
            $courseSections[$courseId] = [];
        }
        $courseSections[$courseId][] = [
            'AssignmentID' => $row['AssignmentID'],
            'Section'      => $row['Section']
        ];
    }
    $sectionStmt->close();
} else {
    // Legacy system: fetch courses directly (no sections)
    $courseStmt = $conn->prepare("
        SELECT
            CourseID,
            " . ($dbCapabilities['course_code'] ? "Course_Code," : "CourseID as Course_Code,") . "
            Course_Name
        FROM course
        WHERE LectID = ?
        " . ($dbCapabilities['course_status'] ? "AND Status = 'Active'" : "") . "
        ORDER BY Course_Name
    ");
    $courseStmt->bind_param("i", $lecturer_id);
    $courseStmt->execute();
    $courseRes = $courseStmt->get_result();
    while ($row = $courseRes->fetch_assoc()) {
        $courses[] = [
            'CourseID'    => $row['CourseID'],
            'Course_Code' => $row['Course_Code'],
            'CourseName'  => $row['Course_Name'],
            'DisplayName' => $row['Course_Code'] . ' - ' . $row['Course_Name']
        ];
        // For legacy system, create a default section entry
        $courseSections[$row['CourseID']] = [
            [
                'AssignmentID' => null,
                'Section'      => 'Default'
            ]
        ];
    }
    $courseStmt->close();
}
// fix broken pic URL: look in assets/images/
$photo_url = !empty($lecturer['profile_pic'])
    ? "../assets/images/".rawurlencode($lecturer['profile_pic'])
    : "../assets/images/user1.png";

// ─────────────────────────────────────────────────────────────────────────────
// 4) HANDLE FORM SUBMISSION: Generate initial QR code and set up session
// ─────────────────────────────────────────────────────────────────────────────
$message    = '';
$file_path  = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['generate_qr'])) {
    // Validate inputs
    $course_id     = isset($_POST['course_id']) ? intval($_POST['course_id']) : 0;
    $section_value = $_POST['section'] ?? '';
    $assignment_id = isset($_POST['assignment_id']) ? intval($_POST['assignment_id']) : 0;
    $class_type    = $_POST['class_type'] ?? '';

    $class_date    = $_POST['class_date'] ?? '';
    $start_time    = $_POST['start_time'] ?? '';
    $end_time      = $_POST['end_time'] ?? '';

    $errors = [];
    $selectedCourse = null;
    $selectedSection = '';

    // 4a) COURSE VALIDATION
    if (empty($course_id)) {
        $errors[] = "Please select a course.";
    } else {
        // Ensure course belongs to this lecturer
        $validCourse = false;
        foreach ($courses as $c) {
            if ($c['CourseID'] === $course_id) {
                $selectedCourse = $c;
                $validCourse = true;
                break;
            }
        }
        if (!$validCourse) {
            $errors[] = "Invalid course selected.";
        }
    }

    // 4b) SECTION VALIDATION
    if ($dbCapabilities['course_assignments']) {
        // New system: validate section selection
        if (empty($section_value)) {
            $errors[] = "Please select a section.";
        } else {
            // Find the assignment ID for the selected course and section
            if (isset($courseSections[$course_id])) {
                foreach ($courseSections[$course_id] as $sectionData) {
                    if ($sectionData['Section'] === $section_value) {
                        $assignment_id = $sectionData['AssignmentID'];
                        $selectedSection = $section_value;
                        break;
                    }
                }
                if (!$assignment_id) {
                    $errors[] = "Invalid section selected for this course.";
                }
            } else {
                $errors[] = "No sections available for the selected course.";
            }
        }
    } else {
        // Legacy system: use default section
        $selectedSection = 'Default';
    }

    // 4c) CLASS TYPE VALIDATION
    if (!in_array($class_type, ['Lecture', 'Lab'], true)) {
        $errors[] = "Please select a valid class type.";
    }

    // 4d) DATE VALIDATION
    if (empty($class_date)) {
        $errors[] = "Please pick a class date.";
    } else {
        $today = date('Y-m-d');
        if ($class_date < $today) {
            $errors[] = "Class date cannot be in the past.";
        }
    }

    // 4d) TIME VALIDATION (whole hours)
    $validHours = [];
    for ($h = 7; $h <= 18; $h++) {
        $validHours[] = sprintf("%02d:00", $h);
    }
    if (!in_array($start_time, $validHours, true)) {
        $errors[] = "Please select a valid start time.";
    }
    if (!in_array($end_time, $validHours, true)) {
        $errors[] = "Please select a valid end time.";
    }
    if (strtotime($end_time) <= strtotime($start_time)) {
        $errors[] = "End time must be later than start time.";
    }

    if (empty($errors)) {
        // Store active attendance details in session
        $_SESSION['active_attendance'] = [
            'course_id'     => $course_id,
            'lecturer_id'   => $lecturer_id,
            'assignment_id' => $dbCapabilities['course_assignments'] ? $assignment_id : null,
            'section'       => $selectedSection,
            'class_type'    => $class_type,
            'class_date'    => $class_date,
            'start_time'    => $start_time,
            'end_time'      => $end_time,
            'created_at'    => time()
        ];

        // Enhanced QR data: encode all info + assignment/section + random + expiry
        $token = bin2hex(random_bytes(8));
        $_SESSION['qr_token'] = $token;
        $expires_at = time() + 10; // 10 seconds from now

        // Build enhanced QR payload with assignment and section information
        $qr_data = [
            'course_id'   => $course_id,
            'lecturer_id' => $lecturer_id,
            'token'       => $token,
            'expires_at'  => $expires_at
        ];

        // Add assignment and section data for enhanced system
        if ($dbCapabilities['course_assignments'] && $assignment_id > 0) {
            $qr_data['assignment_id'] = $assignment_id;
            $qr_data['section'] = $selectedSection;
        }

        $qr_payload = http_build_query($qr_data);
        $file_path = '../uploads/qr_attendance.png';

        // Try to generate QR code with fallback support
        try {
            // Check if GD extension is available
            if (extension_loaded('gd') && function_exists('imagecreate')) {
                // Use original QR code library
                QRcode::png($qr_payload, $file_path, QR_ECLEVEL_L, 8);
                $qr_generation_method = 'local';
            } else {
                // Use fallback method - create a placeholder or use online service
                $qr_generation_method = 'fallback';
                // We'll handle this in the display section
            }
        } catch (Exception $e) {
            // If QR generation fails, use fallback
            $qr_generation_method = 'fallback';
            error_log("QR Generation failed, using fallback: " . $e->getMessage());
        }

        $_SESSION['qr_payload'] = $qr_payload;
        $_SESSION['qr_expires_at'] = $expires_at;
        $_SESSION['qr_generation_method'] = $qr_generation_method;

        // DEBUG: Log QR generation data for troubleshooting
        error_log("QR Generation Debug - QR Data Array: " . print_r($qr_data, true));
        error_log("QR Generation Debug - QR Payload: " . $qr_payload);
        error_log("QR Generation Debug - DB Capabilities: " . print_r($dbCapabilities, true));
        error_log("QR Generation Debug - Assignment ID: " . $assignment_id . ", Selected Section: " . $selectedSection);

        $message = "QR Code generated successfully!";
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Generate QR Code</title>

  <!-- FontAwesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Base + Modular CSS -->
  <link rel="stylesheet" href="../dashboard/css/base-styles.css">
  <link rel="stylesheet" href="../dashboard/css/lecturer-header.css">
  <link rel="stylesheet" href="../dashboard/css/lecturer-sidebar.css">
  <link rel="stylesheet" href="../dashboard/css/lecturer-footer.css">
  <link rel="stylesheet" href="../dashboard/css/student-dashboard-enhanced.css"/>
  <link rel="stylesheet" href="../dashboard/css/qr-generate-styles.css">

  <script>
    // Course-Section mapping data from PHP
    const courseSections = <?= json_encode($courseSections) ?>;
    const hasAssignments = <?= json_encode($dbCapabilities['course_assignments']) ?>;

    // Function to update sections dropdown based on selected course
    function updateSections() {
      const courseSelect = document.getElementById('course_id');
      const sectionSelect = document.getElementById('section');
      const assignmentIdInput = document.getElementById('assignment_id');

      if (!hasAssignments || !sectionSelect) {
        return; // Skip if not using assignment system or section dropdown doesn't exist
      }

      const selectedCourseId = courseSelect.value;

      // Clear existing options
      sectionSelect.innerHTML = '<option value="">— Select Section —</option>';
      if (assignmentIdInput) {
        assignmentIdInput.value = '';
      }

      if (selectedCourseId && courseSections[selectedCourseId]) {
        // Populate sections for the selected course
        courseSections[selectedCourseId].forEach(function(sectionData) {
          const option = document.createElement('option');
          option.value = sectionData.Section;

          // Check if section already starts with "Section" to avoid duplication
          const displayText = sectionData.Section.startsWith('Section')
            ? sectionData.Section
            : 'Section ' + sectionData.Section;
          option.textContent = displayText;

          option.setAttribute('data-assignment-id', sectionData.AssignmentID);
          sectionSelect.appendChild(option);
        });

        // Enable the section dropdown
        sectionSelect.disabled = false;
      } else {
        // Disable the section dropdown if no course is selected
        sectionSelect.disabled = true;
      }
    }

    // Function to update assignment ID when section is selected
    function updateAssignmentId() {
      const sectionSelect = document.getElementById('section');
      const assignmentIdInput = document.getElementById('assignment_id');

      if (!hasAssignments || !sectionSelect || !assignmentIdInput) {
        return;
      }

      const selectedOption = sectionSelect.options[sectionSelect.selectedIndex];
      if (selectedOption && selectedOption.getAttribute('data-assignment-id')) {
        assignmentIdInput.value = selectedOption.getAttribute('data-assignment-id');
      } else {
        assignmentIdInput.value = '';
      }
    }

    // Initialize the form when page loads
    document.addEventListener('DOMContentLoaded', function() {
      // Set up event listeners
      const sectionSelect = document.getElementById('section');
      if (sectionSelect) {
        sectionSelect.addEventListener('change', updateAssignmentId);

        // Initialize sections if a course is already selected (for form validation errors)
        updateSections();

        // If there's a pre-selected section (from form validation), select it
        const preSelectedSection = '<?= isset($section_value) ? htmlspecialchars($section_value) : '' ?>';
        if (preSelectedSection) {
          sectionSelect.value = preSelectedSection;
          updateAssignmentId();
        }
      }
    });
  </script>
</head>
<body>
  <!-- ─────────── HEADER ─────────── -->
  <div class="header">
    <div class="header-left">
      <img src="../assets/images/logo-uthm2.png" alt="UTHM Logo" class="logo">
    </div>
    <div class="header-right">
      <span class="user-id"><?= htmlspecialchars($lecturer_userid) ?></span>
    </div>
  </div>

  <!-- ─────────── MAIN CONTAINER (SIDEBAR + CONTENT) ─────────── -->
  <div class="container">
    <!-- ─────────── SIDEBAR ─────────── -->
    <div class="sidebar">
      <div class="profile">
        <img src="<?=$photo_url?>" class="profile-pic" alt="Profile Photo"/>
        <p class="profile-name"><?=htmlspecialchars($lecturer['Name'])?></p>
        <p class="profile-id"><?=htmlspecialchars($lecturer['UserID'])?></p>
      </div>
      <ul class="menu">
        <li><a href="../dashboard/lecturer.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
        <li><a href="../modules/profile_lecturer.php"><i class="fas fa-user"></i> Profile</a></li>
        <li><a href="qr_generate.php" class="active"><i class="fas fa-qrcode"></i> Generate QR Code</a></li>
        <li><a href="../dashboard/attendance_report.php"><i class="fas fa-book"></i> Attendance Report</a></li>
        <li><a href="../modules/courses_details.php"><i class="fas fa-graduation-cap"></i> Course Details</a></li>
        <li><a href="../dashboard/blockchain_records.php"><i class="fas fa-link"></i> Blockchain Report</a></li>
        <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
      </ul>
    </div>

    <!-- ─────────── MAIN CONTENT ─────────── -->
    <div class="main-content-qr">
      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">Generate QR Code</h1>
        <p class="page-subtitle">Choose course, date &amp; time to start an attendance session.</p>
      </div>

      <!-- ─────────── DISPLAY FORM ERRORS ─────────── -->
      <?php if (!empty($errors)): ?>
        <div class="msg error">
          <ul>
            <?php foreach ($errors as $err): ?>
              <li><?= htmlspecialchars($err) ?></li>
            <?php endforeach; ?>
          </ul>
        </div>
      <?php endif; ?>

      <!-- ─────────── GENERATE QR FORM ─────────── -->
      <div class="form-container-qr">
        <form method="POST" class="generate-qr-form">
          <!-- Course Selection -->
          <div class="form-group">
            <label for="course_id">Course:</label>
            <select name="course_id" id="course_id" required onchange="updateSections()">
              <option value="">— Select Course —</option>
              <?php foreach ($courses as $c): ?>
                <option
                  value="<?= $c['CourseID'] ?>"
                  <?= (isset($course_id) && $course_id === $c['CourseID']) ? 'selected' : '' ?>>
                  <?= htmlspecialchars($c['DisplayName']) ?>
                </option>
              <?php endforeach; ?>
            </select>
          </div>

          <!-- Section Selection -->
          <?php if ($dbCapabilities['course_assignments']): ?>
          <div class="form-group">
            <label for="section">Section:</label>
            <select name="section" id="section" required>
              <option value="">— Select Section —</option>
            </select>
            <input type="hidden" name="assignment_id" id="assignment_id" value="">
          </div>
          <?php else: ?>
          <input type="hidden" name="section" value="Default">
          <?php endif; ?>

          <!-- Class Type -->
          <div class="form-group">
            <label for="class_type">Class Type:</label>
            <select name="class_type" id="class_type" required>
              <option value="">— Select Type —</option>
              <option value="Lecture" <?= (isset($class_type) && $class_type === 'Lecture') ? 'selected' : '' ?>>Lecture</option>
              <option value="Lab"     <?= (isset($class_type) && $class_type === 'Lab') ? 'selected' : '' ?>>Lab</option>
            </select>
          </div>



          <!-- Class Date -->
          <div class="form-group">
            <label for="class_date">Date:</label>
            <input 
              type="date" 
              id="class_date" 
              name="class_date" 
              value="<?= htmlspecialchars($class_date ?? '') ?>" 
              min="<?= date('Y-m-d') ?>" 
              required>
          </div>

          <!-- Start Time (Whole Hours) -->
          <div class="form-group">
            <label for="start_time">Start Time:</label>
            <select name="start_time" id="start_time" required>
              <option value="">— Select Start —</option>
              <?php
              for ($h = 7; $h <= 17; $h++):
                $label = sprintf("%02d:00", $h);
              ?>
                <option 
                  value="<?= $label ?>" 
                  <?= (isset($start_time) && $start_time === $label) ? 'selected' : '' ?>>
                  <?= $label ?>
                </option>
              <?php endfor; ?>
            </select>
          </div>

          <!-- End Time (Whole Hours) -->
          <div class="form-group">
            <label for="end_time">End Time:</label>
            <select name="end_time" id="end_time" required>
              <option value="">— Select End —</option>
              <?php
              for ($h = 7; $h <= 18; $h++):
                $label = sprintf("%02d:00", $h);
              ?>
                <option 
                  value="<?= $label ?>" 
                  <?= (isset($end_time) && $end_time === $label) ? 'selected' : '' ?>>
                  <?= $label ?>
                </option>
              <?php endfor; ?>
            </select>
          </div>

          <!-- Submit Button -->
          <div class="form-group form-actions">
            <button type="submit" name="generate_qr" class="btn-generate">
              <i class="fas fa-qrcode"></i> Generate QR Code
            </button>
          </div>
        </form>
      </div>

      <!-- ─────────── QR CODE MODAL (If Generated) ─────────── -->
      <?php if (!empty($message)): ?>
        <div id="qr-modal" class="modal">
          <div class="modal-content">
            <h2 class="qr-title"><i class="fas fa-check-circle"></i> QR Code generated successfully!</h2>
            <p class="qr-subtext">This QR code refreshes every 10 seconds.</p>
            <div id="qr-code-container">
              <?php if (isset($_SESSION['qr_generation_method']) && $_SESSION['qr_generation_method'] === 'fallback'): ?>
                <!-- Fallback QR Code Display -->
                <div class="qr-fallback-container">
                  <?php
                  $qr_url = "https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=" . urlencode($_SESSION['qr_payload']);
                  ?>
                  <img id="qr-code-image" src="<?= $qr_url ?>" alt="QR Code" style="max-width: 300px; border: 2px solid #ddd; padding: 10px; background: white;">
                  <p style="margin-top: 10px; color: #666; font-size: 12px;">
                    <i class="fas fa-info-circle"></i> QR Code generated using online service (GD extension not available)
                  </p>
                </div>
              <?php else: ?>
                <!-- Local QR Code Display -->
                <img id="qr-code-image" src="<?= $file_path ?>?t=<?= time() ?>" alt="QR Code">
              <?php endif; ?>
              <div id="countdown">10</div>
            </div>
            <div class="modal-actions">
              <button class="btn btn-secondary" onclick="stopQRSession()">
                <i class="fas fa-stop-circle"></i> End Session
              </button>
              <button class="btn btn-primary" onclick="closeModal()">
                <i class="fas fa-times"></i> Close
              </button>
            </div>
          </div>
        </div>
      <?php endif; ?>

    </div> <!-- end .main-content-qr -->
  </div>   <!-- end .container -->

  <!-- ─────────── FOOTER ─────────── -->
  <footer>
    <p>UNIVERSITI TUN HUSSEIN ONN MALAYSIA</p>
  </footer>

  <!-- ─────────── JAVASCRIPT: Countdown & AJAX Updates ─────────── -->
  <script>
    let countdown = 10;
    let qrUpdateInterval;

    function updateQRCode() {
      // Check if we're using fallback method
      const isFallback = <?= json_encode(isset($_SESSION['qr_generation_method']) && $_SESSION['qr_generation_method'] === 'fallback') ?>;

      if (isFallback) {
        // For fallback method, regenerate the URL with new timestamp
        fetch('update_qr_code.php')
          .then(res => res.json())
          .then(data => {
            if (data.success && data.qr_payload) {
              const img = document.getElementById('qr-code-image');
              const newUrl = 'https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=' + encodeURIComponent(data.qr_payload);
              img.src = newUrl;
            }
          })
          .catch(err => console.error('Error updating QR code:', err));
      } else {
        // Original method for local QR generation
        fetch('update_qr_code.php')
          .then(res => res.json())
          .then(data => {
            if (data.success) {
              const img = document.getElementById('qr-code-image');
              img.src = data.file_path + '?t=' + Date.now();
            }
          })
          .catch(err => console.error('Error updating QR code:', err));
      }
    }

    function updateCountdown() {
      const el = document.getElementById('countdown');
      if (!el) return;
      countdown--;
      el.textContent = countdown;
      if (countdown <= 0) {
        countdown = 10;
        updateQRCode();
      }
    }

    function closeModal() {
      const modal = document.getElementById('qr-modal');
      if (modal) modal.style.display = 'none';
      clearInterval(qrUpdateInterval);
    }

    function stopQRSession() {
      if (!confirm('Are you sure you want to end this attendance session?')) return;
      clearInterval(qrUpdateInterval);
      fetch('end_qr_session.php')
        .then(res => res.json())
        .then(data => {
          if (data.success) {
            alert('Attendance session ended.');
            closeModal();
          }
        })
        .catch(err => console.error('Error ending session:', err));
    }

    // Start countdown and QR updates once modal is visible
    document.addEventListener('DOMContentLoaded', () => {
      if (document.getElementById('qr-modal')) {
        qrUpdateInterval = setInterval(updateCountdown, 1000);
      }
    });
  </script>
</body>
</html>
