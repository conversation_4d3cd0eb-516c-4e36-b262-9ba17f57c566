/* General Styling */
body, html {
    min-height: 100vh;
    background-image: url('../images/uthm-background.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

/* Login Page Layout */
.login-page {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
}

.login-container {
    background-color: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    width: 350px;
    text-align: center;
}

.logo img {
    display: block;
    margin: 0 auto 20px;
    width: 150px; /* Resize logo */
    height: auto;
}

h2 {
    text-align: center;
    color: #333;
    margin-bottom: 20px;
}

/* Input Fields */
label {
    font-weight: bold;
    margin-top: 15px;
    display: block;
    color: #333;
}

input[type="text"], input[type="password"] {
    width: 100%;
    padding: 10px;
    margin: 10px 0;
    border: 1px solid #ddd;
    border-radius: 5px;
}

input:focus {
    outline: none;
    border-color: #00509e;
    box-shadow: 0 0 5px rgba(0, 80, 158, 0.5);
}

/* Buttons */
.btn {
    width: 100%;
    padding: 12px;
    background-color: #00509e;
    color: white;
    font-weight: bold;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 10px;
}

.btn:hover {
    background-color: #003366;
}

/* Links */
p a {
    color: #00509e;
    text-decoration: none;
}

p a:hover {
    text-decoration: underline;
}

/* Instruction Section */
.instruction {
    color: white;
    text-align: left;
    max-width: 400px;
}

.instruction img {
    width: 100%;
    border-radius: 10px;
    margin-top: 10px;
}

/* Header Styling */
header {
    background-color: #003366;
    color: white;
    /* padding: 20px; */
    text-align: center;
    position: sticky;
    top: 0;
    z-index: 1000;
}

header .logo img {
    height: 50px;
    vertical-align: middle;
}

header .logo h1 {
    display: inline;
    margin-left: 15px;
}

header .user-info {
    margin-top: 10px;
    font-size: 14px;
}

/* Navigation Bar */
nav {
    background-color: #00509e;
    padding: 15px 0;
    text-align: center;
}

nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: inline-block;
}

nav ul li {
    display: inline;
    margin: 0 15px;
}

nav ul li a {
    color: white;
    text-decoration: none;
    font-weight: bold;
}

nav ul li a:hover {
    text-decoration: underline;
}

/* Footer */
footer {
    background-color: #003366;
    color: white;
    text-align: center;
    padding: 10px 0;
}

/* Dashboard */
.dashboard-container {
    max-width: 900px;
    margin: 50px auto;
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.dashboard-container h1 {
    color: #333;
    text-align: center;
    margin-bottom: 20px;
}

.dashboard-links {
    display: flex;
    justify-content: space-around;
    margin-top: 20px;
}

.btn {
    padding: 5px 5px;
    font-size: 15px;
    text-align: center;
    display: block;
}

/* Tables */
.table-container {
    overflow-x: auto;
    margin-top: 20px;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

table th, table td {
    border: 1px solid #ddd;
    padding: 12px;
    text-align: left;
}

table th {
    background-color: #0078d4;
    color: white;
}

table tr:hover {
    background-color: #f1f1f1;
}
/* Reset */
body {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    box-sizing: border-box;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #0078d4;
    color: white;
    padding: 10px 20px;
}

.header-left img {
    height: 30px;
}

.header-right {
    display: flex;
    align-items: center;
}

.qr-button {
    background-color: white;
    color: #0078d4;
    border: none;
    padding: 5px 15px;
    margin-right: 10px;
    cursor: pointer;
}

.user-id {
    font-weight: bold;
}

/* Container */
.container {
    display: flex;
    height: calc(100vh - 70px);
}

/* Sidebar */
.sidebar {
    width: 20%;
    background-color: #f0f4f8;
    padding: 20px;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.profile {
    margin-bottom: 20px;
}

.profile-pic {
    border-radius: 50%;
    width: 100px;
    height: 100px;
    margin-bottom: 10px;
}

.profile-name {
    font-weight: bold;
}

.profile-id {
    color: gray;
    font-size: 0.9em;
}

.menu {
    list-style: none;
    padding: 0;
}

.menu li {
    margin: 10px 0;
}

.menu li a {
    text-decoration: none;
    color: #0078d4;
    font-weight: bold;
    display: block;
    padding: 10px;
    border: 1px solid transparent;
    border-radius: 5px;
    transition: background-color 0.3s, border-color 0.3s;
}

.menu li a:hover {
    background-color: #e3f2fd;
    border-color: #0078d4;
}

/* Main Content */
.main-content {
    width: 80%;
    padding: 20px;
}

.session {
    margin-bottom: 20px;
}

.session label {
    margin-right: 10px;
}

.view-button {
    margin-left: 10px;
    background-color: #0078d4;
    color: white;
    border: none;
    padding: 5px 15px;
    cursor: pointer;
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th, table td {
    border: 1px solid #ddd;
    padding: 10px;
    text-align: left;
}

table th {
    background-color: #f2f2f2;
}

footer {
    text-align: center;
    padding: 10px;
    background-color: #f0f4f8;
    font-size: 0.9em;
    color: gray;
    position: fixed;
    bottom: 0;
    width: 100%;
}
