<?php
/**
 * Secure Password Reset Form
 * Handles token-based password reset from email links
 */

session_start();
require '../config/config.php';
require_once __DIR__ . '/../includes/PasswordResetService.php';

// Get token from URL and clean it
$rawToken = $_GET['token'] ?? '';
$token = trim(urldecode($rawToken)); // Properly decode URL-encoded token
$message = '';
$messageType = 'error';
$tokenValid = false;
$tokenData = null;



try {
    $passwordResetService = new PasswordResetService($conn);

    // Log token validation attempt
    error_log("PASSWORD RESET FORM: Token validation attempt - Token: " . substr($token, 0, 10) . "...");

    // Validate token
    if (!empty($token)) {

        // Check if table exists first
        $tableCheck = $conn->query("SHOW TABLES LIKE 'password_reset_tokens'");
        if ($tableCheck->num_rows === 0) {
            $message = "Password reset system is not properly configured. Please contact your administrator.";
            error_log("PASSWORD RESET FORM ERROR: password_reset_tokens table does not exist");
        } else {

            $tokenData = $passwordResetService->validateToken($token);
            if ($tokenData) {
                $tokenValid = true;
                error_log("PASSWORD RESET FORM: Token valid for user {$tokenData['user_id']} role {$tokenData['user_role']}");
            } else {
                $message = "Invalid or expired password reset link. Please request a new password reset.";
                error_log("PASSWORD RESET FORM ERROR: Token validation failed for token: " . substr($token, 0, 10) . "...");
            }
        }
    } else {
        $message = "No password reset token provided.";
        error_log("PASSWORD RESET FORM ERROR: No token provided in URL");
    }

} catch (Exception $e) {
    $message = "System error: " . $e->getMessage();
    error_log("Password reset form error: " . $e->getMessage());
}

// Handle password reset form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $tokenValid) {
    $newPassword = $_POST['new_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    if (empty($newPassword) || empty($confirmPassword)) {
        $message = "Please fill in both password fields.";
        $messageType = 'error';
    } elseif ($newPassword !== $confirmPassword) {
        $message = "Passwords do not match. Please try again.";
        $messageType = 'error';
    } else {
        // Reset password using the service
        $resetResult = $passwordResetService->resetPassword($token, $newPassword);
        
        if ($resetResult['success']) {
            $message = "Password reset successfully! You can now log in with your new password.";
            $messageType = 'success';
            $tokenValid = false; // Hide form after successful reset
            
            // Clear any first-login session data
            unset($_SESSION['first_login_user_id'], $_SESSION['first_login_role']);
            
        } else {
            $message = $resetResult['message'];
            $messageType = 'error';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - UTHM Attendance</title>
    
    <!-- ─── GLOBAL CSS (same as other dashboards) ─── -->
    <link rel="stylesheet" href="../dashboard/css/base-styles.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-header.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-sidebar.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-footer.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-dashboard-styles.css" />
    
    <!-- ─── BOOTSTRAP & ICONS ─── -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        /* ─────────── PASSWORD RESET FORM PAGE SPECIFIC STYLES ─────────── */
        .reset-form-container {
            max-width: 500px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        .reset-form-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-md);
        }

        .reset-form-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
        }

        .reset-form-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-md);
        }

        .reset-form-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .reset-form-subtitle {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .form-section {
            margin-bottom: var(--spacing-lg);
        }

        .form-group {
            margin-bottom: var(--spacing-lg);
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .form-input {
            width: 100%;
            padding: var(--spacing-md);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-input.is-valid {
            border-color: #10b981;
        }

        .form-input.is-invalid {
            border-color: #ef4444;
        }

        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: #ffffff;
            border: 2px solid #3b82f6;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            min-height: 48px;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
        }

        .submit-btn:hover:not(:disabled) {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            border-color: #2563eb;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }

        .submit-btn:disabled {
            background: #9ca3af;
            border-color: #9ca3af;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .password-requirements {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: var(--spacing-md);
            margin-top: var(--spacing-sm);
            font-size: 0.875rem;
        }

        .requirements-list {
            list-style: none;
            padding: 0;
            margin: var(--spacing-sm) 0 0 0;
        }

        .requirements-list li {
            display: flex;
            align-items: center;
            margin-bottom: var(--spacing-xs);
            color: var(--text-secondary);
        }

        .requirements-list i {
            margin-right: var(--spacing-sm);
            width: 16px;
        }

        .success-section {
            text-align: center;
        }

        .success-icon {
            font-size: 4rem;
            color: #10b981;
            margin-bottom: var(--spacing-lg);
        }

        .success-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #10b981;
            margin-bottom: var(--spacing-md);
        }

        .success-message {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: var(--spacing-xl);
        }

        .login-btn {
            background: #10b981;
            color: #ffffff;
            text-decoration: none;
            padding: 12px 32px;
            border-radius: 8px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            border: 2px solid #10b981;
            min-height: 48px;
            box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
        }

        .login-btn:hover {
            background: #059669;
            border-color: #059669;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
            color: #ffffff;
            text-decoration: none;
        }

        .back-link {
            display: block;
            text-align: center;
            margin-top: var(--spacing-lg);
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .back-link:hover {
            color: var(--primary-color);
            text-decoration: none;
        }

        /* ─────────── RESPONSIVE DESIGN ─────────── */
        @media (max-width: 768px) {
            .reset-form-container {
                padding: var(--spacing-lg);
            }

            .reset-form-card {
                padding: var(--spacing-lg);
            }

            .reset-form-icon {
                font-size: 2.5rem;
            }

            .reset-form-title {
                font-size: 1.3rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-left">
            <img src="../assets/images/logo-uthm2.png" alt="UTHM Logo" class="logo">
        </div>
        <div class="header-right">
            <span class="user-id">Password Reset</span>
        </div>
    </div>

    <div class="container">
        <div class="reset-form-container">
            <div class="reset-form-card">
                <?php if ($messageType === 'success' && !$tokenValid): ?>
                    <!-- Success Section -->
                    <div class="success-section">
                        <div class="success-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h1 class="success-title">Password Reset Successful!</h1>
                        <p class="success-message">
                            Your password has been successfully updated. You can now log in to the UTHM Attendance System with your new password.
                        </p>
                        <a href="../index.php" class="login-btn">
                            <i class="fas fa-sign-in-alt"></i>
                            Go to Login Page
                        </a>
                    </div>

                <?php elseif ($tokenValid): ?>
                    <!-- Password Reset Form -->
                    <div class="reset-form-header">
                        <div class="reset-form-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <h1 class="reset-form-title">Set Your New Password</h1>
                        <p class="reset-form-subtitle">
                            Please create a strong password for your UTHM Attendance System account.
                        </p>
                    </div>

                    <!-- Message Display -->
                    <?php if (!empty($message)): ?>
                        <div class="popup-message <?= $messageType ?>" style="margin-bottom: var(--spacing-lg);">
                            <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'times-circle' ?>"></i>
                            <?= htmlspecialchars($message) ?>
                        </div>
                    <?php endif; ?>

                    <form method="post" id="resetForm">
                        <div class="form-section">
                            <div class="form-group">
                                <label for="new_password">New Password</label>
                                <input type="password" id="new_password" name="new_password"
                                       class="form-input" required autocomplete="new-password">

                                <div class="password-requirements">
                                    <h4>Password Requirements:</h4>
                                    <ul class="requirements-list">
                                        <li><i class="fas fa-times text-danger" id="req-length"></i> At least 8 characters</li>
                                        <li><i class="fas fa-times text-danger" id="req-upper"></i> One uppercase letter</li>
                                        <li><i class="fas fa-times text-danger" id="req-lower"></i> One lowercase letter</li>
                                        <li><i class="fas fa-times text-danger" id="req-number"></i> One number</li>
                                        <li><i class="fas fa-times text-danger" id="req-special"></i> One special character</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="confirm_password">Confirm New Password</label>
                                <input type="password" id="confirm_password" name="confirm_password"
                                       class="form-input" required autocomplete="new-password">
                            </div>
                        </div>

                        <button type="submit" class="submit-btn" id="submitBtn" disabled>
                            <i class="fas fa-lock"></i>
                            Reset Password
                        </button>
                    </form>

                <?php else: ?>
                    <!-- Invalid Token Section -->
                    <div class="reset-form-header">
                        <div class="reset-form-icon" style="color: #dc2626;">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h1 class="reset-form-title" style="color: #dc2626;">Invalid Reset Link</h1>
                        <p class="reset-form-subtitle">
                            <?= htmlspecialchars($message) ?>
                        </p>
                        <div style="margin-top: var(--spacing-xl);">
                            <a href="../index.php" class="login-btn">
                                <i class="fas fa-arrow-left"></i>
                                Back to Login
                            </a>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($tokenValid): ?>
                    <a href="../index.php" class="back-link">
                        <i class="fas fa-arrow-left"></i>
                        Back to Login
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <p>UNIVERSITI TUN HUSSEIN ONN MALAYSIA</p>
    </footer>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function togglePassword(inputId, icon) {
            const input = document.getElementById(inputId);
            const isVisible = input.type === 'text';

            input.type = isVisible ? 'password' : 'text';
            icon.classList.toggle('fa-eye');
            icon.classList.toggle('fa-eye-slash');
        }

        // Password strength validation
        const passwordInput = document.getElementById('new_password');
        const confirmInput = document.getElementById('confirm_password');
        const submitBtn = document.getElementById('submitBtn');

        if (passwordInput) {
            passwordInput.addEventListener('input', validatePassword);
            confirmInput.addEventListener('input', validateConfirmPassword);
        }

        function validatePassword() {
            const password = passwordInput.value;
            const requirements = {
                length: password.length >= 8,
                upper: /[A-Z]/.test(password),
                lower: /[a-z]/.test(password),
                number: /[0-9]/.test(password),
                special: /[^a-zA-Z0-9]/.test(password)
            };

            // Update requirement indicators
            Object.keys(requirements).forEach(req => {
                const element = document.getElementById(`req-${req}`);
                if (element) {
                    if (requirements[req]) {
                        element.className = 'fas fa-check text-success';
                    } else {
                        element.className = 'fas fa-times text-danger';
                    }
                }
            });

            validateConfirmPassword();
            updateSubmitButton();
        }

        function validateConfirmPassword() {
            const password = passwordInput.value;
            const confirm = confirmInput.value;

            if (confirm.length === 0) {
                confirmInput.classList.remove('is-invalid', 'is-valid');
                return;
            }

            if (password === confirm) {
                confirmInput.classList.remove('is-invalid');
                confirmInput.classList.add('is-valid');
            } else {
                confirmInput.classList.remove('is-valid');
                confirmInput.classList.add('is-invalid');
            }

            updateSubmitButton();
        }

        function updateSubmitButton() {
            const password = passwordInput.value;
            const confirm = confirmInput.value;

            const requirements = {
                length: password.length >= 8,
                upper: /[A-Z]/.test(password),
                lower: /[a-z]/.test(password),
                number: /[0-9]/.test(password),
                special: /[^a-zA-Z0-9]/.test(password)
            };

            const allRequirementsMet = Object.values(requirements).every(Boolean);
            const passwordsMatch = password === confirm && confirm.length > 0;

            if (submitBtn) {
                submitBtn.disabled = !(allRequirementsMet && passwordsMatch);
            }
        }

        // Form submission validation
        document.getElementById('resetForm')?.addEventListener('submit', function(e) {
            const password = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;

            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Passwords do not match. Please try again.');
                return false;
            }

            if (password.length < 8) {
                e.preventDefault();
                alert('Password must be at least 8 characters long.');
                return false;
            }
        });
    </script>
</body>
</html>
