<?php
// ===================================================================
// QR CODE FALLBACK IMPLEMENTATION
// This provides QR code generation when GD extension is not available
// ===================================================================

class QRCodeFallback {
    
    /**
     * Generate QR code using online service as fallback
     * @param string $data The data to encode
     * @param string $size Size of QR code (default: 200x200)
     * @return string HTML img tag with QR code
     */
    public static function generateOnline($data, $size = '200x200') {
        // Use Google Charts API (free, no API key required)
        $encoded_data = urlencode($data);
        $qr_url = "https://chart.googleapis.com/chart?chs={$size}&cht=qr&chl={$encoded_data}";
        
        return $qr_url;
    }
    
    /**
     * Generate QR code HTML with fallback options
     * @param string $data The data to encode
     * @param int $size Size in pixels
     * @return string Complete HTML for QR code display
     */
    public static function generateHTML($data, $size = 200) {
        $qr_url = self::generateOnline($data, "{$size}x{$size}");
        
        $html = "
        <div class='qr-code-container' style='text-align: center; margin: 20px 0;'>
            <img src='{$qr_url}' 
                 alt='QR Code' 
                 style='max-width: {$size}px; height: auto; border: 1px solid #ddd; padding: 10px; background: white;'
                 onerror=\"this.style.display='none'; document.getElementById('qr-fallback').style.display='block';\">
            
            <div id='qr-fallback' style='display: none; padding: 20px; border: 1px solid #ddd; background: #f9f9f9; margin-top: 10px;'>
                <p><strong>QR Code Data:</strong></p>
                <p style='word-break: break-all; font-family: monospace; background: white; padding: 10px; border: 1px solid #ccc;'>{$data}</p>
                <p><small>Manual entry option available if QR scanner fails</small></p>
            </div>
        </div>";
        
        return $html;
    }
    
    /**
     * Check if GD extension is available
     * @return bool
     */
    public static function isGDAvailable() {
        return extension_loaded('gd') && function_exists('imagecreate');
    }
    
    /**
     * Generate QR code with automatic fallback
     * @param string $data The data to encode
     * @param string $filename Optional filename for local generation
     * @param int $size Size in pixels
     * @return mixed Returns filename for GD or HTML for fallback
     */
    public static function generate($data, $filename = null, $size = 200) {
        if (self::isGDAvailable()) {
            // Use original QR code library
            try {
                require_once __DIR__ . '/../phpqrcode/qrlib.php';
                
                if ($filename) {
                    QRcode::png($data, $filename, QR_ECLEVEL_L, 4, 2);
                    return $filename;
                } else {
                    // Generate to output buffer
                    ob_start();
                    QRcode::png($data, false, QR_ECLEVEL_L, 4, 2);
                    $image_data = ob_get_contents();
                    ob_end_clean();
                    return 'data:image/png;base64,' . base64encode($image_data);
                }
            } catch (Exception $e) {
                // Fall back to online service if GD fails
                return self::generateHTML($data, $size);
            }
        } else {
            // Use online service fallback
            return self::generateHTML($data, $size);
        }
    }
}

// ===================================================================
// USAGE EXAMPLES
// ===================================================================

/*
// Example 1: Simple QR code generation
$qr_html = QRCodeFallback::generateHTML("https://example.com/attendance?token=abc123");
echo $qr_html;

// Example 2: Check if GD is available
if (QRCodeFallback::isGDAvailable()) {
    echo "Using local QR generation";
} else {
    echo "Using online QR service";
}

// Example 3: Automatic fallback
$result = QRCodeFallback::generate("attendance_data_here");
if (is_string($result) && strpos($result, '<div') === 0) {
    // HTML fallback
    echo $result;
} else {
    // File or data URL
    echo "<img src='$result' alt='QR Code'>";
}
*/
?>
