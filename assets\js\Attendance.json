{"contractName": "Attendance", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "student", "type": "address"}, {"indexed": true, "internalType": "string", "name": "studentID", "type": "string"}, {"indexed": true, "internalType": "uint256", "name": "courseId", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "studentName", "type": "string"}, {"indexed": false, "internalType": "string", "name": "courseName", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "AttendanceRecorded", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "hasAttended", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function", "constant": true}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "records", "outputs": [{"internalType": "address", "name": "student", "type": "address"}, {"internalType": "string", "name": "studentID", "type": "string"}, {"internalType": "string", "name": "studentName", "type": "string"}, {"internalType": "uint256", "name": "courseId", "type": "uint256"}, {"internalType": "string", "name": "courseName", "type": "string"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "stateMutability": "view", "type": "function", "constant": true}, {"inputs": [{"internalType": "uint256", "name": "courseId", "type": "uint256"}, {"internalType": "string", "name": "studentID", "type": "string"}, {"internalType": "string", "name": "studentName", "type": "string"}, {"internalType": "string", "name": "courseName", "type": "string"}], "name": "markAttendance", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getRecordCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "constant": true}, {"inputs": [{"internalType": "uint256", "name": "idx", "type": "uint256"}], "name": "getRecord", "outputs": [{"internalType": "address", "name": "student", "type": "address"}, {"internalType": "string", "name": "studentID", "type": "string"}, {"internalType": "string", "name": "studentName", "type": "string"}, {"internalType": "uint256", "name": "courseId", "type": "uint256"}, {"internalType": "string", "name": "courseName", "type": "string"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "stateMutability": "view", "type": "function", "constant": true}], "metadata": "{\"compiler\":{\"version\":\"0.8.0+commit.c7dfd78e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"student\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"string\",\"name\":\"studentID\",\"type\":\"string\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"courseId\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"studentName\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"courseName\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"name\":\"AttendanceRecorded\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"idx\",\"type\":\"uint256\"}],\"name\":\"getRecord\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"student\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"studentID\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"studentName\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"courseId\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"courseName\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getRecordCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"hasAttended\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"courseId\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"studentID\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"studentName\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"courseName\",\"type\":\"string\"}],\"name\":\"markAttendance\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"records\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"student\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"studentID\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"studentName\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"courseId\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"courseName\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"markAttendance(uint256,string,string,string)\":{\"params\":{\"courseId\":\"numeric course identifier\",\"courseName\":\"name of the course/session\",\"studentID\":\"student\\u2019s matric (e.g. \\\"AI250\\\")\",\"studentName\":\"full name of the student\"}}},\"title\":\"UTHM Attendance Smart Contract (matric-based duplicate guard)\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"getRecord(uint256)\":{\"notice\":\"Fetch a specific attendance record by index\"},\"getRecordCount()\":{\"notice\":\"Number of attendance records stored\"},\"markAttendance(uint256,string,string,string)\":{\"notice\":\"Mark attendance for a given matric + course\"}},\"notice\":\"Records student attendance per course in an immutable ledger\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"project:/contracts/Attendance.sol\":\"Attendance\"},\"evmVersion\":\"istanbul\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"project:/contracts/Attendance.sol\":{\"keccak256\":\"0x22e2d2409cd25e042a2fee181efae59173921e54d1b9994f433ffc8178583eab\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4911902344829d97bcbee11f8a841d8f6cd780b3052ad57df603855584f3d0c0\",\"dweb:/ipfs/QmcGebuxGkcJbjUjVWCCzRjSCzt3aRdJqE3pKeDJd8NQ7u\"]}},\"version\":1}", "bytecode": "0x608060405234801561001057600080fd5b50610fee806100206000396000f3fe608060405234801561001057600080fd5b50600436106100575760003560e01c806303e9e6091461005c5780631c12ceb91461009157806334461067146100c1578063a9b8f385146100f6578063ca267f2814610112575b600080fd5b61007660048036038101906100719190610a4a565b610130565b60405161008896959493929190610d1d565b60405180910390f35b6100ab60048036038101906100a69190610a73565b6103c3565b6040516100b89190610d93565b60405180910390f35b6100db60048036038101906100d69190610a4a565b6103f2565b6040516100ed96959493929190610d1d565b60405180910390f35b610110600480360381019061010b9190610aaf565b6105f6565b005b61011a610926565b6040516101279190610e37565b60405180910390f35b60006060806000606060006001805490508710610182576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161017990610df7565b60405180910390fd5b6000600188815481106101be577f4e487b7100000000000000000000000000000000000000000000000000000000600052603260045260246000fd5b906000526020600020906006020190508060000160009054906101000a900473ffffffffffffffffffffffffffffffffffffffff168160010182600201836003015484600401856005015484805461021590610f18565b80601f016020809104026020016040519081016040528092919081815260200182805461024190610f18565b801561028e5780601f106102635761010080835404028352916020019161028e565b820191906000526020600020905b81548152906001019060200180831161027157829003601f168201915b505050505094508380546102a190610f18565b80601f01602080910402602001604051908101604052809291908181526020018280546102cd90610f18565b801561031a5780601f106102ef5761010080835404028352916020019161031a565b820191906000526020600020905b8154815290600101906020018083116102fd57829003601f168201915b5050505050935081805461032d90610f18565b80601f016020809104026020016040519081016040528092919081815260200182805461035990610f18565b80156103a65780601f1061037b576101008083540402835291602001916103a6565b820191906000526020600020905b81548152906001019060200180831161038957829003601f168201915b505050505091509650965096509650965096505091939550919395565b60006020528160005260406000206020528060005260406000206000915091509054906101000a900460ff1681565b6001818154811061040257600080fd5b90600052602060002090600602016000915090508060000160009054906101000a900473ffffffffffffffffffffffffffffffffffffffff169080600101805461044b90610f18565b80601f016020809104026020016040519081016040528092919081815260200182805461047790610f18565b80156104c45780601f10610499576101008083540402835291602001916104c4565b820191906000526020600020905b8154815290600101906020018083116104a757829003601f168201915b5050505050908060020180546104d990610f18565b80601f016020809104026020016040519081016040528092919081815260200182805461050590610f18565b80156105525780601f1061052757610100808354040283529160200191610552565b820191906000526020600020905b81548152906001019060200180831161053557829003601f168201915b50505050509080600301549080600401805461056d90610f18565b80601f016020809104026020016040519081016040528092919081815260200182805461059990610f18565b80156105e65780601f106105bb576101008083540402835291602001916105e6565b820191906000526020600020905b8154815290600101906020018083116105c957829003601f168201915b5050505050908060050154905086565b60008686604051610608929190610ceb565b60405180910390209050600080898152602001908152602001600020600082815260200190815260200160002060009054906101000a900460ff1615610683576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161067a90610e17565b60405180910390fd5b60016000808a8152602001908152602001600020600083815260200190815260200160002060006101000a81548160ff02191690831515021790555060016040518060c001604052803373ffffffffffffffffffffffffffffffffffffffff16815260200189898080601f016020809104026020016040519081016040528093929190818152602001838380828437600081840152601f19601f82011690508083019250505050505050815260200187878080601f016020809104026020016040519081016040528093929190818152602001838380828437600081840152601f19601f8201169050808301925050505050505081526020018a815260200185858080601f016020809104026020016040519081016040528093929190818152602001838380828437600081840152601f19601f82011690508083019250505050505050815260200142815250908060018154018082558091505060019003906000526020600020906006020160009091909190915060008201518160000160006101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff160217905550602082015181600101908051906020019061085c929190610933565b506040820151816002019080519060200190610879929190610933565b506060820151816003015560808201518160040190805190602001906108a0929190610933565b5060a0820151816005015550508787876040516108be929190610d04565b60405180910390203373ffffffffffffffffffffffffffffffffffffffff167f31830c83b1dec597681de7ba91873fe951cd4db6fc460ea5765f17381634831d8888888842604051610914959493929190610dae565b60405180910390a45050505050505050565b6000600180549050905090565b82805461093f90610f18565b90600052602060002090601f01602090048101928261096157600085556109a8565b82601f1061097a57805160ff19168380011785556109a8565b828001600101855582156109a8579182015b828111156109a757825182559160200191906001019061098c565b5b5090506109b591906109b9565b5090565b5b808211156109d25760008160009055506001016109ba565b5090565b6000813590506109e581610f8a565b92915050565b60008083601f8401126109fd57600080fd5b8235905067ffffffffffffffff811115610a1657600080fd5b602083019150836001820283011115610a2e57600080fd5b9250929050565b600081359050610a4481610fa1565b92915050565b600060208284031215610a5c57600080fd5b6000610a6a84828501610a35565b91505092915050565b60008060408385031215610a8657600080fd5b6000610a9485828601610a35565b9250506020610aa5858286016109d6565b9150509250929050565b60008060008060008060006080888a031215610aca57600080fd5b6000610ad88a828b01610a35565b975050602088013567ffffffffffffffff811115610af557600080fd5b610b018a828b016109eb565b9650965050604088013567ffffffffffffffff811115610b2057600080fd5b610b2c8a828b016109eb565b9450945050606088013567ffffffffffffffff811115610b4b57600080fd5b610b578a828b016109eb565b925092505092959891949750929550565b610b7181610e84565b82525050565b610b8081610e96565b82525050565b6000610b928385610e5d565b9350610b9f838584610ed6565b82840190509392505050565b6000610bb78385610e68565b9350610bc4838584610ed6565b610bcd83610f79565b840190509392505050565b6000610be48385610e79565b9350610bf1838584610ed6565b82840190509392505050565b6000610c0882610e52565b610c128185610e68565b9350610c22818560208601610ee5565b610c2b81610f79565b840191505092915050565b6000610c43601383610e68565b91507f496e646578206f7574206f6620626f756e6473000000000000000000000000006000830152602082019050919050565b6000610c83602983610e68565b91507f416c7265616479206d61726b656420617474656e64616e636520666f7220746860008301527f6973206d617472696300000000000000000000000000000000000000000000006020830152604082019050919050565b610ce581610ecc565b82525050565b6000610cf8828486610b86565b91508190509392505050565b6000610d11828486610bd8565b91508190509392505050565b600060c082019050610d326000830189610b68565b8181036020830152610d448188610bfd565b90508181036040830152610d588187610bfd565b9050610d676060830186610cdc565b8181036080830152610d798185610bfd565b9050610d8860a0830184610cdc565b979650505050505050565b6000602082019050610da86000830184610b77565b92915050565b60006060820190508181036000830152610dc9818789610bab565b90508181036020830152610dde818587610bab565b9050610ded6040830184610cdc565b9695505050505050565b60006020820190508181036000830152610e1081610c36565b9050919050565b60006020820190508181036000830152610e3081610c76565b9050919050565b6000602082019050610e4c6000830184610cdc565b92915050565b600081519050919050565b600081905092915050565b600082825260208201905092915050565b600081905092915050565b6000610e8f82610eac565b9050919050565b60008115159050919050565b6000819050919050565b600073ffffffffffffffffffffffffffffffffffffffff82169050919050565b6000819050919050565b82818337600083830152505050565b60005b83811015610f03578082015181840152602081019050610ee8565b83811115610f12576000848401525b50505050565b60006002820490506001821680610f3057607f821691505b60208210811415610f4457610f43610f4a565b5b50919050565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052602260045260246000fd5b6000601f19601f8301169050919050565b610f9381610ea2565b8114610f9e57600080fd5b50565b610faa81610ecc565b8114610fb557600080fd5b5056fea26469706673582212209c90a512380310b0539bebfaf938d33fdca2bbe6d8631d4461f0a47031cce73764736f6c63430008000033", "deployedBytecode": "0x608060405234801561001057600080fd5b50600436106100575760003560e01c806303e9e6091461005c5780631c12ceb91461009157806334461067146100c1578063a9b8f385146100f6578063ca267f2814610112575b600080fd5b61007660048036038101906100719190610a4a565b610130565b60405161008896959493929190610d1d565b60405180910390f35b6100ab60048036038101906100a69190610a73565b6103c3565b6040516100b89190610d93565b60405180910390f35b6100db60048036038101906100d69190610a4a565b6103f2565b6040516100ed96959493929190610d1d565b60405180910390f35b610110600480360381019061010b9190610aaf565b6105f6565b005b61011a610926565b6040516101279190610e37565b60405180910390f35b60006060806000606060006001805490508710610182576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161017990610df7565b60405180910390fd5b6000600188815481106101be577f4e487b7100000000000000000000000000000000000000000000000000000000600052603260045260246000fd5b906000526020600020906006020190508060000160009054906101000a900473ffffffffffffffffffffffffffffffffffffffff168160010182600201836003015484600401856005015484805461021590610f18565b80601f016020809104026020016040519081016040528092919081815260200182805461024190610f18565b801561028e5780601f106102635761010080835404028352916020019161028e565b820191906000526020600020905b81548152906001019060200180831161027157829003601f168201915b505050505094508380546102a190610f18565b80601f01602080910402602001604051908101604052809291908181526020018280546102cd90610f18565b801561031a5780601f106102ef5761010080835404028352916020019161031a565b820191906000526020600020905b8154815290600101906020018083116102fd57829003601f168201915b5050505050935081805461032d90610f18565b80601f016020809104026020016040519081016040528092919081815260200182805461035990610f18565b80156103a65780601f1061037b576101008083540402835291602001916103a6565b820191906000526020600020905b81548152906001019060200180831161038957829003601f168201915b505050505091509650965096509650965096505091939550919395565b60006020528160005260406000206020528060005260406000206000915091509054906101000a900460ff1681565b6001818154811061040257600080fd5b90600052602060002090600602016000915090508060000160009054906101000a900473ffffffffffffffffffffffffffffffffffffffff169080600101805461044b90610f18565b80601f016020809104026020016040519081016040528092919081815260200182805461047790610f18565b80156104c45780601f10610499576101008083540402835291602001916104c4565b820191906000526020600020905b8154815290600101906020018083116104a757829003601f168201915b5050505050908060020180546104d990610f18565b80601f016020809104026020016040519081016040528092919081815260200182805461050590610f18565b80156105525780601f1061052757610100808354040283529160200191610552565b820191906000526020600020905b81548152906001019060200180831161053557829003601f168201915b50505050509080600301549080600401805461056d90610f18565b80601f016020809104026020016040519081016040528092919081815260200182805461059990610f18565b80156105e65780601f106105bb576101008083540402835291602001916105e6565b820191906000526020600020905b8154815290600101906020018083116105c957829003601f168201915b5050505050908060050154905086565b60008686604051610608929190610ceb565b60405180910390209050600080898152602001908152602001600020600082815260200190815260200160002060009054906101000a900460ff1615610683576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161067a90610e17565b60405180910390fd5b60016000808a8152602001908152602001600020600083815260200190815260200160002060006101000a81548160ff02191690831515021790555060016040518060c001604052803373ffffffffffffffffffffffffffffffffffffffff16815260200189898080601f016020809104026020016040519081016040528093929190818152602001838380828437600081840152601f19601f82011690508083019250505050505050815260200187878080601f016020809104026020016040519081016040528093929190818152602001838380828437600081840152601f19601f8201169050808301925050505050505081526020018a815260200185858080601f016020809104026020016040519081016040528093929190818152602001838380828437600081840152601f19601f82011690508083019250505050505050815260200142815250908060018154018082558091505060019003906000526020600020906006020160009091909190915060008201518160000160006101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff160217905550602082015181600101908051906020019061085c929190610933565b506040820151816002019080519060200190610879929190610933565b506060820151816003015560808201518160040190805190602001906108a0929190610933565b5060a0820151816005015550508787876040516108be929190610d04565b60405180910390203373ffffffffffffffffffffffffffffffffffffffff167f31830c83b1dec597681de7ba91873fe951cd4db6fc460ea5765f17381634831d8888888842604051610914959493929190610dae565b60405180910390a45050505050505050565b6000600180549050905090565b82805461093f90610f18565b90600052602060002090601f01602090048101928261096157600085556109a8565b82601f1061097a57805160ff19168380011785556109a8565b828001600101855582156109a8579182015b828111156109a757825182559160200191906001019061098c565b5b5090506109b591906109b9565b5090565b5b808211156109d25760008160009055506001016109ba565b5090565b6000813590506109e581610f8a565b92915050565b60008083601f8401126109fd57600080fd5b8235905067ffffffffffffffff811115610a1657600080fd5b602083019150836001820283011115610a2e57600080fd5b9250929050565b600081359050610a4481610fa1565b92915050565b600060208284031215610a5c57600080fd5b6000610a6a84828501610a35565b91505092915050565b60008060408385031215610a8657600080fd5b6000610a9485828601610a35565b9250506020610aa5858286016109d6565b9150509250929050565b60008060008060008060006080888a031215610aca57600080fd5b6000610ad88a828b01610a35565b975050602088013567ffffffffffffffff811115610af557600080fd5b610b018a828b016109eb565b9650965050604088013567ffffffffffffffff811115610b2057600080fd5b610b2c8a828b016109eb565b9450945050606088013567ffffffffffffffff811115610b4b57600080fd5b610b578a828b016109eb565b925092505092959891949750929550565b610b7181610e84565b82525050565b610b8081610e96565b82525050565b6000610b928385610e5d565b9350610b9f838584610ed6565b82840190509392505050565b6000610bb78385610e68565b9350610bc4838584610ed6565b610bcd83610f79565b840190509392505050565b6000610be48385610e79565b9350610bf1838584610ed6565b82840190509392505050565b6000610c0882610e52565b610c128185610e68565b9350610c22818560208601610ee5565b610c2b81610f79565b840191505092915050565b6000610c43601383610e68565b91507f496e646578206f7574206f6620626f756e6473000000000000000000000000006000830152602082019050919050565b6000610c83602983610e68565b91507f416c7265616479206d61726b656420617474656e64616e636520666f7220746860008301527f6973206d617472696300000000000000000000000000000000000000000000006020830152604082019050919050565b610ce581610ecc565b82525050565b6000610cf8828486610b86565b91508190509392505050565b6000610d11828486610bd8565b91508190509392505050565b600060c082019050610d326000830189610b68565b8181036020830152610d448188610bfd565b90508181036040830152610d588187610bfd565b9050610d676060830186610cdc565b8181036080830152610d798185610bfd565b9050610d8860a0830184610cdc565b979650505050505050565b6000602082019050610da86000830184610b77565b92915050565b60006060820190508181036000830152610dc9818789610bab565b90508181036020830152610dde818587610bab565b9050610ded6040830184610cdc565b9695505050505050565b60006020820190508181036000830152610e1081610c36565b9050919050565b60006020820190508181036000830152610e3081610c76565b9050919050565b6000602082019050610e4c6000830184610cdc565b92915050565b600081519050919050565b600081905092915050565b600082825260208201905092915050565b600081905092915050565b6000610e8f82610eac565b9050919050565b60008115159050919050565b6000819050919050565b600073ffffffffffffffffffffffffffffffffffffffff82169050919050565b6000819050919050565b82818337600083830152505050565b60005b83811015610f03578082015181840152602081019050610ee8565b83811115610f12576000848401525b50505050565b60006002820490506001821680610f3057607f821691505b60208210811415610f4457610f43610f4a565b5b50919050565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052602260045260246000fd5b6000601f19601f8301169050919050565b610f9381610ea2565b8114610f9e57600080fd5b50565b610faa81610ecc565b8114610fb557600080fd5b5056fea26469706673582212209c90a512380310b0539bebfaf938d33fdca2bbe6d8631d4461f0a47031cce73764736f6c63430008000033", "immutableReferences": {}, "generatedSources": [], "deployedGeneratedSources": [{"ast": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "0:10956:2", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "59:87:2", "statements": [{"nodeType": "YulAssignment", "src": "69:29:2", "value": {"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "91:6:2"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "78:12:2"}, "nodeType": "YulFunctionCall", "src": "78:20:2"}, "variableNames": [{"name": "value", "nodeType": "YulIdentifier", "src": "69:5:2"}]}, {"expression": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "134:5:2"}], "functionName": {"name": "validator_revert_t_bytes32", "nodeType": "YulIdentifier", "src": "107:26:2"}, "nodeType": "YulFunctionCall", "src": "107:33:2"}, "nodeType": "YulExpressionStatement", "src": "107:33:2"}]}, "name": "abi_decode_t_bytes32", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "offset", "nodeType": "YulTypedName", "src": "37:6:2", "type": ""}, {"name": "end", "nodeType": "YulTypedName", "src": "45:3:2", "type": ""}], "returnVariables": [{"name": "value", "nodeType": "YulTypedName", "src": "53:5:2", "type": ""}], "src": "7:139:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "241:277:2", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "290:16:2", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "299:1:2", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "302:1:2", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "292:6:2"}, "nodeType": "YulFunctionCall", "src": "292:12:2"}, "nodeType": "YulExpressionStatement", "src": "292:12:2"}]}, "condition": {"arguments": [{"arguments": [{"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "269:6:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "277:4:2", "type": "", "value": "0x1f"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "265:3:2"}, "nodeType": "YulFunctionCall", "src": "265:17:2"}, {"name": "end", "nodeType": "YulIdentifier", "src": "284:3:2"}], "functionName": {"name": "slt", "nodeType": "YulIdentifier", "src": "261:3:2"}, "nodeType": "YulFunctionCall", "src": "261:27:2"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "254:6:2"}, "nodeType": "YulFunctionCall", "src": "254:35:2"}, "nodeType": "YulIf", "src": "251:2:2"}, {"nodeType": "YulAssignment", "src": "315:30:2", "value": {"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "338:6:2"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "325:12:2"}, "nodeType": "YulFunctionCall", "src": "325:20:2"}, "variableNames": [{"name": "length", "nodeType": "YulIdentifier", "src": "315:6:2"}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "388:16:2", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "397:1:2", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "400:1:2", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "390:6:2"}, "nodeType": "YulFunctionCall", "src": "390:12:2"}, "nodeType": "YulExpressionStatement", "src": "390:12:2"}]}, "condition": {"arguments": [{"name": "length", "nodeType": "YulIdentifier", "src": "360:6:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "368:18:2", "type": "", "value": "0xffffffffffffffff"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "357:2:2"}, "nodeType": "YulFunctionCall", "src": "357:30:2"}, "nodeType": "YulIf", "src": "354:2:2"}, {"nodeType": "YulAssignment", "src": "413:29:2", "value": {"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "429:6:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "437:4:2", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "425:3:2"}, "nodeType": "YulFunctionCall", "src": "425:17:2"}, "variableNames": [{"name": "arrayPos", "nodeType": "YulIdentifier", "src": "413:8:2"}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "496:16:2", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "505:1:2", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "508:1:2", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "498:6:2"}, "nodeType": "YulFunctionCall", "src": "498:12:2"}, "nodeType": "YulExpressionStatement", "src": "498:12:2"}]}, "condition": {"arguments": [{"arguments": [{"name": "arrayPos", "nodeType": "YulIdentifier", "src": "461:8:2"}, {"arguments": [{"name": "length", "nodeType": "YulIdentifier", "src": "475:6:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "483:4:2", "type": "", "value": "0x01"}], "functionName": {"name": "mul", "nodeType": "YulIdentifier", "src": "471:3:2"}, "nodeType": "YulFunctionCall", "src": "471:17:2"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "457:3:2"}, "nodeType": "YulFunctionCall", "src": "457:32:2"}, {"name": "end", "nodeType": "YulIdentifier", "src": "491:3:2"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "454:2:2"}, "nodeType": "YulFunctionCall", "src": "454:41:2"}, "nodeType": "YulIf", "src": "451:2:2"}]}, "name": "abi_decode_t_string_calldata_ptr", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "offset", "nodeType": "YulTypedName", "src": "208:6:2", "type": ""}, {"name": "end", "nodeType": "YulTypedName", "src": "216:3:2", "type": ""}], "returnVariables": [{"name": "arrayPos", "nodeType": "YulTypedName", "src": "224:8:2", "type": ""}, {"name": "length", "nodeType": "YulTypedName", "src": "234:6:2", "type": ""}], "src": "166:352:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "576:87:2", "statements": [{"nodeType": "YulAssignment", "src": "586:29:2", "value": {"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "608:6:2"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "595:12:2"}, "nodeType": "YulFunctionCall", "src": "595:20:2"}, "variableNames": [{"name": "value", "nodeType": "YulIdentifier", "src": "586:5:2"}]}, {"expression": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "651:5:2"}], "functionName": {"name": "validator_revert_t_uint256", "nodeType": "YulIdentifier", "src": "624:26:2"}, "nodeType": "YulFunctionCall", "src": "624:33:2"}, "nodeType": "YulExpressionStatement", "src": "624:33:2"}]}, "name": "abi_decode_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "offset", "nodeType": "YulTypedName", "src": "554:6:2", "type": ""}, {"name": "end", "nodeType": "YulTypedName", "src": "562:3:2", "type": ""}], "returnVariables": [{"name": "value", "nodeType": "YulTypedName", "src": "570:5:2", "type": ""}], "src": "524:139:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "735:196:2", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "781:16:2", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "790:1:2", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "793:1:2", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "783:6:2"}, "nodeType": "YulFunctionCall", "src": "783:12:2"}, "nodeType": "YulExpressionStatement", "src": "783:12:2"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nodeType": "YulIdentifier", "src": "756:7:2"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "765:9:2"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "752:3:2"}, "nodeType": "YulFunctionCall", "src": "752:23:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "777:2:2", "type": "", "value": "32"}], "functionName": {"name": "slt", "nodeType": "YulIdentifier", "src": "748:3:2"}, "nodeType": "YulFunctionCall", "src": "748:32:2"}, "nodeType": "YulIf", "src": "745:2:2"}, {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "807:117:2", "statements": [{"nodeType": "YulVariableDeclaration", "src": "822:15:2", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "836:1:2", "type": "", "value": "0"}, "variables": [{"name": "offset", "nodeType": "YulTypedName", "src": "826:6:2", "type": ""}]}, {"nodeType": "YulAssignment", "src": "851:63:2", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "886:9:2"}, {"name": "offset", "nodeType": "YulIdentifier", "src": "897:6:2"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "882:3:2"}, "nodeType": "YulFunctionCall", "src": "882:22:2"}, {"name": "dataEnd", "nodeType": "YulIdentifier", "src": "906:7:2"}], "functionName": {"name": "abi_decode_t_uint256", "nodeType": "YulIdentifier", "src": "861:20:2"}, "nodeType": "YulFunctionCall", "src": "861:53:2"}, "variableNames": [{"name": "value0", "nodeType": "YulIdentifier", "src": "851:6:2"}]}]}]}, "name": "abi_decode_tuple_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "705:9:2", "type": ""}, {"name": "dataEnd", "nodeType": "YulTypedName", "src": "716:7:2", "type": ""}], "returnVariables": [{"name": "value0", "nodeType": "YulTypedName", "src": "728:6:2", "type": ""}], "src": "669:262:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1020:324:2", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1066:16:2", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1075:1:2", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1078:1:2", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "1068:6:2"}, "nodeType": "YulFunctionCall", "src": "1068:12:2"}, "nodeType": "YulExpressionStatement", "src": "1068:12:2"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nodeType": "YulIdentifier", "src": "1041:7:2"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "1050:9:2"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "1037:3:2"}, "nodeType": "YulFunctionCall", "src": "1037:23:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1062:2:2", "type": "", "value": "64"}], "functionName": {"name": "slt", "nodeType": "YulIdentifier", "src": "1033:3:2"}, "nodeType": "YulFunctionCall", "src": "1033:32:2"}, "nodeType": "YulIf", "src": "1030:2:2"}, {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1092:117:2", "statements": [{"nodeType": "YulVariableDeclaration", "src": "1107:15:2", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1121:1:2", "type": "", "value": "0"}, "variables": [{"name": "offset", "nodeType": "YulTypedName", "src": "1111:6:2", "type": ""}]}, {"nodeType": "YulAssignment", "src": "1136:63:2", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1171:9:2"}, {"name": "offset", "nodeType": "YulIdentifier", "src": "1182:6:2"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1167:3:2"}, "nodeType": "YulFunctionCall", "src": "1167:22:2"}, {"name": "dataEnd", "nodeType": "YulIdentifier", "src": "1191:7:2"}], "functionName": {"name": "abi_decode_t_uint256", "nodeType": "YulIdentifier", "src": "1146:20:2"}, "nodeType": "YulFunctionCall", "src": "1146:53:2"}, "variableNames": [{"name": "value0", "nodeType": "YulIdentifier", "src": "1136:6:2"}]}]}, {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1219:118:2", "statements": [{"nodeType": "YulVariableDeclaration", "src": "1234:16:2", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1248:2:2", "type": "", "value": "32"}, "variables": [{"name": "offset", "nodeType": "YulTypedName", "src": "1238:6:2", "type": ""}]}, {"nodeType": "YulAssignment", "src": "1264:63:2", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1299:9:2"}, {"name": "offset", "nodeType": "YulIdentifier", "src": "1310:6:2"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1295:3:2"}, "nodeType": "YulFunctionCall", "src": "1295:22:2"}, {"name": "dataEnd", "nodeType": "YulIdentifier", "src": "1319:7:2"}], "functionName": {"name": "abi_decode_t_bytes32", "nodeType": "YulIdentifier", "src": "1274:20:2"}, "nodeType": "YulFunctionCall", "src": "1274:53:2"}, "variableNames": [{"name": "value1", "nodeType": "YulIdentifier", "src": "1264:6:2"}]}]}]}, "name": "abi_decode_tuple_t_uint256t_bytes32", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "982:9:2", "type": ""}, {"name": "dataEnd", "nodeType": "YulTypedName", "src": "993:7:2", "type": ""}], "returnVariables": [{"name": "value0", "nodeType": "YulTypedName", "src": "1005:6:2", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "1013:6:2", "type": ""}], "src": "937:407:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1527:920:2", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1574:16:2", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1583:1:2", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1586:1:2", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "1576:6:2"}, "nodeType": "YulFunctionCall", "src": "1576:12:2"}, "nodeType": "YulExpressionStatement", "src": "1576:12:2"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nodeType": "YulIdentifier", "src": "1548:7:2"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "1557:9:2"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "1544:3:2"}, "nodeType": "YulFunctionCall", "src": "1544:23:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1569:3:2", "type": "", "value": "128"}], "functionName": {"name": "slt", "nodeType": "YulIdentifier", "src": "1540:3:2"}, "nodeType": "YulFunctionCall", "src": "1540:33:2"}, "nodeType": "YulIf", "src": "1537:2:2"}, {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1600:117:2", "statements": [{"nodeType": "YulVariableDeclaration", "src": "1615:15:2", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1629:1:2", "type": "", "value": "0"}, "variables": [{"name": "offset", "nodeType": "YulTypedName", "src": "1619:6:2", "type": ""}]}, {"nodeType": "YulAssignment", "src": "1644:63:2", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1679:9:2"}, {"name": "offset", "nodeType": "YulIdentifier", "src": "1690:6:2"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1675:3:2"}, "nodeType": "YulFunctionCall", "src": "1675:22:2"}, {"name": "dataEnd", "nodeType": "YulIdentifier", "src": "1699:7:2"}], "functionName": {"name": "abi_decode_t_uint256", "nodeType": "YulIdentifier", "src": "1654:20:2"}, "nodeType": "YulFunctionCall", "src": "1654:53:2"}, "variableNames": [{"name": "value0", "nodeType": "YulIdentifier", "src": "1644:6:2"}]}]}, {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1727:231:2", "statements": [{"nodeType": "YulVariableDeclaration", "src": "1742:46:2", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1773:9:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1784:2:2", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1769:3:2"}, "nodeType": "YulFunctionCall", "src": "1769:18:2"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "1756:12:2"}, "nodeType": "YulFunctionCall", "src": "1756:32:2"}, "variables": [{"name": "offset", "nodeType": "YulTypedName", "src": "1746:6:2", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1835:16:2", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1844:1:2", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1847:1:2", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "1837:6:2"}, "nodeType": "YulFunctionCall", "src": "1837:12:2"}, "nodeType": "YulExpressionStatement", "src": "1837:12:2"}]}, "condition": {"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "1807:6:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1815:18:2", "type": "", "value": "0xffffffffffffffff"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "1804:2:2"}, "nodeType": "YulFunctionCall", "src": "1804:30:2"}, "nodeType": "YulIf", "src": "1801:2:2"}, {"nodeType": "YulAssignment", "src": "1865:83:2", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1920:9:2"}, {"name": "offset", "nodeType": "YulIdentifier", "src": "1931:6:2"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1916:3:2"}, "nodeType": "YulFunctionCall", "src": "1916:22:2"}, {"name": "dataEnd", "nodeType": "YulIdentifier", "src": "1940:7:2"}], "functionName": {"name": "abi_decode_t_string_calldata_ptr", "nodeType": "YulIdentifier", "src": "1883:32:2"}, "nodeType": "YulFunctionCall", "src": "1883:65:2"}, "variableNames": [{"name": "value1", "nodeType": "YulIdentifier", "src": "1865:6:2"}, {"name": "value2", "nodeType": "YulIdentifier", "src": "1873:6:2"}]}]}, {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1968:231:2", "statements": [{"nodeType": "YulVariableDeclaration", "src": "1983:46:2", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2014:9:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2025:2:2", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2010:3:2"}, "nodeType": "YulFunctionCall", "src": "2010:18:2"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "1997:12:2"}, "nodeType": "YulFunctionCall", "src": "1997:32:2"}, "variables": [{"name": "offset", "nodeType": "YulTypedName", "src": "1987:6:2", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2076:16:2", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2085:1:2", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2088:1:2", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "2078:6:2"}, "nodeType": "YulFunctionCall", "src": "2078:12:2"}, "nodeType": "YulExpressionStatement", "src": "2078:12:2"}]}, "condition": {"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "2048:6:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2056:18:2", "type": "", "value": "0xffffffffffffffff"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "2045:2:2"}, "nodeType": "YulFunctionCall", "src": "2045:30:2"}, "nodeType": "YulIf", "src": "2042:2:2"}, {"nodeType": "YulAssignment", "src": "2106:83:2", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2161:9:2"}, {"name": "offset", "nodeType": "YulIdentifier", "src": "2172:6:2"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2157:3:2"}, "nodeType": "YulFunctionCall", "src": "2157:22:2"}, {"name": "dataEnd", "nodeType": "YulIdentifier", "src": "2181:7:2"}], "functionName": {"name": "abi_decode_t_string_calldata_ptr", "nodeType": "YulIdentifier", "src": "2124:32:2"}, "nodeType": "YulFunctionCall", "src": "2124:65:2"}, "variableNames": [{"name": "value3", "nodeType": "YulIdentifier", "src": "2106:6:2"}, {"name": "value4", "nodeType": "YulIdentifier", "src": "2114:6:2"}]}]}, {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2209:231:2", "statements": [{"nodeType": "YulVariableDeclaration", "src": "2224:46:2", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2255:9:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2266:2:2", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2251:3:2"}, "nodeType": "YulFunctionCall", "src": "2251:18:2"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "2238:12:2"}, "nodeType": "YulFunctionCall", "src": "2238:32:2"}, "variables": [{"name": "offset", "nodeType": "YulTypedName", "src": "2228:6:2", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2317:16:2", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2326:1:2", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2329:1:2", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "2319:6:2"}, "nodeType": "YulFunctionCall", "src": "2319:12:2"}, "nodeType": "YulExpressionStatement", "src": "2319:12:2"}]}, "condition": {"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "2289:6:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2297:18:2", "type": "", "value": "0xffffffffffffffff"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "2286:2:2"}, "nodeType": "YulFunctionCall", "src": "2286:30:2"}, "nodeType": "YulIf", "src": "2283:2:2"}, {"nodeType": "YulAssignment", "src": "2347:83:2", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2402:9:2"}, {"name": "offset", "nodeType": "YulIdentifier", "src": "2413:6:2"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2398:3:2"}, "nodeType": "YulFunctionCall", "src": "2398:22:2"}, {"name": "dataEnd", "nodeType": "YulIdentifier", "src": "2422:7:2"}], "functionName": {"name": "abi_decode_t_string_calldata_ptr", "nodeType": "YulIdentifier", "src": "2365:32:2"}, "nodeType": "YulFunctionCall", "src": "2365:65:2"}, "variableNames": [{"name": "value5", "nodeType": "YulIdentifier", "src": "2347:6:2"}, {"name": "value6", "nodeType": "YulIdentifier", "src": "2355:6:2"}]}]}]}, "name": "abi_decode_tuple_t_uint256t_string_calldata_ptrt_string_calldata_ptrt_string_calldata_ptr", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "1449:9:2", "type": ""}, {"name": "dataEnd", "nodeType": "YulTypedName", "src": "1460:7:2", "type": ""}], "returnVariables": [{"name": "value0", "nodeType": "YulTypedName", "src": "1472:6:2", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "1480:6:2", "type": ""}, {"name": "value2", "nodeType": "YulTypedName", "src": "1488:6:2", "type": ""}, {"name": "value3", "nodeType": "YulTypedName", "src": "1496:6:2", "type": ""}, {"name": "value4", "nodeType": "YulTypedName", "src": "1504:6:2", "type": ""}, {"name": "value5", "nodeType": "YulTypedName", "src": "1512:6:2", "type": ""}, {"name": "value6", "nodeType": "YulTypedName", "src": "1520:6:2", "type": ""}], "src": "1350:1097:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2518:53:2", "statements": [{"expression": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "2535:3:2"}, {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "2558:5:2"}], "functionName": {"name": "cleanup_t_address", "nodeType": "YulIdentifier", "src": "2540:17:2"}, "nodeType": "YulFunctionCall", "src": "2540:24:2"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2528:6:2"}, "nodeType": "YulFunctionCall", "src": "2528:37:2"}, "nodeType": "YulExpressionStatement", "src": "2528:37:2"}]}, "name": "abi_encode_t_address_to_t_address_fromStack", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "2506:5:2", "type": ""}, {"name": "pos", "nodeType": "YulTypedName", "src": "2513:3:2", "type": ""}], "src": "2453:118:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2636:50:2", "statements": [{"expression": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "2653:3:2"}, {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "2673:5:2"}], "functionName": {"name": "cleanup_t_bool", "nodeType": "YulIdentifier", "src": "2658:14:2"}, "nodeType": "YulFunctionCall", "src": "2658:21:2"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2646:6:2"}, "nodeType": "YulFunctionCall", "src": "2646:34:2"}, "nodeType": "YulExpressionStatement", "src": "2646:34:2"}]}, "name": "abi_encode_t_bool_to_t_bool_fromStack", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "2624:5:2", "type": ""}, {"name": "pos", "nodeType": "YulTypedName", "src": "2631:3:2", "type": ""}], "src": "2577:109:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2832:196:2", "statements": [{"nodeType": "YulAssignment", "src": "2842:95:2", "value": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "2925:3:2"}, {"name": "length", "nodeType": "YulIdentifier", "src": "2930:6:2"}], "functionName": {"name": "array_storeLengthForEncoding_t_bytes_memory_ptr_nonPadded_inplace_fromStack", "nodeType": "YulIdentifier", "src": "2849:75:2"}, "nodeType": "YulFunctionCall", "src": "2849:88:2"}, "variableNames": [{"name": "pos", "nodeType": "YulIdentifier", "src": "2842:3:2"}]}, {"expression": {"arguments": [{"name": "start", "nodeType": "YulIdentifier", "src": "2971:5:2"}, {"name": "pos", "nodeType": "YulIdentifier", "src": "2978:3:2"}, {"name": "length", "nodeType": "YulIdentifier", "src": "2983:6:2"}], "functionName": {"name": "copy_calldata_to_memory", "nodeType": "YulIdentifier", "src": "2947:23:2"}, "nodeType": "YulFunctionCall", "src": "2947:43:2"}, "nodeType": "YulExpressionStatement", "src": "2947:43:2"}, {"nodeType": "YulAssignment", "src": "2999:23:2", "value": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "3010:3:2"}, {"name": "length", "nodeType": "YulIdentifier", "src": "3015:6:2"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3006:3:2"}, "nodeType": "YulFunctionCall", "src": "3006:16:2"}, "variableNames": [{"name": "end", "nodeType": "YulIdentifier", "src": "2999:3:2"}]}]}, "name": "abi_encode_t_bytes_calldata_ptr_to_t_bytes_memory_ptr_nonPadded_inplace_fromStack", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "start", "nodeType": "YulTypedName", "src": "2805:5:2", "type": ""}, {"name": "length", "nodeType": "YulTypedName", "src": "2812:6:2", "type": ""}, {"name": "pos", "nodeType": "YulTypedName", "src": "2820:3:2", "type": ""}], "returnVariables": [{"name": "end", "nodeType": "YulTypedName", "src": "2828:3:2", "type": ""}], "src": "2714:314:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3160:202:2", "statements": [{"nodeType": "YulAssignment", "src": "3170:78:2", "value": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "3236:3:2"}, {"name": "length", "nodeType": "YulIdentifier", "src": "3241:6:2"}], "functionName": {"name": "array_storeLengthForEncoding_t_string_memory_ptr_fromStack", "nodeType": "YulIdentifier", "src": "3177:58:2"}, "nodeType": "YulFunctionCall", "src": "3177:71:2"}, "variableNames": [{"name": "pos", "nodeType": "YulIdentifier", "src": "3170:3:2"}]}, {"expression": {"arguments": [{"name": "start", "nodeType": "YulIdentifier", "src": "3282:5:2"}, {"name": "pos", "nodeType": "YulIdentifier", "src": "3289:3:2"}, {"name": "length", "nodeType": "YulIdentifier", "src": "3294:6:2"}], "functionName": {"name": "copy_calldata_to_memory", "nodeType": "YulIdentifier", "src": "3258:23:2"}, "nodeType": "YulFunctionCall", "src": "3258:43:2"}, "nodeType": "YulExpressionStatement", "src": "3258:43:2"}, {"nodeType": "YulAssignment", "src": "3310:46:2", "value": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "3321:3:2"}, {"arguments": [{"name": "length", "nodeType": "YulIdentifier", "src": "3348:6:2"}], "functionName": {"name": "round_up_to_mul_of_32", "nodeType": "YulIdentifier", "src": "3326:21:2"}, "nodeType": "YulFunctionCall", "src": "3326:29:2"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3317:3:2"}, "nodeType": "YulFunctionCall", "src": "3317:39:2"}, "variableNames": [{"name": "end", "nodeType": "YulIdentifier", "src": "3310:3:2"}]}]}, "name": "abi_encode_t_string_calldata_ptr_to_t_string_memory_ptr_fromStack", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "start", "nodeType": "YulTypedName", "src": "3133:5:2", "type": ""}, {"name": "length", "nodeType": "YulTypedName", "src": "3140:6:2", "type": ""}, {"name": "pos", "nodeType": "YulTypedName", "src": "3148:3:2", "type": ""}], "returnVariables": [{"name": "end", "nodeType": "YulTypedName", "src": "3156:3:2", "type": ""}], "src": "3058:304:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3512:197:2", "statements": [{"nodeType": "YulAssignment", "src": "3522:96:2", "value": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "3606:3:2"}, {"name": "length", "nodeType": "YulIdentifier", "src": "3611:6:2"}], "functionName": {"name": "array_storeLengthForEncoding_t_string_memory_ptr_nonPadded_inplace_fromStack", "nodeType": "YulIdentifier", "src": "3529:76:2"}, "nodeType": "YulFunctionCall", "src": "3529:89:2"}, "variableNames": [{"name": "pos", "nodeType": "YulIdentifier", "src": "3522:3:2"}]}, {"expression": {"arguments": [{"name": "start", "nodeType": "YulIdentifier", "src": "3652:5:2"}, {"name": "pos", "nodeType": "YulIdentifier", "src": "3659:3:2"}, {"name": "length", "nodeType": "YulIdentifier", "src": "3664:6:2"}], "functionName": {"name": "copy_calldata_to_memory", "nodeType": "YulIdentifier", "src": "3628:23:2"}, "nodeType": "YulFunctionCall", "src": "3628:43:2"}, "nodeType": "YulExpressionStatement", "src": "3628:43:2"}, {"nodeType": "YulAssignment", "src": "3680:23:2", "value": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "3691:3:2"}, {"name": "length", "nodeType": "YulIdentifier", "src": "3696:6:2"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3687:3:2"}, "nodeType": "YulFunctionCall", "src": "3687:16:2"}, "variableNames": [{"name": "end", "nodeType": "YulIdentifier", "src": "3680:3:2"}]}]}, "name": "abi_encode_t_string_calldata_ptr_to_t_string_memory_ptr_nonPadded_inplace_fromStack", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "start", "nodeType": "YulTypedName", "src": "3485:5:2", "type": ""}, {"name": "length", "nodeType": "YulTypedName", "src": "3492:6:2", "type": ""}, {"name": "pos", "nodeType": "YulTypedName", "src": "3500:3:2", "type": ""}], "returnVariables": [{"name": "end", "nodeType": "YulTypedName", "src": "3508:3:2", "type": ""}], "src": "3392:317:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3807:272:2", "statements": [{"nodeType": "YulVariableDeclaration", "src": "3817:53:2", "value": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "3864:5:2"}], "functionName": {"name": "array_length_t_string_memory_ptr", "nodeType": "YulIdentifier", "src": "3831:32:2"}, "nodeType": "YulFunctionCall", "src": "3831:39:2"}, "variables": [{"name": "length", "nodeType": "YulTypedName", "src": "3821:6:2", "type": ""}]}, {"nodeType": "YulAssignment", "src": "3879:78:2", "value": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "3945:3:2"}, {"name": "length", "nodeType": "YulIdentifier", "src": "3950:6:2"}], "functionName": {"name": "array_storeLengthForEncoding_t_string_memory_ptr_fromStack", "nodeType": "YulIdentifier", "src": "3886:58:2"}, "nodeType": "YulFunctionCall", "src": "3886:71:2"}, "variableNames": [{"name": "pos", "nodeType": "YulIdentifier", "src": "3879:3:2"}]}, {"expression": {"arguments": [{"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "3992:5:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3999:4:2", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3988:3:2"}, "nodeType": "YulFunctionCall", "src": "3988:16:2"}, {"name": "pos", "nodeType": "YulIdentifier", "src": "4006:3:2"}, {"name": "length", "nodeType": "YulIdentifier", "src": "4011:6:2"}], "functionName": {"name": "copy_memory_to_memory", "nodeType": "YulIdentifier", "src": "3966:21:2"}, "nodeType": "YulFunctionCall", "src": "3966:52:2"}, "nodeType": "YulExpressionStatement", "src": "3966:52:2"}, {"nodeType": "YulAssignment", "src": "4027:46:2", "value": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "4038:3:2"}, {"arguments": [{"name": "length", "nodeType": "YulIdentifier", "src": "4065:6:2"}], "functionName": {"name": "round_up_to_mul_of_32", "nodeType": "YulIdentifier", "src": "4043:21:2"}, "nodeType": "YulFunctionCall", "src": "4043:29:2"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4034:3:2"}, "nodeType": "YulFunctionCall", "src": "4034:39:2"}, "variableNames": [{"name": "end", "nodeType": "YulIdentifier", "src": "4027:3:2"}]}]}, "name": "abi_encode_t_string_memory_ptr_to_t_string_memory_ptr_fromStack", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "3788:5:2", "type": ""}, {"name": "pos", "nodeType": "YulTypedName", "src": "3795:3:2", "type": ""}], "returnVariables": [{"name": "end", "nodeType": "YulTypedName", "src": "3803:3:2", "type": ""}], "src": "3715:364:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4231:171:2", "statements": [{"nodeType": "YulAssignment", "src": "4241:74:2", "value": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "4307:3:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4312:2:2", "type": "", "value": "19"}], "functionName": {"name": "array_storeLengthForEncoding_t_string_memory_ptr_fromStack", "nodeType": "YulIdentifier", "src": "4248:58:2"}, "nodeType": "YulFunctionCall", "src": "4248:67:2"}, "variableNames": [{"name": "pos", "nodeType": "YulIdentifier", "src": "4241:3:2"}]}, {"expression": {"arguments": [{"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "4336:3:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4341:1:2", "type": "", "value": "0"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4332:3:2"}, "nodeType": "YulFunctionCall", "src": "4332:11:2"}, {"kind": "string", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4345:21:2", "type": "", "value": "Index out of bounds"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "4325:6:2"}, "nodeType": "YulFunctionCall", "src": "4325:42:2"}, "nodeType": "YulExpressionStatement", "src": "4325:42:2"}, {"nodeType": "YulAssignment", "src": "4377:19:2", "value": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "4388:3:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4393:2:2", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4384:3:2"}, "nodeType": "YulFunctionCall", "src": "4384:12:2"}, "variableNames": [{"name": "end", "nodeType": "YulIdentifier", "src": "4377:3:2"}]}]}, "name": "abi_encode_t_stringliteral_dd00b67a545791a54dd99d9c09eb42099756ea4ee2bd47188784c22234589367_to_t_string_memory_ptr_fromStack", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "pos", "nodeType": "YulTypedName", "src": "4219:3:2", "type": ""}], "returnVariables": [{"name": "end", "nodeType": "YulTypedName", "src": "4227:3:2", "type": ""}], "src": "4085:317:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4554:227:2", "statements": [{"nodeType": "YulAssignment", "src": "4564:74:2", "value": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "4630:3:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4635:2:2", "type": "", "value": "41"}], "functionName": {"name": "array_storeLengthForEncoding_t_string_memory_ptr_fromStack", "nodeType": "YulIdentifier", "src": "4571:58:2"}, "nodeType": "YulFunctionCall", "src": "4571:67:2"}, "variableNames": [{"name": "pos", "nodeType": "YulIdentifier", "src": "4564:3:2"}]}, {"expression": {"arguments": [{"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "4659:3:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4664:1:2", "type": "", "value": "0"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4655:3:2"}, "nodeType": "YulFunctionCall", "src": "4655:11:2"}, {"kind": "string", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4668:34:2", "type": "", "value": "Already marked attendance for th"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "4648:6:2"}, "nodeType": "YulFunctionCall", "src": "4648:55:2"}, "nodeType": "YulExpressionStatement", "src": "4648:55:2"}, {"expression": {"arguments": [{"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "4724:3:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4729:2:2", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4720:3:2"}, "nodeType": "YulFunctionCall", "src": "4720:12:2"}, {"kind": "string", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4734:11:2", "type": "", "value": "is matric"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "4713:6:2"}, "nodeType": "YulFunctionCall", "src": "4713:33:2"}, "nodeType": "YulExpressionStatement", "src": "4713:33:2"}, {"nodeType": "YulAssignment", "src": "4756:19:2", "value": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "4767:3:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4772:2:2", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4763:3:2"}, "nodeType": "YulFunctionCall", "src": "4763:12:2"}, "variableNames": [{"name": "end", "nodeType": "YulIdentifier", "src": "4756:3:2"}]}]}, "name": "abi_encode_t_stringliteral_df4092a6585e16cbd7297e32b101e6ed160262f5c62bc08342cd783cc74d8a8c_to_t_string_memory_ptr_fromStack", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "pos", "nodeType": "YulTypedName", "src": "4542:3:2", "type": ""}], "returnVariables": [{"name": "end", "nodeType": "YulTypedName", "src": "4550:3:2", "type": ""}], "src": "4408:373:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4852:53:2", "statements": [{"expression": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "4869:3:2"}, {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "4892:5:2"}], "functionName": {"name": "cleanup_t_uint256", "nodeType": "YulIdentifier", "src": "4874:17:2"}, "nodeType": "YulFunctionCall", "src": "4874:24:2"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "4862:6:2"}, "nodeType": "YulFunctionCall", "src": "4862:37:2"}, "nodeType": "YulExpressionStatement", "src": "4862:37:2"}]}, "name": "abi_encode_t_uint256_to_t_uint256_fromStack", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "4840:5:2", "type": ""}, {"name": "pos", "nodeType": "YulTypedName", "src": "4847:3:2", "type": ""}], "src": "4787:118:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5055:147:2", "statements": [{"nodeType": "YulAssignment", "src": "5066:110:2", "value": {"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "5155:6:2"}, {"name": "value1", "nodeType": "YulIdentifier", "src": "5163:6:2"}, {"name": "pos", "nodeType": "YulIdentifier", "src": "5172:3:2"}], "functionName": {"name": "abi_encode_t_bytes_calldata_ptr_to_t_bytes_memory_ptr_nonPadded_inplace_fromStack", "nodeType": "YulIdentifier", "src": "5073:81:2"}, "nodeType": "YulFunctionCall", "src": "5073:103:2"}, "variableNames": [{"name": "pos", "nodeType": "YulIdentifier", "src": "5066:3:2"}]}, {"nodeType": "YulAssignment", "src": "5186:10:2", "value": {"name": "pos", "nodeType": "YulIdentifier", "src": "5193:3:2"}, "variableNames": [{"name": "end", "nodeType": "YulIdentifier", "src": "5186:3:2"}]}]}, "name": "abi_encode_tuple_packed_t_bytes_calldata_ptr__to_t_bytes_memory_ptr__nonPadded_inplace_fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "pos", "nodeType": "YulTypedName", "src": "5026:3:2", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "5032:6:2", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "5040:6:2", "type": ""}], "returnVariables": [{"name": "end", "nodeType": "YulTypedName", "src": "5051:3:2", "type": ""}], "src": "4911:291:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5354:149:2", "statements": [{"nodeType": "YulAssignment", "src": "5365:112:2", "value": {"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "5456:6:2"}, {"name": "value1", "nodeType": "YulIdentifier", "src": "5464:6:2"}, {"name": "pos", "nodeType": "YulIdentifier", "src": "5473:3:2"}], "functionName": {"name": "abi_encode_t_string_calldata_ptr_to_t_string_memory_ptr_nonPadded_inplace_fromStack", "nodeType": "YulIdentifier", "src": "5372:83:2"}, "nodeType": "YulFunctionCall", "src": "5372:105:2"}, "variableNames": [{"name": "pos", "nodeType": "YulIdentifier", "src": "5365:3:2"}]}, {"nodeType": "YulAssignment", "src": "5487:10:2", "value": {"name": "pos", "nodeType": "YulIdentifier", "src": "5494:3:2"}, "variableNames": [{"name": "end", "nodeType": "YulIdentifier", "src": "5487:3:2"}]}]}, "name": "abi_encode_tuple_packed_t_string_calldata_ptr__to_t_string_memory_ptr__nonPadded_inplace_fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "pos", "nodeType": "YulTypedName", "src": "5325:3:2", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "5331:6:2", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "5339:6:2", "type": ""}], "returnVariables": [{"name": "end", "nodeType": "YulTypedName", "src": "5350:3:2", "type": ""}], "src": "5208:295:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5807:750:2", "statements": [{"nodeType": "YulAssignment", "src": "5817:27:2", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "5829:9:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5840:3:2", "type": "", "value": "192"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "5825:3:2"}, "nodeType": "YulFunctionCall", "src": "5825:19:2"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "5817:4:2"}]}, {"expression": {"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "5898:6:2"}, {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "5911:9:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5922:1:2", "type": "", "value": "0"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "5907:3:2"}, "nodeType": "YulFunctionCall", "src": "5907:17:2"}], "functionName": {"name": "abi_encode_t_address_to_t_address_fromStack", "nodeType": "YulIdentifier", "src": "5854:43:2"}, "nodeType": "YulFunctionCall", "src": "5854:71:2"}, "nodeType": "YulExpressionStatement", "src": "5854:71:2"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "5946:9:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5957:2:2", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "5942:3:2"}, "nodeType": "YulFunctionCall", "src": "5942:18:2"}, {"arguments": [{"name": "tail", "nodeType": "YulIdentifier", "src": "5966:4:2"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "5972:9:2"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "5962:3:2"}, "nodeType": "YulFunctionCall", "src": "5962:20:2"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "5935:6:2"}, "nodeType": "YulFunctionCall", "src": "5935:48:2"}, "nodeType": "YulExpressionStatement", "src": "5935:48:2"}, {"nodeType": "YulAssignment", "src": "5992:86:2", "value": {"arguments": [{"name": "value1", "nodeType": "YulIdentifier", "src": "6064:6:2"}, {"name": "tail", "nodeType": "YulIdentifier", "src": "6073:4:2"}], "functionName": {"name": "abi_encode_t_string_memory_ptr_to_t_string_memory_ptr_fromStack", "nodeType": "YulIdentifier", "src": "6000:63:2"}, "nodeType": "YulFunctionCall", "src": "6000:78:2"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "5992:4:2"}]}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "6099:9:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6110:2:2", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "6095:3:2"}, "nodeType": "YulFunctionCall", "src": "6095:18:2"}, {"arguments": [{"name": "tail", "nodeType": "YulIdentifier", "src": "6119:4:2"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "6125:9:2"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "6115:3:2"}, "nodeType": "YulFunctionCall", "src": "6115:20:2"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "6088:6:2"}, "nodeType": "YulFunctionCall", "src": "6088:48:2"}, "nodeType": "YulExpressionStatement", "src": "6088:48:2"}, {"nodeType": "YulAssignment", "src": "6145:86:2", "value": {"arguments": [{"name": "value2", "nodeType": "YulIdentifier", "src": "6217:6:2"}, {"name": "tail", "nodeType": "YulIdentifier", "src": "6226:4:2"}], "functionName": {"name": "abi_encode_t_string_memory_ptr_to_t_string_memory_ptr_fromStack", "nodeType": "YulIdentifier", "src": "6153:63:2"}, "nodeType": "YulFunctionCall", "src": "6153:78:2"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "6145:4:2"}]}, {"expression": {"arguments": [{"name": "value3", "nodeType": "YulIdentifier", "src": "6285:6:2"}, {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "6298:9:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6309:2:2", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "6294:3:2"}, "nodeType": "YulFunctionCall", "src": "6294:18:2"}], "functionName": {"name": "abi_encode_t_uint256_to_t_uint256_fromStack", "nodeType": "YulIdentifier", "src": "6241:43:2"}, "nodeType": "YulFunctionCall", "src": "6241:72:2"}, "nodeType": "YulExpressionStatement", "src": "6241:72:2"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "6334:9:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6345:3:2", "type": "", "value": "128"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "6330:3:2"}, "nodeType": "YulFunctionCall", "src": "6330:19:2"}, {"arguments": [{"name": "tail", "nodeType": "YulIdentifier", "src": "6355:4:2"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "6361:9:2"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "6351:3:2"}, "nodeType": "YulFunctionCall", "src": "6351:20:2"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "6323:6:2"}, "nodeType": "YulFunctionCall", "src": "6323:49:2"}, "nodeType": "YulExpressionStatement", "src": "6323:49:2"}, {"nodeType": "YulAssignment", "src": "6381:86:2", "value": {"arguments": [{"name": "value4", "nodeType": "YulIdentifier", "src": "6453:6:2"}, {"name": "tail", "nodeType": "YulIdentifier", "src": "6462:4:2"}], "functionName": {"name": "abi_encode_t_string_memory_ptr_to_t_string_memory_ptr_fromStack", "nodeType": "YulIdentifier", "src": "6389:63:2"}, "nodeType": "YulFunctionCall", "src": "6389:78:2"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "6381:4:2"}]}, {"expression": {"arguments": [{"name": "value5", "nodeType": "YulIdentifier", "src": "6521:6:2"}, {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "6534:9:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6545:3:2", "type": "", "value": "160"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "6530:3:2"}, "nodeType": "YulFunctionCall", "src": "6530:19:2"}], "functionName": {"name": "abi_encode_t_uint256_to_t_uint256_fromStack", "nodeType": "YulIdentifier", "src": "6477:43:2"}, "nodeType": "YulFunctionCall", "src": "6477:73:2"}, "nodeType": "YulExpressionStatement", "src": "6477:73:2"}]}, "name": "abi_encode_tuple_t_address_t_string_memory_ptr_t_string_memory_ptr_t_uint256_t_string_memory_ptr_t_uint256__to_t_address_t_string_memory_ptr_t_string_memory_ptr_t_uint256_t_string_memory_ptr_t_uint256__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "5739:9:2", "type": ""}, {"name": "value5", "nodeType": "YulTypedName", "src": "5751:6:2", "type": ""}, {"name": "value4", "nodeType": "YulTypedName", "src": "5759:6:2", "type": ""}, {"name": "value3", "nodeType": "YulTypedName", "src": "5767:6:2", "type": ""}, {"name": "value2", "nodeType": "YulTypedName", "src": "5775:6:2", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "5783:6:2", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "5791:6:2", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "5802:4:2", "type": ""}], "src": "5509:1048:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6655:118:2", "statements": [{"nodeType": "YulAssignment", "src": "6665:26:2", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "6677:9:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6688:2:2", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "6673:3:2"}, "nodeType": "YulFunctionCall", "src": "6673:18:2"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "6665:4:2"}]}, {"expression": {"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "6739:6:2"}, {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "6752:9:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6763:1:2", "type": "", "value": "0"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "6748:3:2"}, "nodeType": "YulFunctionCall", "src": "6748:17:2"}], "functionName": {"name": "abi_encode_t_bool_to_t_bool_fromStack", "nodeType": "YulIdentifier", "src": "6701:37:2"}, "nodeType": "YulFunctionCall", "src": "6701:65:2"}, "nodeType": "YulExpressionStatement", "src": "6701:65:2"}]}, "name": "abi_encode_tuple_t_bool__to_t_bool__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "6627:9:2", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "6639:6:2", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "6650:4:2", "type": ""}], "src": "6563:210:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6993:450:2", "statements": [{"nodeType": "YulAssignment", "src": "7003:26:2", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "7015:9:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7026:2:2", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "7011:3:2"}, "nodeType": "YulFunctionCall", "src": "7011:18:2"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "7003:4:2"}]}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "7050:9:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7061:1:2", "type": "", "value": "0"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "7046:3:2"}, "nodeType": "YulFunctionCall", "src": "7046:17:2"}, {"arguments": [{"name": "tail", "nodeType": "YulIdentifier", "src": "7069:4:2"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "7075:9:2"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "7065:3:2"}, "nodeType": "YulFunctionCall", "src": "7065:20:2"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "7039:6:2"}, "nodeType": "YulFunctionCall", "src": "7039:47:2"}, "nodeType": "YulExpressionStatement", "src": "7039:47:2"}, {"nodeType": "YulAssignment", "src": "7095:96:2", "value": {"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "7169:6:2"}, {"name": "value1", "nodeType": "YulIdentifier", "src": "7177:6:2"}, {"name": "tail", "nodeType": "YulIdentifier", "src": "7186:4:2"}], "functionName": {"name": "abi_encode_t_string_calldata_ptr_to_t_string_memory_ptr_fromStack", "nodeType": "YulIdentifier", "src": "7103:65:2"}, "nodeType": "YulFunctionCall", "src": "7103:88:2"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "7095:4:2"}]}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "7212:9:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7223:2:2", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "7208:3:2"}, "nodeType": "YulFunctionCall", "src": "7208:18:2"}, {"arguments": [{"name": "tail", "nodeType": "YulIdentifier", "src": "7232:4:2"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "7238:9:2"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "7228:3:2"}, "nodeType": "YulFunctionCall", "src": "7228:20:2"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "7201:6:2"}, "nodeType": "YulFunctionCall", "src": "7201:48:2"}, "nodeType": "YulExpressionStatement", "src": "7201:48:2"}, {"nodeType": "YulAssignment", "src": "7258:96:2", "value": {"arguments": [{"name": "value2", "nodeType": "YulIdentifier", "src": "7332:6:2"}, {"name": "value3", "nodeType": "YulIdentifier", "src": "7340:6:2"}, {"name": "tail", "nodeType": "YulIdentifier", "src": "7349:4:2"}], "functionName": {"name": "abi_encode_t_string_calldata_ptr_to_t_string_memory_ptr_fromStack", "nodeType": "YulIdentifier", "src": "7266:65:2"}, "nodeType": "YulFunctionCall", "src": "7266:88:2"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "7258:4:2"}]}, {"expression": {"arguments": [{"name": "value4", "nodeType": "YulIdentifier", "src": "7408:6:2"}, {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "7421:9:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7432:2:2", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "7417:3:2"}, "nodeType": "YulFunctionCall", "src": "7417:18:2"}], "functionName": {"name": "abi_encode_t_uint256_to_t_uint256_fromStack", "nodeType": "YulIdentifier", "src": "7364:43:2"}, "nodeType": "YulFunctionCall", "src": "7364:72:2"}, "nodeType": "YulExpressionStatement", "src": "7364:72:2"}]}, "name": "abi_encode_tuple_t_string_calldata_ptr_t_string_calldata_ptr_t_uint256__to_t_string_memory_ptr_t_string_memory_ptr_t_uint256__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "6933:9:2", "type": ""}, {"name": "value4", "nodeType": "YulTypedName", "src": "6945:6:2", "type": ""}, {"name": "value3", "nodeType": "YulTypedName", "src": "6953:6:2", "type": ""}, {"name": "value2", "nodeType": "YulTypedName", "src": "6961:6:2", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "6969:6:2", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "6977:6:2", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "6988:4:2", "type": ""}], "src": "6779:664:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7620:248:2", "statements": [{"nodeType": "YulAssignment", "src": "7630:26:2", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "7642:9:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7653:2:2", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "7638:3:2"}, "nodeType": "YulFunctionCall", "src": "7638:18:2"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "7630:4:2"}]}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "7677:9:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7688:1:2", "type": "", "value": "0"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "7673:3:2"}, "nodeType": "YulFunctionCall", "src": "7673:17:2"}, {"arguments": [{"name": "tail", "nodeType": "YulIdentifier", "src": "7696:4:2"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "7702:9:2"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "7692:3:2"}, "nodeType": "YulFunctionCall", "src": "7692:20:2"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "7666:6:2"}, "nodeType": "YulFunctionCall", "src": "7666:47:2"}, "nodeType": "YulExpressionStatement", "src": "7666:47:2"}, {"nodeType": "YulAssignment", "src": "7722:139:2", "value": {"arguments": [{"name": "tail", "nodeType": "YulIdentifier", "src": "7856:4:2"}], "functionName": {"name": "abi_encode_t_stringliteral_dd00b67a545791a54dd99d9c09eb42099756ea4ee2bd47188784c22234589367_to_t_string_memory_ptr_fromStack", "nodeType": "YulIdentifier", "src": "7730:124:2"}, "nodeType": "YulFunctionCall", "src": "7730:131:2"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "7722:4:2"}]}]}, "name": "abi_encode_tuple_t_stringliteral_dd00b67a545791a54dd99d9c09eb42099756ea4ee2bd47188784c22234589367__to_t_string_memory_ptr__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "7600:9:2", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "7615:4:2", "type": ""}], "src": "7449:419:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8045:248:2", "statements": [{"nodeType": "YulAssignment", "src": "8055:26:2", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "8067:9:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8078:2:2", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "8063:3:2"}, "nodeType": "YulFunctionCall", "src": "8063:18:2"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "8055:4:2"}]}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "8102:9:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8113:1:2", "type": "", "value": "0"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "8098:3:2"}, "nodeType": "YulFunctionCall", "src": "8098:17:2"}, {"arguments": [{"name": "tail", "nodeType": "YulIdentifier", "src": "8121:4:2"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "8127:9:2"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "8117:3:2"}, "nodeType": "YulFunctionCall", "src": "8117:20:2"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "8091:6:2"}, "nodeType": "YulFunctionCall", "src": "8091:47:2"}, "nodeType": "YulExpressionStatement", "src": "8091:47:2"}, {"nodeType": "YulAssignment", "src": "8147:139:2", "value": {"arguments": [{"name": "tail", "nodeType": "YulIdentifier", "src": "8281:4:2"}], "functionName": {"name": "abi_encode_t_stringliteral_df4092a6585e16cbd7297e32b101e6ed160262f5c62bc08342cd783cc74d8a8c_to_t_string_memory_ptr_fromStack", "nodeType": "YulIdentifier", "src": "8155:124:2"}, "nodeType": "YulFunctionCall", "src": "8155:131:2"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "8147:4:2"}]}]}, "name": "abi_encode_tuple_t_stringliteral_df4092a6585e16cbd7297e32b101e6ed160262f5c62bc08342cd783cc74d8a8c__to_t_string_memory_ptr__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "8025:9:2", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "8040:4:2", "type": ""}], "src": "7874:419:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8397:124:2", "statements": [{"nodeType": "YulAssignment", "src": "8407:26:2", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "8419:9:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8430:2:2", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "8415:3:2"}, "nodeType": "YulFunctionCall", "src": "8415:18:2"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "8407:4:2"}]}, {"expression": {"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "8487:6:2"}, {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "8500:9:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8511:1:2", "type": "", "value": "0"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "8496:3:2"}, "nodeType": "YulFunctionCall", "src": "8496:17:2"}], "functionName": {"name": "abi_encode_t_uint256_to_t_uint256_fromStack", "nodeType": "YulIdentifier", "src": "8443:43:2"}, "nodeType": "YulFunctionCall", "src": "8443:71:2"}, "nodeType": "YulExpressionStatement", "src": "8443:71:2"}]}, "name": "abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "8369:9:2", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "8381:6:2", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "8392:4:2", "type": ""}], "src": "8299:222:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8586:40:2", "statements": [{"nodeType": "YulAssignment", "src": "8597:22:2", "value": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "8613:5:2"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "8607:5:2"}, "nodeType": "YulFunctionCall", "src": "8607:12:2"}, "variableNames": [{"name": "length", "nodeType": "YulIdentifier", "src": "8597:6:2"}]}]}, "name": "array_length_t_string_memory_ptr", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "8569:5:2", "type": ""}], "returnVariables": [{"name": "length", "nodeType": "YulTypedName", "src": "8579:6:2", "type": ""}], "src": "8527:99:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8745:34:2", "statements": [{"nodeType": "YulAssignment", "src": "8755:18:2", "value": {"name": "pos", "nodeType": "YulIdentifier", "src": "8770:3:2"}, "variableNames": [{"name": "updated_pos", "nodeType": "YulIdentifier", "src": "8755:11:2"}]}]}, "name": "array_storeLengthForEncoding_t_bytes_memory_ptr_nonPadded_inplace_fromStack", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "pos", "nodeType": "YulTypedName", "src": "8717:3:2", "type": ""}, {"name": "length", "nodeType": "YulTypedName", "src": "8722:6:2", "type": ""}], "returnVariables": [{"name": "updated_pos", "nodeType": "YulTypedName", "src": "8733:11:2", "type": ""}], "src": "8632:147:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8881:73:2", "statements": [{"expression": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "8898:3:2"}, {"name": "length", "nodeType": "YulIdentifier", "src": "8903:6:2"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "8891:6:2"}, "nodeType": "YulFunctionCall", "src": "8891:19:2"}, "nodeType": "YulExpressionStatement", "src": "8891:19:2"}, {"nodeType": "YulAssignment", "src": "8919:29:2", "value": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "8938:3:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8943:4:2", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "8934:3:2"}, "nodeType": "YulFunctionCall", "src": "8934:14:2"}, "variableNames": [{"name": "updated_pos", "nodeType": "YulIdentifier", "src": "8919:11:2"}]}]}, "name": "array_storeLengthForEncoding_t_string_memory_ptr_fromStack", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "pos", "nodeType": "YulTypedName", "src": "8853:3:2", "type": ""}, {"name": "length", "nodeType": "YulTypedName", "src": "8858:6:2", "type": ""}], "returnVariables": [{"name": "updated_pos", "nodeType": "YulTypedName", "src": "8869:11:2", "type": ""}], "src": "8785:169:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9074:34:2", "statements": [{"nodeType": "YulAssignment", "src": "9084:18:2", "value": {"name": "pos", "nodeType": "YulIdentifier", "src": "9099:3:2"}, "variableNames": [{"name": "updated_pos", "nodeType": "YulIdentifier", "src": "9084:11:2"}]}]}, "name": "array_storeLengthForEncoding_t_string_memory_ptr_nonPadded_inplace_fromStack", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "pos", "nodeType": "YulTypedName", "src": "9046:3:2", "type": ""}, {"name": "length", "nodeType": "YulTypedName", "src": "9051:6:2", "type": ""}], "returnVariables": [{"name": "updated_pos", "nodeType": "YulTypedName", "src": "9062:11:2", "type": ""}], "src": "8960:148:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9159:51:2", "statements": [{"nodeType": "YulAssignment", "src": "9169:35:2", "value": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "9198:5:2"}], "functionName": {"name": "cleanup_t_uint160", "nodeType": "YulIdentifier", "src": "9180:17:2"}, "nodeType": "YulFunctionCall", "src": "9180:24:2"}, "variableNames": [{"name": "cleaned", "nodeType": "YulIdentifier", "src": "9169:7:2"}]}]}, "name": "cleanup_t_address", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "9141:5:2", "type": ""}], "returnVariables": [{"name": "cleaned", "nodeType": "YulTypedName", "src": "9151:7:2", "type": ""}], "src": "9114:96:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9258:48:2", "statements": [{"nodeType": "YulAssignment", "src": "9268:32:2", "value": {"arguments": [{"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "9293:5:2"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "9286:6:2"}, "nodeType": "YulFunctionCall", "src": "9286:13:2"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "9279:6:2"}, "nodeType": "YulFunctionCall", "src": "9279:21:2"}, "variableNames": [{"name": "cleaned", "nodeType": "YulIdentifier", "src": "9268:7:2"}]}]}, "name": "cleanup_t_bool", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "9240:5:2", "type": ""}], "returnVariables": [{"name": "cleaned", "nodeType": "YulTypedName", "src": "9250:7:2", "type": ""}], "src": "9216:90:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9357:32:2", "statements": [{"nodeType": "YulAssignment", "src": "9367:16:2", "value": {"name": "value", "nodeType": "YulIdentifier", "src": "9378:5:2"}, "variableNames": [{"name": "cleaned", "nodeType": "YulIdentifier", "src": "9367:7:2"}]}]}, "name": "cleanup_t_bytes32", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "9339:5:2", "type": ""}], "returnVariables": [{"name": "cleaned", "nodeType": "YulTypedName", "src": "9349:7:2", "type": ""}], "src": "9312:77:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9440:81:2", "statements": [{"nodeType": "YulAssignment", "src": "9450:65:2", "value": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "9465:5:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9472:42:2", "type": "", "value": "0xffffffffffffffffffffffffffffffffffffffff"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "9461:3:2"}, "nodeType": "YulFunctionCall", "src": "9461:54:2"}, "variableNames": [{"name": "cleaned", "nodeType": "YulIdentifier", "src": "9450:7:2"}]}]}, "name": "cleanup_t_uint160", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "9422:5:2", "type": ""}], "returnVariables": [{"name": "cleaned", "nodeType": "YulTypedName", "src": "9432:7:2", "type": ""}], "src": "9395:126:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9572:32:2", "statements": [{"nodeType": "YulAssignment", "src": "9582:16:2", "value": {"name": "value", "nodeType": "YulIdentifier", "src": "9593:5:2"}, "variableNames": [{"name": "cleaned", "nodeType": "YulIdentifier", "src": "9582:7:2"}]}]}, "name": "cleanup_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "9554:5:2", "type": ""}], "returnVariables": [{"name": "cleaned", "nodeType": "YulTypedName", "src": "9564:7:2", "type": ""}], "src": "9527:77:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9661:103:2", "statements": [{"expression": {"arguments": [{"name": "dst", "nodeType": "YulIdentifier", "src": "9684:3:2"}, {"name": "src", "nodeType": "YulIdentifier", "src": "9689:3:2"}, {"name": "length", "nodeType": "YulIdentifier", "src": "9694:6:2"}], "functionName": {"name": "calldatacopy", "nodeType": "YulIdentifier", "src": "9671:12:2"}, "nodeType": "YulFunctionCall", "src": "9671:30:2"}, "nodeType": "YulExpressionStatement", "src": "9671:30:2"}, {"expression": {"arguments": [{"arguments": [{"name": "dst", "nodeType": "YulIdentifier", "src": "9742:3:2"}, {"name": "length", "nodeType": "YulIdentifier", "src": "9747:6:2"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "9738:3:2"}, "nodeType": "YulFunctionCall", "src": "9738:16:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9756:1:2", "type": "", "value": "0"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "9731:6:2"}, "nodeType": "YulFunctionCall", "src": "9731:27:2"}, "nodeType": "YulExpressionStatement", "src": "9731:27:2"}]}, "name": "copy_calldata_to_memory", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "src", "nodeType": "YulTypedName", "src": "9643:3:2", "type": ""}, {"name": "dst", "nodeType": "YulTypedName", "src": "9648:3:2", "type": ""}, {"name": "length", "nodeType": "YulTypedName", "src": "9653:6:2", "type": ""}], "src": "9610:154:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9819:258:2", "statements": [{"nodeType": "YulVariableDeclaration", "src": "9829:10:2", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9838:1:2", "type": "", "value": "0"}, "variables": [{"name": "i", "nodeType": "YulTypedName", "src": "9833:1:2", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9898:63:2", "statements": [{"expression": {"arguments": [{"arguments": [{"name": "dst", "nodeType": "YulIdentifier", "src": "9923:3:2"}, {"name": "i", "nodeType": "YulIdentifier", "src": "9928:1:2"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "9919:3:2"}, "nodeType": "YulFunctionCall", "src": "9919:11:2"}, {"arguments": [{"arguments": [{"name": "src", "nodeType": "YulIdentifier", "src": "9942:3:2"}, {"name": "i", "nodeType": "YulIdentifier", "src": "9947:1:2"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "9938:3:2"}, "nodeType": "YulFunctionCall", "src": "9938:11:2"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "9932:5:2"}, "nodeType": "YulFunctionCall", "src": "9932:18:2"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "9912:6:2"}, "nodeType": "YulFunctionCall", "src": "9912:39:2"}, "nodeType": "YulExpressionStatement", "src": "9912:39:2"}]}, "condition": {"arguments": [{"name": "i", "nodeType": "YulIdentifier", "src": "9859:1:2"}, {"name": "length", "nodeType": "YulIdentifier", "src": "9862:6:2"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "9856:2:2"}, "nodeType": "YulFunctionCall", "src": "9856:13:2"}, "nodeType": "YulForLoop", "post": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9870:19:2", "statements": [{"nodeType": "YulAssignment", "src": "9872:15:2", "value": {"arguments": [{"name": "i", "nodeType": "YulIdentifier", "src": "9881:1:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9884:2:2", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "9877:3:2"}, "nodeType": "YulFunctionCall", "src": "9877:10:2"}, "variableNames": [{"name": "i", "nodeType": "YulIdentifier", "src": "9872:1:2"}]}]}, "pre": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9852:3:2", "statements": []}, "src": "9848:113:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9995:76:2", "statements": [{"expression": {"arguments": [{"arguments": [{"name": "dst", "nodeType": "YulIdentifier", "src": "10045:3:2"}, {"name": "length", "nodeType": "YulIdentifier", "src": "10050:6:2"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "10041:3:2"}, "nodeType": "YulFunctionCall", "src": "10041:16:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10059:1:2", "type": "", "value": "0"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "10034:6:2"}, "nodeType": "YulFunctionCall", "src": "10034:27:2"}, "nodeType": "YulExpressionStatement", "src": "10034:27:2"}]}, "condition": {"arguments": [{"name": "i", "nodeType": "YulIdentifier", "src": "9976:1:2"}, {"name": "length", "nodeType": "YulIdentifier", "src": "9979:6:2"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "9973:2:2"}, "nodeType": "YulFunctionCall", "src": "9973:13:2"}, "nodeType": "YulIf", "src": "9970:2:2"}]}, "name": "copy_memory_to_memory", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "src", "nodeType": "YulTypedName", "src": "9801:3:2", "type": ""}, {"name": "dst", "nodeType": "YulTypedName", "src": "9806:3:2", "type": ""}, {"name": "length", "nodeType": "YulTypedName", "src": "9811:6:2", "type": ""}], "src": "9770:307:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10134:269:2", "statements": [{"nodeType": "YulAssignment", "src": "10144:22:2", "value": {"arguments": [{"name": "data", "nodeType": "YulIdentifier", "src": "10158:4:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10164:1:2", "type": "", "value": "2"}], "functionName": {"name": "div", "nodeType": "YulIdentifier", "src": "10154:3:2"}, "nodeType": "YulFunctionCall", "src": "10154:12:2"}, "variableNames": [{"name": "length", "nodeType": "YulIdentifier", "src": "10144:6:2"}]}, {"nodeType": "YulVariableDeclaration", "src": "10175:38:2", "value": {"arguments": [{"name": "data", "nodeType": "YulIdentifier", "src": "10205:4:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10211:1:2", "type": "", "value": "1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "10201:3:2"}, "nodeType": "YulFunctionCall", "src": "10201:12:2"}, "variables": [{"name": "outOfPlaceEncoding", "nodeType": "YulTypedName", "src": "10179:18:2", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10252:51:2", "statements": [{"nodeType": "YulAssignment", "src": "10266:27:2", "value": {"arguments": [{"name": "length", "nodeType": "YulIdentifier", "src": "10280:6:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10288:4:2", "type": "", "value": "0x7f"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "10276:3:2"}, "nodeType": "YulFunctionCall", "src": "10276:17:2"}, "variableNames": [{"name": "length", "nodeType": "YulIdentifier", "src": "10266:6:2"}]}]}, "condition": {"arguments": [{"name": "outOfPlaceEncoding", "nodeType": "YulIdentifier", "src": "10232:18:2"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "10225:6:2"}, "nodeType": "YulFunctionCall", "src": "10225:26:2"}, "nodeType": "YulIf", "src": "10222:2:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10355:42:2", "statements": [{"expression": {"arguments": [], "functionName": {"name": "panic_error_0x22", "nodeType": "YulIdentifier", "src": "10369:16:2"}, "nodeType": "YulFunctionCall", "src": "10369:18:2"}, "nodeType": "YulExpressionStatement", "src": "10369:18:2"}]}, "condition": {"arguments": [{"name": "outOfPlaceEncoding", "nodeType": "YulIdentifier", "src": "10319:18:2"}, {"arguments": [{"name": "length", "nodeType": "YulIdentifier", "src": "10342:6:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10350:2:2", "type": "", "value": "32"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "10339:2:2"}, "nodeType": "YulFunctionCall", "src": "10339:14:2"}], "functionName": {"name": "eq", "nodeType": "YulIdentifier", "src": "10316:2:2"}, "nodeType": "YulFunctionCall", "src": "10316:38:2"}, "nodeType": "YulIf", "src": "10313:2:2"}]}, "name": "extract_byte_array_length", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "data", "nodeType": "YulTypedName", "src": "10118:4:2", "type": ""}], "returnVariables": [{"name": "length", "nodeType": "YulTypedName", "src": "10127:6:2", "type": ""}], "src": "10083:320:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10437:152:2", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10454:1:2", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10457:77:2", "type": "", "value": "35408467139433450592217433187231851964531694900788300625387963629091585785856"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "10447:6:2"}, "nodeType": "YulFunctionCall", "src": "10447:88:2"}, "nodeType": "YulExpressionStatement", "src": "10447:88:2"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10551:1:2", "type": "", "value": "4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10554:4:2", "type": "", "value": "0x22"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "10544:6:2"}, "nodeType": "YulFunctionCall", "src": "10544:15:2"}, "nodeType": "YulExpressionStatement", "src": "10544:15:2"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10575:1:2", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10578:4:2", "type": "", "value": "0x24"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "10568:6:2"}, "nodeType": "YulFunctionCall", "src": "10568:15:2"}, "nodeType": "YulExpressionStatement", "src": "10568:15:2"}]}, "name": "panic_error_0x22", "nodeType": "YulFunctionDefinition", "src": "10409:180:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10643:54:2", "statements": [{"nodeType": "YulAssignment", "src": "10653:38:2", "value": {"arguments": [{"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "10671:5:2"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10678:2:2", "type": "", "value": "31"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "10667:3:2"}, "nodeType": "YulFunctionCall", "src": "10667:14:2"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10687:2:2", "type": "", "value": "31"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "10683:3:2"}, "nodeType": "YulFunctionCall", "src": "10683:7:2"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "10663:3:2"}, "nodeType": "YulFunctionCall", "src": "10663:28:2"}, "variableNames": [{"name": "result", "nodeType": "YulIdentifier", "src": "10653:6:2"}]}]}, "name": "round_up_to_mul_of_32", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "10626:5:2", "type": ""}], "returnVariables": [{"name": "result", "nodeType": "YulTypedName", "src": "10636:6:2", "type": ""}], "src": "10595:102:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10746:79:2", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10803:16:2", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10812:1:2", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10815:1:2", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "10805:6:2"}, "nodeType": "YulFunctionCall", "src": "10805:12:2"}, "nodeType": "YulExpressionStatement", "src": "10805:12:2"}]}, "condition": {"arguments": [{"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "10769:5:2"}, {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "10794:5:2"}], "functionName": {"name": "cleanup_t_bytes32", "nodeType": "YulIdentifier", "src": "10776:17:2"}, "nodeType": "YulFunctionCall", "src": "10776:24:2"}], "functionName": {"name": "eq", "nodeType": "YulIdentifier", "src": "10766:2:2"}, "nodeType": "YulFunctionCall", "src": "10766:35:2"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "10759:6:2"}, "nodeType": "YulFunctionCall", "src": "10759:43:2"}, "nodeType": "YulIf", "src": "10756:2:2"}]}, "name": "validator_revert_t_bytes32", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "10739:5:2", "type": ""}], "src": "10703:122:2"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10874:79:2", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10931:16:2", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10940:1:2", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "10943:1:2", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "10933:6:2"}, "nodeType": "YulFunctionCall", "src": "10933:12:2"}, "nodeType": "YulExpressionStatement", "src": "10933:12:2"}]}, "condition": {"arguments": [{"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "10897:5:2"}, {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "10922:5:2"}], "functionName": {"name": "cleanup_t_uint256", "nodeType": "YulIdentifier", "src": "10904:17:2"}, "nodeType": "YulFunctionCall", "src": "10904:24:2"}], "functionName": {"name": "eq", "nodeType": "YulIdentifier", "src": "10894:2:2"}, "nodeType": "YulFunctionCall", "src": "10894:35:2"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "10887:6:2"}, "nodeType": "YulFunctionCall", "src": "10887:43:2"}, "nodeType": "YulIf", "src": "10884:2:2"}]}, "name": "validator_revert_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "10867:5:2", "type": ""}], "src": "10831:122:2"}]}, "contents": "{\n\n    function abi_decode_t_bytes32(offset, end) -> value {\n        value := calldataload(offset)\n        validator_revert_t_bytes32(value)\n    }\n\n    // string\n    function abi_decode_t_string_calldata_ptr(offset, end) -> arrayPos, length {\n        if iszero(slt(add(offset, 0x1f), end)) { revert(0, 0) }\n        length := calldataload(offset)\n        if gt(length, 0xffffffffffffffff) { revert(0, 0) }\n        arrayPos := add(offset, 0x20)\n        if gt(add(arrayPos, mul(length, 0x01)), end) { revert(0, 0) }\n    }\n\n    function abi_decode_t_uint256(offset, end) -> value {\n        value := calldataload(offset)\n        validator_revert_t_uint256(value)\n    }\n\n    function abi_decode_tuple_t_uint256(headStart, dataEnd) -> value0 {\n        if slt(sub(dataEnd, headStart), 32) { revert(0, 0) }\n\n        {\n\n            let offset := 0\n\n            value0 := abi_decode_t_uint256(add(headStart, offset), dataEnd)\n        }\n\n    }\n\n    function abi_decode_tuple_t_uint256t_bytes32(headStart, dataEnd) -> value0, value1 {\n        if slt(sub(dataEnd, headStart), 64) { revert(0, 0) }\n\n        {\n\n            let offset := 0\n\n            value0 := abi_decode_t_uint256(add(headStart, offset), dataEnd)\n        }\n\n        {\n\n            let offset := 32\n\n            value1 := abi_decode_t_bytes32(add(headStart, offset), dataEnd)\n        }\n\n    }\n\n    function abi_decode_tuple_t_uint256t_string_calldata_ptrt_string_calldata_ptrt_string_calldata_ptr(headStart, dataEnd) -> value0, value1, value2, value3, value4, value5, value6 {\n        if slt(sub(dataEnd, headStart), 128) { revert(0, 0) }\n\n        {\n\n            let offset := 0\n\n            value0 := abi_decode_t_uint256(add(headStart, offset), dataEnd)\n        }\n\n        {\n\n            let offset := calldataload(add(headStart, 32))\n            if gt(offset, 0xffffffffffffffff) { revert(0, 0) }\n\n            value1, value2 := abi_decode_t_string_calldata_ptr(add(headStart, offset), dataEnd)\n        }\n\n        {\n\n            let offset := calldataload(add(headStart, 64))\n            if gt(offset, 0xffffffffffffffff) { revert(0, 0) }\n\n            value3, value4 := abi_decode_t_string_calldata_ptr(add(headStart, offset), dataEnd)\n        }\n\n        {\n\n            let offset := calldataload(add(headStart, 96))\n            if gt(offset, 0xffffffffffffffff) { revert(0, 0) }\n\n            value5, value6 := abi_decode_t_string_calldata_ptr(add(headStart, offset), dataEnd)\n        }\n\n    }\n\n    function abi_encode_t_address_to_t_address_fromStack(value, pos) {\n        mstore(pos, cleanup_t_address(value))\n    }\n\n    function abi_encode_t_bool_to_t_bool_fromStack(value, pos) {\n        mstore(pos, cleanup_t_bool(value))\n    }\n\n    // bytes -> bytes\n    function abi_encode_t_bytes_calldata_ptr_to_t_bytes_memory_ptr_nonPadded_inplace_fromStack(start, length, pos) -> end {\n        pos := array_storeLengthForEncoding_t_bytes_memory_ptr_nonPadded_inplace_fromStack(pos, length)\n\n        copy_calldata_to_memory(start, pos, length)\n        end := add(pos, length)\n    }\n\n    // string -> string\n    function abi_encode_t_string_calldata_ptr_to_t_string_memory_ptr_fromStack(start, length, pos) -> end {\n        pos := array_storeLengthForEncoding_t_string_memory_ptr_fromStack(pos, length)\n\n        copy_calldata_to_memory(start, pos, length)\n        end := add(pos, round_up_to_mul_of_32(length))\n    }\n\n    // string -> string\n    function abi_encode_t_string_calldata_ptr_to_t_string_memory_ptr_nonPadded_inplace_fromStack(start, length, pos) -> end {\n        pos := array_storeLengthForEncoding_t_string_memory_ptr_nonPadded_inplace_fromStack(pos, length)\n\n        copy_calldata_to_memory(start, pos, length)\n        end := add(pos, length)\n    }\n\n    function abi_encode_t_string_memory_ptr_to_t_string_memory_ptr_fromStack(value, pos) -> end {\n        let length := array_length_t_string_memory_ptr(value)\n        pos := array_storeLengthForEncoding_t_string_memory_ptr_fromStack(pos, length)\n        copy_memory_to_memory(add(value, 0x20), pos, length)\n        end := add(pos, round_up_to_mul_of_32(length))\n    }\n\n    function abi_encode_t_stringliteral_dd00b67a545791a54dd99d9c09eb42099756ea4ee2bd47188784c22234589367_to_t_string_memory_ptr_fromStack(pos) -> end {\n        pos := array_storeLengthForEncoding_t_string_memory_ptr_fromStack(pos, 19)\n\n        mstore(add(pos, 0), \"Index out of bounds\")\n\n        end := add(pos, 32)\n    }\n\n    function abi_encode_t_stringliteral_df4092a6585e16cbd7297e32b101e6ed160262f5c62bc08342cd783cc74d8a8c_to_t_string_memory_ptr_fromStack(pos) -> end {\n        pos := array_storeLengthForEncoding_t_string_memory_ptr_fromStack(pos, 41)\n\n        mstore(add(pos, 0), \"Already marked attendance for th\")\n\n        mstore(add(pos, 32), \"is matric\")\n\n        end := add(pos, 64)\n    }\n\n    function abi_encode_t_uint256_to_t_uint256_fromStack(value, pos) {\n        mstore(pos, cleanup_t_uint256(value))\n    }\n\n    function abi_encode_tuple_packed_t_bytes_calldata_ptr__to_t_bytes_memory_ptr__nonPadded_inplace_fromStack_reversed(pos , value1, value0) -> end {\n\n        pos := abi_encode_t_bytes_calldata_ptr_to_t_bytes_memory_ptr_nonPadded_inplace_fromStack(value0, value1,  pos)\n\n        end := pos\n    }\n\n    function abi_encode_tuple_packed_t_string_calldata_ptr__to_t_string_memory_ptr__nonPadded_inplace_fromStack_reversed(pos , value1, value0) -> end {\n\n        pos := abi_encode_t_string_calldata_ptr_to_t_string_memory_ptr_nonPadded_inplace_fromStack(value0, value1,  pos)\n\n        end := pos\n    }\n\n    function abi_encode_tuple_t_address_t_string_memory_ptr_t_string_memory_ptr_t_uint256_t_string_memory_ptr_t_uint256__to_t_address_t_string_memory_ptr_t_string_memory_ptr_t_uint256_t_string_memory_ptr_t_uint256__fromStack_reversed(headStart , value5, value4, value3, value2, value1, value0) -> tail {\n        tail := add(headStart, 192)\n\n        abi_encode_t_address_to_t_address_fromStack(value0,  add(headStart, 0))\n\n        mstore(add(headStart, 32), sub(tail, headStart))\n        tail := abi_encode_t_string_memory_ptr_to_t_string_memory_ptr_fromStack(value1,  tail)\n\n        mstore(add(headStart, 64), sub(tail, headStart))\n        tail := abi_encode_t_string_memory_ptr_to_t_string_memory_ptr_fromStack(value2,  tail)\n\n        abi_encode_t_uint256_to_t_uint256_fromStack(value3,  add(headStart, 96))\n\n        mstore(add(headStart, 128), sub(tail, headStart))\n        tail := abi_encode_t_string_memory_ptr_to_t_string_memory_ptr_fromStack(value4,  tail)\n\n        abi_encode_t_uint256_to_t_uint256_fromStack(value5,  add(headStart, 160))\n\n    }\n\n    function abi_encode_tuple_t_bool__to_t_bool__fromStack_reversed(headStart , value0) -> tail {\n        tail := add(headStart, 32)\n\n        abi_encode_t_bool_to_t_bool_fromStack(value0,  add(headStart, 0))\n\n    }\n\n    function abi_encode_tuple_t_string_calldata_ptr_t_string_calldata_ptr_t_uint256__to_t_string_memory_ptr_t_string_memory_ptr_t_uint256__fromStack_reversed(headStart , value4, value3, value2, value1, value0) -> tail {\n        tail := add(headStart, 96)\n\n        mstore(add(headStart, 0), sub(tail, headStart))\n        tail := abi_encode_t_string_calldata_ptr_to_t_string_memory_ptr_fromStack(value0, value1,  tail)\n\n        mstore(add(headStart, 32), sub(tail, headStart))\n        tail := abi_encode_t_string_calldata_ptr_to_t_string_memory_ptr_fromStack(value2, value3,  tail)\n\n        abi_encode_t_uint256_to_t_uint256_fromStack(value4,  add(headStart, 64))\n\n    }\n\n    function abi_encode_tuple_t_stringliteral_dd00b67a545791a54dd99d9c09eb42099756ea4ee2bd47188784c22234589367__to_t_string_memory_ptr__fromStack_reversed(headStart ) -> tail {\n        tail := add(headStart, 32)\n\n        mstore(add(headStart, 0), sub(tail, headStart))\n        tail := abi_encode_t_stringliteral_dd00b67a545791a54dd99d9c09eb42099756ea4ee2bd47188784c22234589367_to_t_string_memory_ptr_fromStack( tail)\n\n    }\n\n    function abi_encode_tuple_t_stringliteral_df4092a6585e16cbd7297e32b101e6ed160262f5c62bc08342cd783cc74d8a8c__to_t_string_memory_ptr__fromStack_reversed(headStart ) -> tail {\n        tail := add(headStart, 32)\n\n        mstore(add(headStart, 0), sub(tail, headStart))\n        tail := abi_encode_t_stringliteral_df4092a6585e16cbd7297e32b101e6ed160262f5c62bc08342cd783cc74d8a8c_to_t_string_memory_ptr_fromStack( tail)\n\n    }\n\n    function abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed(headStart , value0) -> tail {\n        tail := add(headStart, 32)\n\n        abi_encode_t_uint256_to_t_uint256_fromStack(value0,  add(headStart, 0))\n\n    }\n\n    function array_length_t_string_memory_ptr(value) -> length {\n\n        length := mload(value)\n\n    }\n\n    function array_storeLengthForEncoding_t_bytes_memory_ptr_nonPadded_inplace_fromStack(pos, length) -> updated_pos {\n        updated_pos := pos\n    }\n\n    function array_storeLengthForEncoding_t_string_memory_ptr_fromStack(pos, length) -> updated_pos {\n        mstore(pos, length)\n        updated_pos := add(pos, 0x20)\n    }\n\n    function array_storeLengthForEncoding_t_string_memory_ptr_nonPadded_inplace_fromStack(pos, length) -> updated_pos {\n        updated_pos := pos\n    }\n\n    function cleanup_t_address(value) -> cleaned {\n        cleaned := cleanup_t_uint160(value)\n    }\n\n    function cleanup_t_bool(value) -> cleaned {\n        cleaned := iszero(iszero(value))\n    }\n\n    function cleanup_t_bytes32(value) -> cleaned {\n        cleaned := value\n    }\n\n    function cleanup_t_uint160(value) -> cleaned {\n        cleaned := and(value, 0xffffffffffffffffffffffffffffffffffffffff)\n    }\n\n    function cleanup_t_uint256(value) -> cleaned {\n        cleaned := value\n    }\n\n    function copy_calldata_to_memory(src, dst, length) {\n        calldatacopy(dst, src, length)\n        // clear end\n        mstore(add(dst, length), 0)\n    }\n\n    function copy_memory_to_memory(src, dst, length) {\n        let i := 0\n        for { } lt(i, length) { i := add(i, 32) }\n        {\n            mstore(add(dst, i), mload(add(src, i)))\n        }\n        if gt(i, length)\n        {\n            // clear end\n            mstore(add(dst, length), 0)\n        }\n    }\n\n    function extract_byte_array_length(data) -> length {\n        length := div(data, 2)\n        let outOfPlaceEncoding := and(data, 1)\n        if iszero(outOfPlaceEncoding) {\n            length := and(length, 0x7f)\n        }\n\n        if eq(outOfPlaceEncoding, lt(length, 32)) {\n            panic_error_0x22()\n        }\n    }\n\n    function panic_error_0x22() {\n        mstore(0, 35408467139433450592217433187231851964531694900788300625387963629091585785856)\n        mstore(4, 0x22)\n        revert(0, 0x24)\n    }\n\n    function round_up_to_mul_of_32(value) -> result {\n        result := and(add(value, 31), not(31))\n    }\n\n    function validator_revert_t_bytes32(value) {\n        if iszero(eq(value, cleanup_t_bytes32(value))) { revert(0, 0) }\n    }\n\n    function validator_revert_t_uint256(value) {\n        if iszero(eq(value, cleanup_t_uint256(value))) { revert(0, 0) }\n    }\n\n}\n", "id": 2, "language": "<PERSON>l", "name": "#utility.yul"}], "sourceMap": "208:2725:0:-:0;;;;;;;;;;;;;;;;;;;", "deployedSourceMap": "208:2725:0:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2324:606;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;;;;;;:::i;:::-;;;;;;;;548:63;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;658:23;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;;;;;;:::i;:::-;;;;;;;;1201:895;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;2157:98;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;2324:606;2420:15;2450:23;2488:25;2528:16;2559:24;2598:17;2657:7;:14;;;;2651:3;:20;2643:52;;;;;;;;;;;;:::i;:::-;;;;;;;;;2706:16;2725:7;2733:3;2725:12;;;;;;;;;;;;;;;;;;;;;;;;;;2706:31;;2770:1;:9;;;;;;;;;;;;2794:1;:11;;2820:1;:13;;2848:1;:10;;;2873:1;:12;;2900:1;:11;;;2748:174;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2324:606;;;;;;;:::o;548:63::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;658:23::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;1201:895::-;1395:11;1425:9;;1409:27;;;;;;;:::i;:::-;;;;;;;;1395:41;;1470:11;:21;1482:8;1470:21;;;;;;;;;;;:26;1492:3;1470:26;;;;;;;;;;;;;;;;;;;;;1469:27;1447:118;;;;;;;;;;;;:::i;:::-;;;;;;;;;1607:4;1578:11;:21;1590:8;1578:21;;;;;;;;;;;:26;1600:3;1578:26;;;;;;;;;;;;:33;;;;;;;;;;;;;;;;;;1624:7;1637:250;;;;;;;;1672:10;1637:250;;;;;;1710:9;;1637:250;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1747:11;;1637:250;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1786:8;1637:250;;;;1822:10;;1637:250;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1860:15;1637:250;;;1624:264;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;1988:8;1964:9;;1906:182;;;;;;;:::i;:::-;;;;;;;;1939:10;1906:182;;;2011:11;;2037:10;;2062:15;1906:182;;;;;;;;;;:::i;:::-;;;;;;;;1201:895;;;;;;;;:::o;2157:98::-;2206:7;2233;:14;;;;2226:21;;2157:98;:::o;-1:-1:-1:-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;:::o;7:139:2:-;;91:6;78:20;69:29;;107:33;134:5;107:33;:::i;:::-;59:87;;;;:::o;166:352::-;;;284:3;277:4;269:6;265:17;261:27;251:2;;302:1;299;292:12;251:2;338:6;325:20;315:30;;368:18;360:6;357:30;354:2;;;400:1;397;390:12;354:2;437:4;429:6;425:17;413:29;;491:3;483:4;475:6;471:17;461:8;457:32;454:41;451:2;;;508:1;505;498:12;451:2;241:277;;;;;:::o;524:139::-;;608:6;595:20;586:29;;624:33;651:5;624:33;:::i;:::-;576:87;;;;:::o;669:262::-;;777:2;765:9;756:7;752:23;748:32;745:2;;;793:1;790;783:12;745:2;836:1;861:53;906:7;897:6;886:9;882:22;861:53;:::i;:::-;851:63;;807:117;735:196;;;;:::o;937:407::-;;;1062:2;1050:9;1041:7;1037:23;1033:32;1030:2;;;1078:1;1075;1068:12;1030:2;1121:1;1146:53;1191:7;1182:6;1171:9;1167:22;1146:53;:::i;:::-;1136:63;;1092:117;1248:2;1274:53;1319:7;1310:6;1299:9;1295:22;1274:53;:::i;:::-;1264:63;;1219:118;1020:324;;;;;:::o;1350:1097::-;;;;;;;;1569:3;1557:9;1548:7;1544:23;1540:33;1537:2;;;1586:1;1583;1576:12;1537:2;1629:1;1654:53;1699:7;1690:6;1679:9;1675:22;1654:53;:::i;:::-;1644:63;;1600:117;1784:2;1773:9;1769:18;1756:32;1815:18;1807:6;1804:30;1801:2;;;1847:1;1844;1837:12;1801:2;1883:65;1940:7;1931:6;1920:9;1916:22;1883:65;:::i;:::-;1865:83;;;;1727:231;2025:2;2014:9;2010:18;1997:32;2056:18;2048:6;2045:30;2042:2;;;2088:1;2085;2078:12;2042:2;2124:65;2181:7;2172:6;2161:9;2157:22;2124:65;:::i;:::-;2106:83;;;;1968:231;2266:2;2255:9;2251:18;2238:32;2297:18;2289:6;2286:30;2283:2;;;2329:1;2326;2319:12;2283:2;2365:65;2422:7;2413:6;2402:9;2398:22;2365:65;:::i;:::-;2347:83;;;;2209:231;1527:920;;;;;;;;;;:::o;2453:118::-;2540:24;2558:5;2540:24;:::i;:::-;2535:3;2528:37;2518:53;;:::o;2577:109::-;2658:21;2673:5;2658:21;:::i;:::-;2653:3;2646:34;2636:50;;:::o;2714:314::-;;2849:88;2930:6;2925:3;2849:88;:::i;:::-;2842:95;;2947:43;2983:6;2978:3;2971:5;2947:43;:::i;:::-;3015:6;3010:3;3006:16;2999:23;;2832:196;;;;;:::o;3058:304::-;;3177:71;3241:6;3236:3;3177:71;:::i;:::-;3170:78;;3258:43;3294:6;3289:3;3282:5;3258:43;:::i;:::-;3326:29;3348:6;3326:29;:::i;:::-;3321:3;3317:39;3310:46;;3160:202;;;;;:::o;3392:317::-;;3529:89;3611:6;3606:3;3529:89;:::i;:::-;3522:96;;3628:43;3664:6;3659:3;3652:5;3628:43;:::i;:::-;3696:6;3691:3;3687:16;3680:23;;3512:197;;;;;:::o;3715:364::-;;3831:39;3864:5;3831:39;:::i;:::-;3886:71;3950:6;3945:3;3886:71;:::i;:::-;3879:78;;3966:52;4011:6;4006:3;3999:4;3992:5;3988:16;3966:52;:::i;:::-;4043:29;4065:6;4043:29;:::i;:::-;4038:3;4034:39;4027:46;;3807:272;;;;;:::o;4085:317::-;;4248:67;4312:2;4307:3;4248:67;:::i;:::-;4241:74;;4345:21;4341:1;4336:3;4332:11;4325:42;4393:2;4388:3;4384:12;4377:19;;4231:171;;;:::o;4408:373::-;;4571:67;4635:2;4630:3;4571:67;:::i;:::-;4564:74;;4668:34;4664:1;4659:3;4655:11;4648:55;4734:11;4729:2;4724:3;4720:12;4713:33;4772:2;4767:3;4763:12;4756:19;;4554:227;;;:::o;4787:118::-;4874:24;4892:5;4874:24;:::i;:::-;4869:3;4862:37;4852:53;;:::o;4911:291::-;;5073:103;5172:3;5163:6;5155;5073:103;:::i;:::-;5066:110;;5193:3;5186:10;;5055:147;;;;;:::o;5208:295::-;;5372:105;5473:3;5464:6;5456;5372:105;:::i;:::-;5365:112;;5494:3;5487:10;;5354:149;;;;;:::o;5509:1048::-;;5840:3;5829:9;5825:19;5817:27;;5854:71;5922:1;5911:9;5907:17;5898:6;5854:71;:::i;:::-;5972:9;5966:4;5962:20;5957:2;5946:9;5942:18;5935:48;6000:78;6073:4;6064:6;6000:78;:::i;:::-;5992:86;;6125:9;6119:4;6115:20;6110:2;6099:9;6095:18;6088:48;6153:78;6226:4;6217:6;6153:78;:::i;:::-;6145:86;;6241:72;6309:2;6298:9;6294:18;6285:6;6241:72;:::i;:::-;6361:9;6355:4;6351:20;6345:3;6334:9;6330:19;6323:49;6389:78;6462:4;6453:6;6389:78;:::i;:::-;6381:86;;6477:73;6545:3;6534:9;6530:19;6521:6;6477:73;:::i;:::-;5807:750;;;;;;;;;:::o;6563:210::-;;6688:2;6677:9;6673:18;6665:26;;6701:65;6763:1;6752:9;6748:17;6739:6;6701:65;:::i;:::-;6655:118;;;;:::o;6779:664::-;;7026:2;7015:9;7011:18;7003:26;;7075:9;7069:4;7065:20;7061:1;7050:9;7046:17;7039:47;7103:88;7186:4;7177:6;7169;7103:88;:::i;:::-;7095:96;;7238:9;7232:4;7228:20;7223:2;7212:9;7208:18;7201:48;7266:88;7349:4;7340:6;7332;7266:88;:::i;:::-;7258:96;;7364:72;7432:2;7421:9;7417:18;7408:6;7364:72;:::i;:::-;6993:450;;;;;;;;:::o;7449:419::-;;7653:2;7642:9;7638:18;7630:26;;7702:9;7696:4;7692:20;7688:1;7677:9;7673:17;7666:47;7730:131;7856:4;7730:131;:::i;:::-;7722:139;;7620:248;;;:::o;7874:419::-;;8078:2;8067:9;8063:18;8055:26;;8127:9;8121:4;8117:20;8113:1;8102:9;8098:17;8091:47;8155:131;8281:4;8155:131;:::i;:::-;8147:139;;8045:248;;;:::o;8299:222::-;;8430:2;8419:9;8415:18;8407:26;;8443:71;8511:1;8500:9;8496:17;8487:6;8443:71;:::i;:::-;8397:124;;;;:::o;8527:99::-;;8613:5;8607:12;8597:22;;8586:40;;;:::o;8632:147::-;;8770:3;8755:18;;8745:34;;;;:::o;8785:169::-;;8903:6;8898:3;8891:19;8943:4;8938:3;8934:14;8919:29;;8881:73;;;;:::o;8960:148::-;;9099:3;9084:18;;9074:34;;;;:::o;9114:96::-;;9180:24;9198:5;9180:24;:::i;:::-;9169:35;;9159:51;;;:::o;9216:90::-;;9293:5;9286:13;9279:21;9268:32;;9258:48;;;:::o;9312:77::-;;9378:5;9367:16;;9357:32;;;:::o;9395:126::-;;9472:42;9465:5;9461:54;9450:65;;9440:81;;;:::o;9527:77::-;;9593:5;9582:16;;9572:32;;;:::o;9610:154::-;9694:6;9689:3;9684;9671:30;9756:1;9747:6;9742:3;9738:16;9731:27;9661:103;;;:::o;9770:307::-;9838:1;9848:113;9862:6;9859:1;9856:13;9848:113;;;9947:1;9942:3;9938:11;9932:18;9928:1;9923:3;9919:11;9912:39;9884:2;9881:1;9877:10;9872:15;;9848:113;;;9979:6;9976:1;9973:13;9970:2;;;10059:1;10050:6;10045:3;10041:16;10034:27;9970:2;9819:258;;;;:::o;10083:320::-;;10164:1;10158:4;10154:12;10144:22;;10211:1;10205:4;10201:12;10232:18;10222:2;;10288:4;10280:6;10276:17;10266:27;;10222:2;10350;10342:6;10339:14;10319:18;10316:38;10313:2;;;10369:18;;:::i;:::-;10313:2;10134:269;;;;:::o;10409:180::-;10457:77;10454:1;10447:88;10554:4;10551:1;10544:15;10578:4;10575:1;10568:15;10595:102;;10687:2;10683:7;10678:2;10671:5;10667:14;10663:28;10653:38;;10643:54;;;:::o;10703:122::-;10776:24;10794:5;10776:24;:::i;:::-;10769:5;10766:35;10756:2;;10815:1;10812;10805:12;10756:2;10746:79;:::o;10831:122::-;10904:24;10922:5;10904:24;:::i;:::-;10897:5;10894:35;10884:2;;10943:1;10940;10933:12;10884:2;10874:79;:::o", "source": "// SPDX-License-Identifier: MIT\r\npragma solidity ^0.8.0;\r\n\r\n/// @title UTHM Attendance Smart Contract (matric-based duplicate guard)\r\n/// @notice Records student attendance per course in an immutable ledger\r\ncontract Attendance {\r\n    struct Record {\r\n        address student;    // the wallet that submitted\r\n        string  studentID;  // e.g. \"AI250\"\r\n        string  studentName;\r\n        uint256 courseId;\r\n        string  courseName;\r\n        uint256 timestamp;\r\n    }\r\n\r\n    // courseId → keccak256(matric) → whether already marked\r\n    mapping(uint256 => mapping(bytes32 => bool)) public hasAttended;\r\n\r\n    // chronological attendance logs\r\n    Record[] public records;\r\n\r\n    event AttendanceRecorded(\r\n        address indexed student,\r\n        string  indexed studentID,\r\n        uint256 indexed courseId,\r\n        string  studentName,\r\n        string  courseName,\r\n        uint256 timestamp\r\n    );\r\n\r\n    /// @notice Mark attendance for a given matric + course\r\n    /// @param courseId numeric course identifier\r\n    /// @param studentID student’s matric (e.g. \"AI250\")\r\n    /// @param studentName full name of the student\r\n    /// @param courseName name of the course/session\r\n    function markAttendance(\r\n        uint256 courseId,\r\n        string calldata studentID,\r\n        string calldata studentName,\r\n        string calldata courseName\r\n    ) external\r\n    {\r\n        bytes32 key = keccak256(bytes(studentID));\r\n        require(\r\n            !hasAttended[courseId][key],\r\n            \"Already marked attendance for this matric\"\r\n        );\r\n\r\n        hasAttended[courseId][key] = true;\r\n\r\n        records.push(Record({\r\n            student:     msg.sender,\r\n            studentID:   studentID,\r\n            studentName: studentName,\r\n            courseId:    courseId,\r\n            courseName:  courseName,\r\n            timestamp:   block.timestamp\r\n        }));\r\n\r\n        emit AttendanceRecorded(\r\n            msg.sender,\r\n            studentID,\r\n            courseId,\r\n            studentName,\r\n            courseName,\r\n            block.timestamp\r\n        );\r\n    }\r\n\r\n    /// @notice Number of attendance records stored\r\n    function getRecordCount() external view returns (uint256) {\r\n        return records.length;\r\n    }\r\n\r\n    /// @notice Fetch a specific attendance record by index\r\n    function getRecord(uint256 idx)\r\n        external\r\n        view\r\n        returns (\r\n            address student,\r\n            string memory studentID,\r\n            string memory studentName,\r\n            uint256 courseId,\r\n            string memory courseName,\r\n            uint256 timestamp\r\n        )\r\n    {\r\n        require(idx < records.length, \"Index out of bounds\");\r\n        Record storage r = records[idx];\r\n        return (\r\n            r.student,\r\n            r.studentID,\r\n            r.studentName,\r\n            r.courseId,\r\n            r.courseName,\r\n            r.timestamp\r\n        );\r\n    }\r\n}\r\n", "sourcePath": "C:\\xampp\\htdocs\\uthm_attendance\\blockchain\\contracts\\Attendance.sol", "ast": {"absolutePath": "project:/contracts/Attendance.sol", "exportedSymbols": {"Attendance": [164]}, "id": 165, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 1, "literals": ["solidity", "^", "0.8", ".0"], "nodeType": "PragmaDirective", "src": "33:23:0"}, {"abstract": false, "baseContracts": [], "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 2, "nodeType": "StructuredDocumentation", "src": "60:148:0", "text": "@title UTHM Attendance Smart Contract (matric-based duplicate guard)\n @notice Records student attendance per course in an immutable ledger"}, "fullyImplemented": true, "id": 164, "linearizedBaseContracts": [164], "name": "Attendance", "nodeType": "ContractDefinition", "nodes": [{"canonicalName": "Attendance.Record", "id": 15, "members": [{"constant": false, "id": 4, "mutability": "mutable", "name": "student", "nodeType": "VariableDeclaration", "scope": 15, "src": "260:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 3, "name": "address", "nodeType": "ElementaryTypeName", "src": "260:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 6, "mutability": "mutable", "name": "studentID", "nodeType": "VariableDeclaration", "scope": 15, "src": "318:17:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}, "typeName": {"id": 5, "name": "string", "nodeType": "ElementaryTypeName", "src": "318:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 8, "mutability": "mutable", "name": "studentName", "nodeType": "VariableDeclaration", "scope": 15, "src": "363:19:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}, "typeName": {"id": 7, "name": "string", "nodeType": "ElementaryTypeName", "src": "363:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 10, "mutability": "mutable", "name": "courseId", "nodeType": "VariableDeclaration", "scope": 15, "src": "393:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 9, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "393:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 12, "mutability": "mutable", "name": "courseName", "nodeType": "VariableDeclaration", "scope": 15, "src": "420:18:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}, "typeName": {"id": 11, "name": "string", "nodeType": "ElementaryTypeName", "src": "420:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 14, "mutability": "mutable", "name": "timestamp", "nodeType": "VariableDeclaration", "scope": 15, "src": "449:17:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 13, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "449:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "name": "Record", "nodeType": "StructDefinition", "scope": 164, "src": "235:239:0", "visibility": "public"}, {"constant": false, "functionSelector": "1c12ceb9", "id": 21, "mutability": "mutable", "name": "hasAttended", "nodeType": "VariableDeclaration", "scope": 164, "src": "548:63:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_mapping$_t_bytes32_$_t_bool_$_$", "typeString": "mapping(uint256 => mapping(bytes32 => bool))"}, "typeName": {"id": 20, "keyType": {"id": 16, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "556:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Mapping", "src": "548:44:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_mapping$_t_bytes32_$_t_bool_$_$", "typeString": "mapping(uint256 => mapping(bytes32 => bool))"}, "valueType": {"id": 19, "keyType": {"id": 17, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "575:7:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "Mapping", "src": "567:24:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_bool_$", "typeString": "mapping(bytes32 => bool)"}, "valueType": {"id": 18, "name": "bool", "nodeType": "ElementaryTypeName", "src": "586:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}}}, "visibility": "public"}, {"constant": false, "functionSelector": "34461067", "id": 25, "mutability": "mutable", "name": "records", "nodeType": "VariableDeclaration", "scope": 164, "src": "658:23:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Record_$15_storage_$dyn_storage", "typeString": "struct Attendance.Record[]"}, "typeName": {"baseType": {"id": 23, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 22, "name": "Record", "nodeType": "IdentifierPath", "referencedDeclaration": 15, "src": "658:6:0"}, "referencedDeclaration": 15, "src": "658:6:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Record_$15_storage_ptr", "typeString": "struct Attendance.Record"}}, "id": 24, "nodeType": "ArrayTypeName", "src": "658:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Record_$15_storage_$dyn_storage_ptr", "typeString": "struct Attendance.Record[]"}}, "visibility": "public"}, {"anonymous": false, "id": 39, "name": "AttendanceRecorded", "nodeType": "EventDefinition", "parameters": {"id": 38, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 27, "indexed": true, "mutability": "mutable", "name": "student", "nodeType": "VariableDeclaration", "scope": 39, "src": "725:23:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 26, "name": "address", "nodeType": "ElementaryTypeName", "src": "725:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 29, "indexed": true, "mutability": "mutable", "name": "studentID", "nodeType": "VariableDeclaration", "scope": 39, "src": "759:25:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 28, "name": "string", "nodeType": "ElementaryTypeName", "src": "759:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 31, "indexed": true, "mutability": "mutable", "name": "courseId", "nodeType": "VariableDeclaration", "scope": 39, "src": "795:24:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 30, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "795:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 33, "indexed": false, "mutability": "mutable", "name": "studentName", "nodeType": "VariableDeclaration", "scope": 39, "src": "830:19:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 32, "name": "string", "nodeType": "ElementaryTypeName", "src": "830:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 35, "indexed": false, "mutability": "mutable", "name": "courseName", "nodeType": "VariableDeclaration", "scope": 39, "src": "860:18:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 34, "name": "string", "nodeType": "ElementaryTypeName", "src": "860:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 37, "indexed": false, "mutability": "mutable", "name": "timestamp", "nodeType": "VariableDeclaration", "scope": 39, "src": "889:17:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 36, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "889:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "714:199:0"}, "src": "690:224:0"}, {"body": {"id": 104, "nodeType": "Block", "src": "1384:712:0", "statements": [{"assignments": [52], "declarations": [{"constant": false, "id": 52, "mutability": "mutable", "name": "key", "nodeType": "VariableDeclaration", "scope": 104, "src": "1395:11:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 51, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "1395:7:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "id": 59, "initialValue": {"arguments": [{"arguments": [{"id": 56, "name": "studentID", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 44, "src": "1425:9:0", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}], "id": 55, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1419:5:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes_storage_ptr_$", "typeString": "type(bytes storage pointer)"}, "typeName": {"id": 54, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "1419:5:0", "typeDescriptions": {}}}, "id": 57, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1419:16:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes calldata"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes calldata"}], "id": 53, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4294967288, "src": "1409:9:0", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 58, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1409:27:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "VariableDeclarationStatement", "src": "1395:41:0"}, {"expression": {"arguments": [{"id": 66, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "1469:27:0", "subExpression": {"baseExpression": {"baseExpression": {"id": 61, "name": "hasAttended", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 21, "src": "1470:11:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_mapping$_t_bytes32_$_t_bool_$_$", "typeString": "mapping(uint256 => mapping(bytes32 => bool))"}}, "id": 63, "indexExpression": {"id": 62, "name": "courseId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42, "src": "1482:8:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1470:21:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_bool_$", "typeString": "mapping(bytes32 => bool)"}}, "id": 65, "indexExpression": {"id": 64, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 52, "src": "1492:3:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1470:26:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "416c7265616479206d61726b656420617474656e64616e636520666f722074686973206d6174726963", "id": 67, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1511:43:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_df4092a6585e16cbd7297e32b101e6ed160262f5c62bc08342cd783cc74d8a8c", "typeString": "literal_string \"Already marked attendance for this matric\""}, "value": "Already marked attendance for this matric"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_df4092a6585e16cbd7297e32b101e6ed160262f5c62bc08342cd783cc74d8a8c", "typeString": "literal_string \"Already marked attendance for this matric\""}], "id": 60, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [4294967278, 4294967278], "referencedDeclaration": 4294967278, "src": "1447:7:0", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 68, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1447:118:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 69, "nodeType": "ExpressionStatement", "src": "1447:118:0"}, {"expression": {"id": 76, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"baseExpression": {"id": 70, "name": "hasAttended", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 21, "src": "1578:11:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_mapping$_t_bytes32_$_t_bool_$_$", "typeString": "mapping(uint256 => mapping(bytes32 => bool))"}}, "id": 73, "indexExpression": {"id": 71, "name": "courseId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42, "src": "1590:8:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1578:21:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_bool_$", "typeString": "mapping(bytes32 => bool)"}}, "id": 74, "indexExpression": {"id": 72, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 52, "src": "1600:3:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1578:26:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 75, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "1607:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "1578:33:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 77, "nodeType": "ExpressionStatement", "src": "1578:33:0"}, {"expression": {"arguments": [{"arguments": [{"expression": {"id": 82, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4294967281, "src": "1672:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 83, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "src": "1672:10:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 84, "name": "studentID", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 44, "src": "1710:9:0", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}}, {"id": 85, "name": "studentName", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 46, "src": "1747:11:0", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}}, {"id": 86, "name": "courseId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42, "src": "1786:8:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 87, "name": "courseName", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 48, "src": "1822:10:0", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}}, {"expression": {"id": 88, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4294967292, "src": "1860:5:0", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 89, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "timestamp", "nodeType": "MemberAccess", "src": "1860:15:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}, {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 81, "name": "Record", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 15, "src": "1637:6:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_Record_$15_storage_ptr_$", "typeString": "type(struct Attendance.Record storage pointer)"}}, "id": 90, "isConstant": false, "isLValue": false, "isPure": false, "kind": "structConstructorCall", "lValueRequested": false, "names": ["student", "studentID", "studentName", "courseId", "courseName", "timestamp"], "nodeType": "FunctionCall", "src": "1637:250:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_Record_$15_memory_ptr", "typeString": "struct Attendance.Record memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_Record_$15_memory_ptr", "typeString": "struct Attendance.Record memory"}], "expression": {"id": 78, "name": "records", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 25, "src": "1624:7:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Record_$15_storage_$dyn_storage", "typeString": "struct Attendance.Record storage ref[] storage ref"}}, "id": 80, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "push", "nodeType": "MemberAccess", "src": "1624:12:0", "typeDescriptions": {"typeIdentifier": "t_function_arraypush_nonpayable$_t_struct$_Record_$15_storage_$returns$__$", "typeString": "function (struct Attendance.Record storage ref)"}}, "id": 91, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1624:264:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 92, "nodeType": "ExpressionStatement", "src": "1624:264:0"}, {"eventCall": {"arguments": [{"expression": {"id": 94, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4294967281, "src": "1939:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 95, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "src": "1939:10:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 96, "name": "studentID", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 44, "src": "1964:9:0", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}}, {"id": 97, "name": "courseId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42, "src": "1988:8:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 98, "name": "studentName", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 46, "src": "2011:11:0", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}}, {"id": 99, "name": "courseName", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 48, "src": "2037:10:0", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}}, {"expression": {"id": 100, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4294967292, "src": "2062:5:0", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 101, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "timestamp", "nodeType": "MemberAccess", "src": "2062:15:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}, {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 93, "name": "AttendanceRecorded", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39, "src": "1906:18:0", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_string_memory_ptr_$_t_uint256_$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_uint256_$returns$__$", "typeString": "function (address,string memory,uint256,string memory,string memory,uint256)"}}, "id": 102, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1906:182:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 103, "nodeType": "EmitStatement", "src": "1901:187:0"}]}, "documentation": {"id": 40, "nodeType": "StructuredDocumentation", "src": "922:273:0", "text": "@notice Mark attendance for a given matric + course\n @param courseId numeric course identifier\n @param studentID student’s matric (e.g. \"AI250\")\n @param studentName full name of the student\n @param courseName name of the course/session"}, "functionSelector": "a9b8f385", "id": 105, "implemented": true, "kind": "function", "modifiers": [], "name": "markAttendance", "nodeType": "FunctionDefinition", "parameters": {"id": 49, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42, "mutability": "mutable", "name": "courseId", "nodeType": "VariableDeclaration", "scope": 105, "src": "1235:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1235:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 44, "mutability": "mutable", "name": "studentID", "nodeType": "VariableDeclaration", "scope": 105, "src": "1262:25:0", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string"}, "typeName": {"id": 43, "name": "string", "nodeType": "ElementaryTypeName", "src": "1262:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 46, "mutability": "mutable", "name": "studentName", "nodeType": "VariableDeclaration", "scope": 105, "src": "1298:27:0", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string"}, "typeName": {"id": 45, "name": "string", "nodeType": "ElementaryTypeName", "src": "1298:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 48, "mutability": "mutable", "name": "courseName", "nodeType": "VariableDeclaration", "scope": 105, "src": "1336:26:0", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string"}, "typeName": {"id": 47, "name": "string", "nodeType": "ElementaryTypeName", "src": "1336:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1224:145:0"}, "returnParameters": {"id": 50, "nodeType": "ParameterList", "parameters": [], "src": "1384:0:0"}, "scope": 164, "src": "1201:895:0", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 114, "nodeType": "Block", "src": "2215:40:0", "statements": [{"expression": {"expression": {"id": 111, "name": "records", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 25, "src": "2233:7:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Record_$15_storage_$dyn_storage", "typeString": "struct Attendance.Record storage ref[] storage ref"}}, "id": 112, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "length", "nodeType": "MemberAccess", "src": "2233:14:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 110, "id": 113, "nodeType": "Return", "src": "2226:21:0"}]}, "documentation": {"id": 106, "nodeType": "StructuredDocumentation", "src": "2104:47:0", "text": "@notice Number of attendance records stored"}, "functionSelector": "ca267f28", "id": 115, "implemented": true, "kind": "function", "modifiers": [], "name": "getRecordCount", "nodeType": "FunctionDefinition", "parameters": {"id": 107, "nodeType": "ParameterList", "parameters": [], "src": "2180:2:0"}, "returnParameters": {"id": 110, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 109, "mutability": "mutable", "name": "", "nodeType": "VariableDeclaration", "scope": 115, "src": "2206:7:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 108, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2206:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2205:9:0"}, "scope": 164, "src": "2157:98:0", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 162, "nodeType": "Block", "src": "2632:298:0", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 137, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 134, "name": "idx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 118, "src": "2651:3:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"expression": {"id": 135, "name": "records", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 25, "src": "2657:7:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Record_$15_storage_$dyn_storage", "typeString": "struct Attendance.Record storage ref[] storage ref"}}, "id": 136, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "length", "nodeType": "MemberAccess", "src": "2657:14:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2651:20:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "496e646578206f7574206f6620626f756e6473", "id": 138, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2673:21:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_dd00b67a545791a54dd99d9c09eb42099756ea4ee2bd47188784c22234589367", "typeString": "literal_string \"Index out of bounds\""}, "value": "Index out of bounds"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_dd00b67a545791a54dd99d9c09eb42099756ea4ee2bd47188784c22234589367", "typeString": "literal_string \"Index out of bounds\""}], "id": 133, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [4294967278, 4294967278], "referencedDeclaration": 4294967278, "src": "2643:7:0", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 139, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2643:52:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 140, "nodeType": "ExpressionStatement", "src": "2643:52:0"}, {"assignments": [143], "declarations": [{"constant": false, "id": 143, "mutability": "mutable", "name": "r", "nodeType": "VariableDeclaration", "scope": 162, "src": "2706:16:0", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_Record_$15_storage_ptr", "typeString": "struct Attendance.Record"}, "typeName": {"id": 142, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 141, "name": "Record", "nodeType": "IdentifierPath", "referencedDeclaration": 15, "src": "2706:6:0"}, "referencedDeclaration": 15, "src": "2706:6:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Record_$15_storage_ptr", "typeString": "struct Attendance.Record"}}, "visibility": "internal"}], "id": 147, "initialValue": {"baseExpression": {"id": 144, "name": "records", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 25, "src": "2725:7:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Record_$15_storage_$dyn_storage", "typeString": "struct Attendance.Record storage ref[] storage ref"}}, "id": 146, "indexExpression": {"id": 145, "name": "idx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 118, "src": "2733:3:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2725:12:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Record_$15_storage", "typeString": "struct Attendance.Record storage ref"}}, "nodeType": "VariableDeclarationStatement", "src": "2706:31:0"}, {"expression": {"components": [{"expression": {"id": 148, "name": "r", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 143, "src": "2770:1:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Record_$15_storage_ptr", "typeString": "struct Attendance.Record storage pointer"}}, "id": 149, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberName": "student", "nodeType": "MemberAccess", "referencedDeclaration": 4, "src": "2770:9:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"expression": {"id": 150, "name": "r", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 143, "src": "2794:1:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Record_$15_storage_ptr", "typeString": "struct Attendance.Record storage pointer"}}, "id": 151, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberName": "studentID", "nodeType": "MemberAccess", "referencedDeclaration": 6, "src": "2794:11:0", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, {"expression": {"id": 152, "name": "r", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 143, "src": "2820:1:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Record_$15_storage_ptr", "typeString": "struct Attendance.Record storage pointer"}}, "id": 153, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberName": "studentName", "nodeType": "MemberAccess", "referencedDeclaration": 8, "src": "2820:13:0", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, {"expression": {"id": 154, "name": "r", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 143, "src": "2848:1:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Record_$15_storage_ptr", "typeString": "struct Attendance.Record storage pointer"}}, "id": 155, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberName": "courseId", "nodeType": "MemberAccess", "referencedDeclaration": 10, "src": "2848:10:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 156, "name": "r", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 143, "src": "2873:1:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Record_$15_storage_ptr", "typeString": "struct Attendance.Record storage pointer"}}, "id": 157, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberName": "courseName", "nodeType": "MemberAccess", "referencedDeclaration": 12, "src": "2873:12:0", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, {"expression": {"id": 158, "name": "r", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 143, "src": "2900:1:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Record_$15_storage_ptr", "typeString": "struct Attendance.Record storage pointer"}}, "id": 159, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberName": "timestamp", "nodeType": "MemberAccess", "referencedDeclaration": 14, "src": "2900:11:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 160, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "2755:167:0", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_address_$_t_string_storage_$_t_string_storage_$_t_uint256_$_t_string_storage_$_t_uint256_$", "typeString": "tuple(address,string storage ref,string storage ref,uint256,string storage ref,uint256)"}}, "functionReturnParameters": 132, "id": 161, "nodeType": "Return", "src": "2748:174:0"}]}, "documentation": {"id": 116, "nodeType": "StructuredDocumentation", "src": "2263:55:0", "text": "@notice Fetch a specific attendance record by index"}, "functionSelector": "03e9e609", "id": 163, "implemented": true, "kind": "function", "modifiers": [], "name": "getRecord", "nodeType": "FunctionDefinition", "parameters": {"id": 119, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 118, "mutability": "mutable", "name": "idx", "nodeType": "VariableDeclaration", "scope": 163, "src": "2343:11:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 117, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2343:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2342:13:0"}, "returnParameters": {"id": 132, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 121, "mutability": "mutable", "name": "student", "nodeType": "VariableDeclaration", "scope": 163, "src": "2420:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 120, "name": "address", "nodeType": "ElementaryTypeName", "src": "2420:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 123, "mutability": "mutable", "name": "studentID", "nodeType": "VariableDeclaration", "scope": 163, "src": "2450:23:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 122, "name": "string", "nodeType": "ElementaryTypeName", "src": "2450:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 125, "mutability": "mutable", "name": "studentName", "nodeType": "VariableDeclaration", "scope": 163, "src": "2488:25:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 124, "name": "string", "nodeType": "ElementaryTypeName", "src": "2488:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 127, "mutability": "mutable", "name": "courseId", "nodeType": "VariableDeclaration", "scope": 163, "src": "2528:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 126, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2528:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 129, "mutability": "mutable", "name": "courseName", "nodeType": "VariableDeclaration", "scope": 163, "src": "2559:24:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 128, "name": "string", "nodeType": "ElementaryTypeName", "src": "2559:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 131, "mutability": "mutable", "name": "timestamp", "nodeType": "VariableDeclaration", "scope": 163, "src": "2598:17:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 130, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2598:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2405:221:0"}, "scope": 164, "src": "2324:606:0", "stateMutability": "view", "virtual": false, "visibility": "external"}], "scope": 165, "src": "208:2725:0"}], "src": "33:2902:0"}, "compiler": {"name": "solc", "version": "0.8.0+commit.c7dfd78e.Emscripten.clang"}, "networks": {"5777": {"events": {}, "links": {}, "address": "******************************************", "transactionHash": "0x5502b096fa862ebed34f84d2e7a8a96627cca8e1470e76a7fbc5133b5a2af387"}}, "schemaVersion": "3.4.16", "updatedAt": "2025-06-09T09:42:37.984Z", "networkType": "ethereum", "devdoc": {"kind": "dev", "methods": {"markAttendance(uint256,string,string,string)": {"params": {"courseId": "numeric course identifier", "courseName": "name of the course/session", "studentID": "student’s matric (e.g. \"AI250\")", "studentName": "full name of the student"}}}, "title": "UTHM Attendance Smart Contract (matric-based duplicate guard)", "version": 1}, "userdoc": {"kind": "user", "methods": {"getRecord(uint256)": {"notice": "Fetch a specific attendance record by index"}, "getRecordCount()": {"notice": "Number of attendance records stored"}, "markAttendance(uint256,string,string,string)": {"notice": "Mark attendance for a given matric + course"}}, "notice": "Records student attendance per course in an immutable ledger", "version": 1}}