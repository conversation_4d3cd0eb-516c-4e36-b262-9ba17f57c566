/* ═══════════════════════════════════════════════════════════════
   MANAGE COURSES PAGE - SPECIFIC STYLING
   Enhanced course management interface with professional design
   ═══════════════════════════════════════════════════════════════ */

/* ─────────── FORM SECTIONS ENHANCEMENT ─────────── */
.form-section {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

.form-section:hover {
  box-shadow: var(--shadow-md);
}

/* ─────────── REGISTRATION TOGGLE STYLING ─────────── */
.registration-toggle-container {
  margin-bottom: var(--spacing-xl);
}

.toggle-status-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  background: var(--card-bg);
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
}

.toggle-status-card:hover {
  box-shadow: var(--shadow-md);
}

.toggle-status-card.enabled {
  border-color: var(--success-color);
  background: rgba(34, 197, 94, 0.05);
}

.toggle-status-card.disabled {
  border-color: var(--danger-color);
  background: rgba(239, 68, 68, 0.05);
}

.toggle-status-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 1.5rem;
}

.toggle-status-card.enabled .toggle-status-icon {
  background: var(--success-color);
  color: white;
}

.toggle-status-card.disabled .toggle-status-icon {
  background: var(--danger-color);
  color: white;
}

.toggle-status-content {
  flex: 1;
}

.toggle-status-title {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.toggle-status-description {
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.5;
  font-size: 0.9rem;
}

.toggle-status-action {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ─────────── ENHANCED TOGGLE SWITCH ─────────── */
.toggle-switch {
  position: relative;
  display: inline-block;
}

.toggle-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-label {
  position: relative;
  display: inline-block;
  width: 64px;
  height: 32px;
  background-color: #dc2626;
  border-radius: 32px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.toggle-slider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 28px;
  height: 28px;
  background-color: white;
  border-radius: 50%;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: bold;
}

.toggle-slider::before {
  content: '✕';
  color: #dc2626;
}

.toggle-input:checked + .toggle-label {
  background-color: #16a34a;
}

.toggle-input:checked + .toggle-label .toggle-slider {
  transform: translateX(32px);
}

.toggle-input:checked + .toggle-label .toggle-slider::before {
  content: '✓';
  color: #16a34a;
}

/* ─────────── ENHANCED BUTTON STYLING ─────────── */
.action-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

.action-btn i {
  font-size: 0.85rem;
}

.action-btn.primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, #2563eb 100%);
  color: white;
  border: 1px solid var(--primary-color);
}

.action-btn.primary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.action-btn.primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.action-btn.primary:disabled {
  background: var(--text-secondary);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* ─────────── FORM STYLING ─────────── */
.add-user-form {
  margin-bottom: var(--spacing-xl);
}

.form-actions {
  display: flex;
  justify-content: flex-start;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

.form-section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: 0 0 var(--spacing-lg) 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.form-section-title i {
  color: var(--primary-color);
  font-size: 0.9rem;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.form-group label {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.form-input {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  transition: all 0.3s ease;
  background: var(--input-bg);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.field-hint {
  color: var(--text-secondary);
  font-size: 0.8rem;
  margin-top: var(--spacing-xs);
}

.required {
  color: var(--danger-color);
}

/* ─────────── USER FILTERING INTERFACE ─────────── */
.user-filter-container {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
}

.filter-tabs {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: var(--spacing-md);
}

.filter-tab {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--surface);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 0.9rem;
}

.filter-tab:hover {
  background: var(--primary-light);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.filter-tab.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.user-count-badge {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 0.8rem;
  font-weight: 600;
  margin-left: var(--spacing-xs);
}

.filter-tab.active .user-count-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* ─────────── ENHANCED USER TABLES ─────────── */
.user-table-container {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.user-table-container.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Enhanced table styling for better layout */
.user-table-container .courses-table {
  table-layout: fixed;
  width: 100%;
}

.user-table-container .courses-table th:nth-child(1),
.user-table-container .courses-table td:nth-child(1) {
  width: 15%; /* Course Code */
}

.user-table-container .courses-table th:nth-child(2),
.user-table-container .courses-table td:nth-child(2) {
  width: 35%; /* Course Name */
}

.user-table-container .courses-table th:nth-child(3),
.user-table-container .courses-table td:nth-child(3) {
  width: 20%; /* Status/Section */
}

.user-table-container .courses-table th:nth-child(4),
.user-table-container .courses-table td:nth-child(4) {
  width: 30%; /* Actions/Lecturer */
}

.user-id-badge {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.user-id-badge.student {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.2) 100%);
  color: var(--primary-color);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.user-id-badge.lecturer {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.2) 100%);
  color: var(--success-color);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.user-name {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.user-email {
  color: var(--text-secondary);
  font-size: 0.85rem;
  font-style: italic;
}

.faculty-text {
  color: var(--text-secondary);
  font-size: 0.85rem;
  line-height: 1.4;
}

/* ─────────── RESPONSIVE DESIGN ─────────── */
@media (max-width: 768px) {
  .toggle-status-card {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .toggle-status-action {
    width: 100%;
    justify-content: center;
  }

  .form-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }

  .filter-tabs {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .filter-tab {
    justify-content: center;
  }

  .form-section {
    padding: var(--spacing-lg);
  }

  .form-section-title {
    font-size: 1rem;
  }

  .action-btn.primary {
    width: 100%;
    min-width: auto;
  }

  /* Mobile table improvements */
  .user-table-container .courses-table th:nth-child(1),
  .user-table-container .courses-table td:nth-child(1) {
    width: 20%;
  }

  .user-table-container .courses-table th:nth-child(2),
  .user-table-container .courses-table td:nth-child(2) {
    width: 25%;
  }

  .user-table-container .courses-table th:nth-child(3),
  .user-table-container .courses-table td:nth-child(3) {
    width: 25%;
  }

  .user-table-container .courses-table th:nth-child(4),
  .user-table-container .courses-table td:nth-child(4) {
    width: 30%;
  }

  .faculty-text {
    font-size: 0.8rem;
  }

  .user-email {
    font-size: 0.8rem;
  }

  .user-name {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .form-section {
    padding: var(--spacing-md);
  }

  /* Very small screen table adjustments */
  .user-table-container .courses-table th,
  .user-table-container .courses-table td {
    padding: var(--spacing-sm);
    font-size: 0.8rem;
  }

  .user-id-badge {
    font-size: 0.7rem;
    padding: 2px 6px;
  }

  .faculty-text {
    font-size: 0.75rem;
    line-height: 1.3;
  }

  .user-email {
    font-size: 0.75rem;
    line-height: 1.3;
  }

  .user-name {
    font-size: 0.8rem;
    line-height: 1.2;
  }
}

/* ─────────────────── RESPONSIVE DESIGN ─────────────────── */
@media (max-width: 768px) {
  .toggle-status-card {
    flex-direction: column;
    text-align: center;
    gap: var(--space-md);
  }

  .toggle-status-action {
    flex-direction: column;
    width: 100%;
    gap: var(--space-sm);
  }

  .toggle-switch {
    align-self: center;
  }

  .action-btn.compact {
    width: 100%;
    justify-content: center;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions .action-btn {
    width: 100%;
    justify-content: center;
  }
}

/* ─────────────────── ENHANCED VISUAL HIERARCHY ─────────────────── */
.courses-container {
  background: var(--surface);
  border-radius: var(--border-radius);
  padding: var(--space-lg);
  margin-bottom: var(--space-lg);
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.page-header {
  margin-bottom: var(--space-xl);
  text-align: left;
}

.page-title {
  margin: 0 0 var(--space-xs) 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
}

.page-subtitle {
  margin: 0;
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* ─────────────────── BUTTON VARIANTS ─────────────────── */
.action-btn.warning {
  background: var(--warning);
  border-color: var(--warning);
  color: white;
}

.action-btn.warning:hover {
  background: var(--warning-dark);
  border-color: var(--warning-dark);
  transform: translateY(-1px);
}

.action-btn.success {
  background: var(--success);
  border-color: var(--success);
  color: white;
}

.action-btn.success:hover {
  background: var(--success-dark);
  border-color: var(--success-dark);
  transform: translateY(-1px);
}

/* ─────────────────── ACCESSIBILITY IMPROVEMENTS ─────────────────── */
.toggle-input:focus + .toggle-label {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

.action-btn:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* ─────────────────── ANIMATION ENHANCEMENTS ─────────────────── */
.toggle-status-card {
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.action-btn.compact {
  transition: all 0.2s ease;
}

.action-btn.compact:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}
