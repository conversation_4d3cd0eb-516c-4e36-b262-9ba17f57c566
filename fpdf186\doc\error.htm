<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Error</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Error</h1>
<code>Error(<b>string</b> msg)</code>
<h2>Description</h2>
This method is automatically called in case of a fatal error; it simply throws an exception
with the provided message.<br>
An inherited class may override it to customize the error handling but the method should
never return, otherwise the resulting document would probably be invalid.
<h2>Parameters</h2>
<dl class="param">
<dt><code>msg</code></dt>
<dd>
The error message.
</dd>
</dl>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
