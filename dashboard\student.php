<?php
// ─────────────────────────────────────────────────────────────────────────────
//  dashboard/student.php
//  - Enhanced Student Dashboard with improved charts and modern design
//  - Fetches real data from course_registration + attendance_report
//  - Professional, creative, and user-friendly interface
// ─────────────────────────────────────────────────────────────────────────────

session_start();
require '../config/config.php';    // Database connection

// ─────────────────────────────────────────────────────────────────────────────
// 1) SECURITY CHECK: Only 'student' role can access
// ─────────────────────────────────────────────────────────────────────────────
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header("Location: ../index.php");
    exit();
}

// ─────────────────────────────────────────────────────────────────────────────
// 2) FETCH STUDENT INFO (for header/sidebar display)
// ─────────────────────────────────────────────────────────────────────────────
$student_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT Name, UserID, Photo FROM student WHERE StudentID = ?");
$stmt->bind_param("i", $student_id);
$stmt->execute();
$res = $stmt->get_result();
$student = $res->fetch_assoc();
$student_name   = $student['Name']   ?? 'Student Name';
$student_userid = $student['UserID'] ?? 'N/A';
$stmt->close();

// build photo URL (fallback to default if empty)
$photo_url = !empty($student['Photo'])
    ? "../uploads/" . rawurlencode($student['Photo'])
    : "../assets/images/user1.png";

// ─────────────────────────────────────────────────────────────────────────────
// 3) FETCH REGISTERED COURSES & ATTENDANCE STATS
// ─────────────────────────────────────────────────────────────────────────────
$query = $conn->prepare("
    SELECT 
      c.CourseID,
      c.Course_Name,
      SUM(CASE WHEN ar.Att_Status = 'Present' THEN 1 ELSE 0 END) AS presentCount,
      COUNT(ar.AttendanceID) AS totalCount
    FROM course_registration cr
    JOIN course c 
      ON cr.CourseID = c.CourseID
    LEFT JOIN attendance_report ar 
      ON ar.CourseID = c.CourseID 
     AND ar.StudentID = ?
    WHERE cr.StudentID = ?
    GROUP BY c.CourseID, c.Course_Name
    ORDER BY c.Course_Name ASC
");
$query->bind_param("ii", $student_id, $student_id);
$query->execute();
$courseRes = $query->get_result();

$registeredCourses = [];
$totalPresentAll   = 0;
$totalSessionsAll  = 0;
$totalAbsentAll    = 0;

while ($row = $courseRes->fetch_assoc()) {
    $cid        = $row['CourseID'];
    $cname      = $row['Course_Name'];
    $presentCnt = (int)$row['presentCount'];
    $totalCnt   = (int)$row['totalCount'];
    $absentCnt  = $totalCnt - $presentCnt;
    $pct        = $totalCnt > 0 ? round(($presentCnt / $totalCnt) * 100, 2) : 0;

    $registeredCourses[] = [
        'CourseID'     => $cid,
        'CourseName'   => $cname,
        'PresentCount' => $presentCnt,
        'AbsentCount'  => $absentCnt,
        'TotalCount'   => $totalCnt,
        'Percentage'   => $pct
    ];

    $totalPresentAll  += $presentCnt;
    $totalSessionsAll += $totalCnt;
    $totalAbsentAll   += $absentCnt;
}
$query->close();

// Compute overall attendance percentage
$overallPct   = $totalSessionsAll > 0 ? round(($totalPresentAll / $totalSessionsAll) * 100, 2) : 0;
$overallAbsentPct = $totalSessionsAll > 0 ? round(($totalAbsentAll / $totalSessionsAll) * 100, 2) : 0;
// Total courses registered
$totalCourses = count($registeredCourses);

// Get current date for greeting
$currentHour = date('H');
$greeting = $currentHour < 12 ? 'Good Morning' : ($currentHour < 18 ? 'Good Afternoon' : 'Good Evening');

?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Student Dashboard</title>

  <!-- FontAwesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Google Font (Inter) -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- Base + Modular CSS from Lecturer Dashboard -->
  <link rel="stylesheet" href="css/base-styles.css">
  <link rel="stylesheet" href="css/lecturer-header.css">
  <link rel="stylesheet" href="css/lecturer-sidebar.css">
  <link rel="stylesheet" href="css/lecturer-footer.css">

  <!-- Enhanced Student Dashboard CSS -->
  <link rel="stylesheet" href="css/student-dashboard-enhanced.css">
</head>
<body>
  <!-- ─────────── HEADER ─────────── -->
  <div class="header">
    <div class="header-left">
      <img src="../assets/images/logo-uthm2.png" alt="UTHM Logo" class="logo">
    </div>
    <div class="header-right">
      <a href="../modules/qr_scan.php" class="qr-button">
        <i class="fas fa-qrcode"></i> Scan QR
      </a>
      <span class="user-id"><?= htmlspecialchars($student_userid) ?></span>
    </div>
  </div>

  <!-- ─────────── CONTAINER (SIDEBAR + MAIN) ─────────── -->
  <div class="container">
    <!-- ─────────── SIDEBAR ─────────── -->
    <div class="sidebar">
      <div class="profile">
        <!-- DYNAMIC PROFILE PHOTO -->
        <img src="<?= $photo_url ?>" alt="Profile Photo" class="profile-pic">
        <p class="profile-name"><?= htmlspecialchars($student_name) ?></p>
        <p class="profile-id"><?= htmlspecialchars($student_userid) ?></p>
      </div>
      <ul class="menu">
        <li>
          <a href="student.php" class="active">
            <i class="fas fa-tachometer-alt"></i> Dashboard
          </a>
        </li>
        <li>
          <a href="../modules/profile_student.php">
            <i class="fas fa-user"></i> Profile
          </a>
        </li>
        <li>
          <a href="../modules/register_courses.php">
            <i class="fas fa-edit"></i> Register Courses
          </a>
        </li>
        <li>
          <a href="../modules/report.php">
            <i class="fas fa-book"></i> Attendance Details
          </a>
        </li>
        <li>
          <a href="student_blockchain_records.php">
            <i class="fas fa-link"></i> Blockchain Records
          </a>
        </li>
        <li>
          <a href="student_transaction_lookup.php">
            <i class="fas fa-search"></i> Transaction Lookup
          </a>
        </li>
        <li>
          <a href="../logout.php">
            <i class="fas fa-sign-out-alt"></i> Logout
          </a>
        </li>
      </ul>
    </div>

    <!-- ─────────── MAIN CONTENT ─────────── -->
    <div class="main-content">
      <!-- Enhanced Page Header -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-text">
            <h1><?= $greeting ?>, <?= htmlspecialchars($student_name) ?>!</h1>
            <p>Here's your academic overview and attendance summary</p>
          </div>
          <div class="header-date">
            <i class="fas fa-calendar-alt"></i>
            <span><?= date('l, F j, Y') ?></span>
          </div>
        </div>
      </div>

      <!-- ─────────── ENHANCED SUMMARY CARDS ─────────── -->
      <div class="card-container">
        <!-- Courses Registered -->
        <div class="stats-card courses-card">
          <div class="card-icon">
            <i class="fas fa-book-open"></i>
          </div>
          <div class="card-content">
            <div class="card-number"><?= $totalCourses ?></div>
            <div class="card-label">Courses Registered</div>
            <div class="card-trend">
              <i class="fas fa-arrow-up"></i> Active
            </div>
          </div>
        </div>

        <!-- Overall Attendance -->
        <div class="stats-card attendance-card">
          <div class="card-icon">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="card-content">
            <div class="card-number"><?= $overallPct ?>%</div>
            <div class="card-label">Overall Attendance</div>
            <div class="card-trend <?= $overallPct >= 80 ? 'positive' : ($overallPct >= 70 ? 'warning' : 'negative') ?>">
              <i class="fas fa-<?= $overallPct >= 80 ? 'check-circle' : ($overallPct >= 70 ? 'exclamation-triangle' : 'times-circle') ?>"></i>
              <?= $overallPct >= 80 ? 'Excellent' : ($overallPct >= 70 ? 'Good' : 'Needs Improvement') ?>
            </div>
          </div>
        </div>

        <!-- Total Sessions -->
        <div class="stats-card sessions-card">
          <div class="card-icon">
            <i class="fas fa-clock"></i>
          </div>
          <div class="card-content">
            <div class="card-number"><?= $totalSessionsAll ?></div>
            <div class="card-label">Total Sessions</div>
            <div class="card-trend">
              <i class="fas fa-info-circle"></i> This Semester
            </div>
          </div>
        </div>

        <!-- Sessions Attended -->
        <div class="stats-card present-card">
          <div class="card-icon">
            <i class="fas fa-user-check"></i>
          </div>
          <div class="card-content">
            <div class="card-number"><?= $totalPresentAll ?></div>
            <div class="card-label">Sessions Attended</div>
            <div class="card-trend positive">
              <i class="fas fa-thumbs-up"></i> Present
            </div>
          </div>
        </div>

        <!-- Blockchain Records -->
        <?php
        // Quick check for blockchain records - using attendance_report table with blockchain hash
        $blockchainCount = 0;
        try {
          // First try to check if blockchain_record table exists
          $checkTable = $conn->query("SHOW TABLES LIKE 'blockchain_record'");
          if ($checkTable && $checkTable->num_rows > 0) {
            // blockchain_record table exists, check its structure
            $checkColumns = $conn->query("SHOW COLUMNS FROM blockchain_record LIKE 'StudentID'");
            if ($checkColumns && $checkColumns->num_rows > 0) {
              // StudentID column exists
              $blockchainQuery = $conn->prepare("
                SELECT COUNT(*) as blockchain_count
                FROM blockchain_record br
                WHERE br.StudentID = ? AND br.Blockchain_Hash IS NOT NULL
              ");
              $blockchainQuery->bind_param("i", $student_id);
              $blockchainQuery->execute();
              $blockchainResult = $blockchainQuery->get_result();
              $blockchainCount = $blockchainResult->fetch_assoc()['blockchain_count'] ?? 0;
              $blockchainQuery->close();
            } else {
              // Try with AttendanceID join
              $blockchainQuery = $conn->prepare("
                SELECT COUNT(DISTINCT br.AttendanceID) as blockchain_count
                FROM blockchain_record br
                JOIN attendance_report ar ON br.AttendanceID = ar.AttendanceID
                WHERE ar.StudentID = ? AND br.Blockchain_Hash IS NOT NULL
              ");
              $blockchainQuery->bind_param("i", $student_id);
              $blockchainQuery->execute();
              $blockchainResult = $blockchainQuery->get_result();
              $blockchainCount = $blockchainResult->fetch_assoc()['blockchain_count'] ?? 0;
              $blockchainQuery->close();
            }
          } else {
            // blockchain_record table doesn't exist, check attendance_report for blockchain hash
            $checkBlockchainColumn = $conn->query("SHOW COLUMNS FROM attendance_report LIKE 'Blockchain_Hash'");
            if ($checkBlockchainColumn && $checkBlockchainColumn->num_rows > 0) {
              $blockchainQuery = $conn->prepare("
                SELECT COUNT(*) as blockchain_count
                FROM attendance_report
                WHERE StudentID = ? AND Blockchain_Hash IS NOT NULL AND Blockchain_Hash != ''
              ");
              $blockchainQuery->bind_param("i", $student_id);
              $blockchainQuery->execute();
              $blockchainResult = $blockchainQuery->get_result();
              $blockchainCount = $blockchainResult->fetch_assoc()['blockchain_count'] ?? 0;
              $blockchainQuery->close();
            }
          }
        } catch (Exception $e) {
          // If any error occurs, set count to 0
          $blockchainCount = 0;
        }
        ?>
        <div class="stats-card blockchain-card" style="background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); border: 1px solid #0ea5e9;">
          <div class="card-icon" style="background: #0ea5e9; color: white;">
            <i class="fas fa-shield-alt"></i>
          </div>
          <div class="card-content">
            <div class="card-number" style="color: #0c4a6e;"><?= $blockchainCount ?></div>
            <div class="card-label" style="color: #075985;">Blockchain Records</div>
            <div class="card-trend" style="color: #0ea5e9;">
              <i class="fas fa-lock"></i> Immutable
            </div>
          </div>
        </div>
      </div>

      <!-- ─────────── BLOCKCHAIN PROTECTION NOTICE ─────────── -->
      <?php if ($blockchainCount > 0): ?>
        <div style="background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); border: 1px solid #0ea5e9; border-radius: var(--border-radius-lg); padding: var(--spacing-lg); margin-bottom: var(--spacing-lg);">
          <div style="display: flex; align-items: center; gap: var(--spacing-md);">
            <div style="background: #0ea5e9; color: white; width: 48px; height: 48px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.25rem; flex-shrink: 0;">
              <i class="fas fa-shield-alt"></i>
            </div>
            <div style="flex: 1;">
              <h3 style="font-size: 1.125rem; font-weight: 700; color: #0c4a6e; margin: 0 0 var(--spacing-xs) 0;">
                <i class="fas fa-lock"></i> Your Attendance is Protected
              </h3>
              <p style="font-size: 0.875rem; color: #075985; margin: 0; line-height: 1.6;">
                You have <strong><?= $blockchainCount ?> attendance record(s)</strong> permanently stored on the blockchain.
                These records are immutable and serve as cryptographic proof of your attendance, even if database records are later modified.
              </p>
            </div>
            <div style="flex-shrink: 0;">
              <a href="student_blockchain_records.php" style="background: #0ea5e9; color: white; padding: 0.75rem 1.5rem; border-radius: var(--border-radius); text-decoration: none; font-weight: 600; display: inline-flex; align-items: center; gap: 0.5rem; transition: all 0.3s ease;">
                <i class="fas fa-eye"></i> View Records
              </a>
            </div>
          </div>
        </div>
      <?php endif; ?>

      <!-- ─────────── CHARTS SECTION ─────────── -->
      <?php if ($totalCourses > 0): ?>
        <div class="charts-container">
          <!-- Attendance by Course Chart -->
          <div class="chart-card primary-chart">
            <div class="chart-header">
              <h2><i class="fas fa-chart-bar"></i> Attendance by Course</h2>
              <div class="chart-controls">
                <button class="chart-btn active" data-chart="bar">
                  <i class="fas fa-chart-bar"></i>
                </button>
                <button class="chart-btn" data-chart="doughnut">
                  <i class="fas fa-chart-pie"></i>
                </button>
              </div>
            </div>
            <div class="chart-wrapper">
              <canvas id="attendanceChart"></canvas>
            </div>
          </div>

          <!-- Overall Attendance Doughnut -->
          <div class="chart-card secondary-chart">
            <div class="chart-header">
              <h2><i class="fas fa-chart-pie"></i> Overall Status</h2>
            </div>
            <div class="chart-wrapper">
              <canvas id="overallChart"></canvas>
            </div>
            <div class="chart-legend">
              <div class="legend-item">
                <div class="legend-color present"></div>
                <span>Present (<?= $totalPresentAll ?>)</span>
              </div>
              <div class="legend-item">
                <div class="legend-color absent"></div>
                <span>Absent (<?= $totalAbsentAll ?>)</span>
              </div>
            </div>
          </div>
        </div>
      <?php else: ?>
        <div class="chart-card empty-state">
          <div class="empty-content">
            <i class="fas fa-book-reader"></i>
            <h3>No Courses Registered</h3>
            <p>You haven't registered any courses yet. Click "Register Courses" in the sidebar to get started.</p>
            <a href="../modules/register_courses.php" class="cta-button">
              <i class="fas fa-plus"></i> Register Courses
            </a>
          </div>
        </div>
      <?php endif; ?>

      <!-- ─────────── ENHANCED COURSES TABLE ─────────── -->
      <div class="table-card">
        <div class="table-header">
          <h2>Your Registered Courses</h2>
          <?php if ($totalCourses > 0): ?>
            <div class="table-actions">
              <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" placeholder="Search courses..." id="courseSearch">
              </div>
            </div>
          <?php endif; ?>
        </div>
        
        <?php if ($totalCourses > 0): ?>
          <div class="table-wrapper">
            <table class="courses-table" id="coursesTable">
              <thead>
                <tr>
                  <th>Course ID</th>
                  <th>Course Name</th>
                  <th>Sessions</th>
                  <th>Attendance Rate</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                <?php foreach ($registeredCourses as $c): ?>
                  <tr>
                    <td data-label="Course ID">
                      <div class="course-id"><?= htmlspecialchars($c['CourseID']) ?></div>
                    </td>
                    <td data-label="Course Name">
                      <div class="course-name"><?= htmlspecialchars($c['CourseName']) ?></div>
                    </td>
                    <td data-label="Sessions">
                      <div class="sessions-info">
                        <span class="present"><?= $c['PresentCount'] ?></span> / 
                        <span class="total"><?= $c['TotalCount'] ?></span>
                      </div>
                    </td>
                    <td data-label="Attendance Rate">
                      <div class="attendance-rate">
                        <div class="progress-bar">
                          <div class="progress-fill" style="width: <?= $c['Percentage'] ?>%"></div>
                        </div>
                        <span class="percentage"><?= $c['Percentage'] ?>%</span>
                      </div>
                    </td>
                    <td data-label="Status">
                      <span class="status-badge <?= $c['Percentage'] >= 80 ? 'excellent' : ($c['Percentage'] >= 70 ? 'good' : 'warning') ?>">
                        <?= $c['Percentage'] >= 80 ? 'Excellent' : ($c['Percentage'] >= 70 ? 'Good' : 'At Risk') ?>
                      </span>
                    </td>
                  </tr>
                <?php endforeach; ?>
              </tbody>
            </table>
          </div>
        <?php else: ?>
          <div class="empty-state-table">
            <i class="fas fa-graduation-cap"></i>
            <h3>No Courses Found</h3>
            <p>Start your academic journey by registering for courses.</p>
          </div>
        <?php endif; ?>
      </div>
    </div> <!-- end .main-content -->
  </div>   <!-- end .container -->

  <!-- ─────────── FOOTER ─────────── -->
  <footer>
    <p>UNIVERSITI TUN HUSSEIN ONN MALAYSIA</p>
  </footer>

  <!-- ─────────── SCRIPTS ─────────── -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    <?php if ($totalCourses > 0): ?>
      // Prepare data for charts
      const courseLabels = <?= json_encode(array_column($registeredCourses, 'CourseName')) ?>;
      const attendanceData = <?= json_encode(array_column($registeredCourses, 'Percentage')) ?>;
      const presentData = <?= json_encode(array_column($registeredCourses, 'PresentCount')) ?>;
      const totalData = <?= json_encode(array_column($registeredCourses, 'TotalCount')) ?>;

      // Color palette
      const colors = [
        'rgba(99, 102, 241, 0.8)',
        'rgba(16, 185, 129, 0.8)',
        'rgba(245, 158, 11, 0.8)',
        'rgba(239, 68, 68, 0.8)',
        'rgba(139, 92, 246, 0.8)',
        'rgba(236, 72, 153, 0.8)'
      ];

      // Main attendance chart
      const ctx = document.getElementById('attendanceChart').getContext('2d');
      let currentChart = null;

      function createBarChart() {
        return new Chart(ctx, {
          type: 'bar',
          data: {
            labels: courseLabels,
            datasets: [{
              label: 'Attendance Rate (%)',
              data: attendanceData,
              backgroundColor: colors.slice(0, courseLabels.length),
              borderColor: colors.slice(0, courseLabels.length).map(color => color.replace('0.8', '1')),
              borderWidth: 2,
              borderRadius: 8,
              borderSkipped: false,
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              },
              tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#fff',
                bodyColor: '#fff',
                borderColor: 'rgba(255, 255, 255, 0.1)',
                borderWidth: 1,
                cornerRadius: 8,
                callbacks: {
                  label: function(context) {
                    const index = context.dataIndex;
                    return [
                      `Attendance: ${context.parsed.y}%`,
                      `Present: ${presentData[index]}`,
                      `Total: ${totalData[index]}`
                    ];
                  }
                }
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                max: 100,
                ticks: {
                  callback: value => value + '%',
                  color: '#6B7280'
                },
                grid: {
                  color: 'rgba(0, 0, 0, 0.05)'
                }
              },
              x: {
                ticks: {
                  color: '#6B7280',
                  maxRotation: 45
                },
                grid: {
                  display: false
                }
              }
            }
          }
        });
      }

      function createDoughnutChart() {
        return new Chart(ctx, {
          type: 'doughnut',
          data: {
            labels: courseLabels,
            datasets: [{
              data: attendanceData,
              backgroundColor: colors.slice(0, courseLabels.length),
              borderColor: '#fff',
              borderWidth: 3,
              hoverOffset: 10
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'bottom',
                labels: {
                  padding: 20,
                  usePointStyle: true,
                  font: {
                    size: 12
                  }
                }
              },
              tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#fff',
                bodyColor: '#fff',
                borderColor: 'rgba(255, 255, 255, 0.1)',
                borderWidth: 1,
                cornerRadius: 8,
                callbacks: {
                  label: function(context) {
                    return context.label + ': ' + context.parsed + '%';
                  }
                }
              }
            }
          }
        });
      }

      // Initialize with bar chart
      currentChart = createBarChart();

      // Chart type switcher
      document.querySelectorAll('.chart-btn').forEach(btn => {
        btn.addEventListener('click', function() {
          const chartType = this.dataset.chart;
          
          // Update active state
          document.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
          this.classList.add('active');
          
          // Destroy current chart and create new one
          if (currentChart) {
            currentChart.destroy();
          }
          
          if (chartType === 'bar') {
            currentChart = createBarChart();
          } else {
            currentChart = createDoughnutChart();
          }
        });
      });

      // Overall attendance doughnut chart
      const overallCtx = document.getElementById('overallChart').getContext('2d');
      new Chart(overallCtx, {
        type: 'doughnut',
        data: {
          labels: ['Present', 'Absent'],
          datasets: [{
            data: [<?= $totalPresentAll ?>, <?= $totalAbsentAll ?>],
            backgroundColor: [
              'rgba(16, 185, 129, 0.8)',
              'rgba(239, 68, 68, 0.8)'
            ],
            borderColor: '#fff',
            borderWidth: 3,
            cutout: '70%'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              backgroundColor: 'rgba(0, 0, 0, 0.8)',
              titleColor: '#fff',
              bodyColor: '#fff',
              borderColor: 'rgba(255, 255, 255, 0.1)',
              borderWidth: 1,
              cornerRadius: 8
            }
          }
        }
      });
    <?php endif; ?>

    // Search functionality
    const searchInput = document.getElementById('courseSearch');
    if (searchInput) {
      searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const tableRows = document.querySelectorAll('#coursesTable tbody tr');
        
        tableRows.forEach(row => {
          const courseName = row.querySelector('.course-name').textContent.toLowerCase();
          const courseId = row.querySelector('.course-id').textContent.toLowerCase();
          
          if (courseName.includes(searchTerm) || courseId.includes(searchTerm)) {
            row.style.display = '';
          } else {
            row.style.display = 'none';
          }
        });
      });
    }
  </script>
</body>
</html>