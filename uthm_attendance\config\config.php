<?php

// 1) Autoload Composer packages (including PHPMailer)
require_once __DIR__ . '/../vendor/autoload.php';

// 2) Database connection settings (for MySQL)
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "uthm_attendance";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Set timezone for both PHP and MySQL to ensure consistency
date_default_timezone_set('Asia/Kuala_Lumpur'); // Malaysia timezone
$conn->query("SET time_zone = '+08:00'"); // Malaysia timezone for MySQL

// 3) SMTP settings (for PHPMailer)
define('SMTP_HOST',       'smtp.gmail.com');
define('SMTP_PORT',        587);
define('SMTP_USER',   '<EMAIL>');        // Replace with actual Gmail address
define('SMTP_PASS',   'bgyd bijp kwxq mscg');           // Replace with Gmail App Password
define('SMTP_ENCRYPTION','tls');

// 4) (Optional) Default “From” email if you prefer a constant
define('MAIL_FROM_EMAIL', SMTP_USER);
define('MAIL_FROM_NAME',  'UTHM Attendance System');

// 5) Email validation and security settings
define('EMAIL_VALIDATION_ENABLED', true);
define('EMAIL_DEBUG_MODE', true);   // Set to true for debugging
define('MAX_EMAIL_ATTEMPTS', 3);    // Maximum retry attempts for failed emails
?>
