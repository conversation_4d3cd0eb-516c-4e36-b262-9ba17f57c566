/**
 * Use this file to configure your Truffle project. It’s seeded with some
 * common settings for different networks and features like migrations,
 * compilation, and testing. Uncomment the ones you need or modify
 * them to suit your project as necessary.
 *
 * More information about configuration can be found at:
 *   https://trufflesuite.com/docs/truffle/reference/configuration
 */

module.exports = {
  /**
   * Networks define how you connect to your Ethereum client and let you set the
   * defaults web3 uses to send transactions. If you don’t specify one, Truffle
   * will spin up a managed Ganache instance for you on port 9545 when you
   * run `truffle develop` or `truffle test`. You can also ask a Truffle command
   * to use a specific network from the command line:
   *
   *   $ truffle migrate --network development
   */

  networks: {
    /**************************************************************************
     * Development network (Ganache GUI on port 7545)                       *
     *                                                                        *
     * Ganache GUI’s default RPC port is 7545, so we must tell <PERSON><PERSON><PERSON> to     *
     * use that host/port.                                                     *
     **************************************************************************/
    development: {
      host: "127.0.0.1",   // Localhost (this is your Ganache GUI)
      port: 7545,          // Ganache GUI’s default RPC port
      network_id: "*",     // Match any network id (Ganache will give it an id of 1337 or similar)
      // You can also add gas/gasPrice overrides here if needed:
      // gas: 6721975,
      // gasPrice: 20000000000,
    },

    // If you ever deploy to a public testnet, you can uncomment and configure the
    // following section to point at Infura (or Alchemy) and use HDWalletProvider.
    //
    // goerli: {
    //   provider: () => new HDWalletProvider(MNEMONIC, `https://goerli.infura.io/v3/${PROJECT_ID}`),
    //   network_id: 5,       // Goerli’s id
    //   confirmations: 2,    // # of confirmations to wait between deployments (default: 0)
    //   timeoutBlocks: 200,  // # of blocks before a deployment times out (minimum/default: 50)
    //   skipDryRun: true     // Skip dry run before migrations? (default: false for public nets)
    // },

    // You can add more networks here if needed.
  },

  // Set default Mocha options here, use special reporters, etc.
  mocha: {
    // timeout: 100000
  },

  // Configure your compilers
  compilers: {
    solc: {
      version: "0.8.0",      // Fetch exact version from solc-bin (default: Truffle’s version)
      // docker: true,        // Use “0.5.1” you’ve installed locally with Docker (default: false)
      // settings: {          // See the Solidity docs for advice about optimization and evmVersion
      //   optimizer: {
      //     enabled: false,
      //     runs: 200
      //   },
      //   evmVersion: "byzantium"
      // }
    }
  },

  // Truffle DB is currently disabled by default. To enable it, change enabled: false to true.
  // Do not migrate your contracts to Truffle DB without backing up your artifacts first.
  //
  // db: {
  //   enabled: false,
  //   host: "127.0.0.1",
  //   adapter: {
  //     name: "indexeddb",
  //     settings: {
  //       directory: ".db"
  //     }
  //   }
  // }
};
