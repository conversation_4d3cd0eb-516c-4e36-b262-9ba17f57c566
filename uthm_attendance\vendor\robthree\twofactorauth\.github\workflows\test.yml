name: Test

on:
  push:
  pull_request:

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-version: ['8.2', '8.3']

    steps:
    - uses: actions/checkout@v4

    - uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php-version }}
        tools: composer
        coverage: xdebug
        ini-values: error_reporting=E_ALL

    - uses: ramsey/composer-install@v3

    - run: composer lint-ci
    - run: composer phpstan
    - run: composer test
