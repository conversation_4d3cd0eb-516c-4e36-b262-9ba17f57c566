<?php
/**
 * Email Delivery Guide and Troubleshooting
 * Provides users with clear guidance on email delivery issues
 */

require_once 'config/config.php';

// Check if user is coming from a password reset flow
$fromPasswordReset = isset($_GET['from']) && $_GET['from'] === 'password_reset';
$userEmail = isset($_GET['email']) ? htmlspecialchars($_GET['email']) : '';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Delivery Guide - UTHM Attendance System</title>
    
    <!-- ─── GLOBAL CSS (same as other dashboards) ─── -->
    <link rel="stylesheet" href="dashboard/css/base-styles.css" />
    <link rel="stylesheet" href="dashboard/css/lecturer-header.css" />
    <link rel="stylesheet" href="dashboard/css/lecturer-sidebar.css" />
    <link rel="stylesheet" href="dashboard/css/lecturer-footer.css" />
    <link rel="stylesheet" href="dashboard/css/lecturer-dashboard-styles.css" />
    
    <!-- ─── ICONS ─── -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        /* ─────────── EMAIL DELIVERY GUIDE SPECIFIC STYLES ─────────── */
        .guide-container {
            max-width: 500px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        .guide-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-md);
        }

        .guide-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
        }

        .guide-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-md);
        }

        .guide-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .guide-subtitle {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .alert-message {
            padding: var(--spacing-md);
            border-radius: var(--border-radius);
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .alert-success {
            background-color: #d1f2eb;
            border: 1px solid #7dcea0;
            color: #0e6b47;
        }

        .alert-info {
            background-color: #d6eaf8;
            border: 1px solid #85c1e9;
            color: #1b4f72;
        }

        .email-highlight {
            background: var(--bg-light);
            padding: var(--spacing-sm);
            border-radius: var(--border-radius);
            font-family: monospace;
            word-break: break-all;
            border: 1px solid var(--border-color);
            margin: var(--spacing-sm) 0;
        }

        .step-card {
            background: var(--bg-light);
            border-left: 4px solid var(--primary-color);
            padding: var(--spacing-md);
            margin: var(--spacing-md) 0;
            border-radius: 0 var(--border-radius) var(--border-radius) 0;
        }

        .step-number {
            background: var(--primary-color);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: var(--spacing-sm);
        }

        .step-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
            display: flex;
            align-items: center;
        }

        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: #ffffff;
            border: 2px solid #3b82f6;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            min-height: 48px;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
            margin: var(--spacing-md) 0;
            text-decoration: none;
        }

        .submit-btn:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            border-color: #2563eb;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
            color: white;
        }

        .secondary-btn {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            border-color: #6b7280;
        }

        .secondary-btn:hover {
            background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
            border-color: #4b5563;
        }

        .back-link {
            display: block;
            text-align: center;
            margin-top: var(--spacing-lg);
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .back-link:hover {
            color: var(--primary-color);
        }

        .info-section {
            background: var(--bg-light);
            padding: var(--spacing-md);
            border-radius: var(--border-radius);
            margin: var(--spacing-md) 0;
            border: 1px solid var(--border-color);
        }

        .info-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .btn-group {
            display: flex;
            gap: var(--spacing-sm);
            margin: var(--spacing-md) 0;
        }

        .btn-group .submit-btn {
            flex: 1;
        }

        ul {
            margin: var(--spacing-sm) 0;
            padding-left: var(--spacing-lg);
        }

        li {
            margin-bottom: var(--spacing-xs);
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="guide-container">
        <div class="guide-card">
            <div class="guide-header">
                <div class="guide-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <h1 class="guide-title">Email Delivery Guide</h1>
                <p class="guide-subtitle">Your password reset email has been sent successfully!</p>
            </div>
            
            <?php if ($fromPasswordReset && $userEmail): ?>
            <div class="alert-message alert-success">
                <i class="fas fa-check-circle"></i>
                <div>
                    <strong>Email Sent Successfully!</strong><br>
                    A password reset email has been sent to:
                    <div class="email-highlight"><?php echo $userEmail; ?></div>
                </div>
            </div>
            <?php endif; ?>

            <div class="alert-message alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>Important:</strong> Email delivery can take 1-5 minutes. Please follow the steps below to locate your email.
            </div>

            <div class="info-title">
                <i class="fas fa-search"></i>
                How to Find Your Password Reset Email
            </div>

            <div class="step-card">
                <div class="step-title">
                    <span class="step-number">1</span>
                    Check Your Inbox
                </div>
                <p>Look for an email from <strong>UTHM Attendance System</strong> with the subject line containing "Password Reset".</p>
                <p><strong>From:</strong> <EMAIL></p>
            </div>

            <div class="step-card">
                <div class="step-title">
                    <span class="step-number">2</span>
                    Check Spam/Junk Folder
                </div>
                <p>Automated emails are often filtered into spam folders. Check your:</p>
                <ul>
                    <li><strong>Gmail:</strong> Spam folder</li>
                    <li><strong>Outlook:</strong> Junk Email folder</li>
                    <li><strong>Yahoo:</strong> Spam folder</li>
                    <li><strong>Other providers:</strong> Look for "Junk" or "Spam" folders</li>
                </ul>
            </div>

            <div class="step-card">
                <div class="step-title">
                    <span class="step-number">3</span>
                    Wait for Delivery
                </div>
                <p>Email delivery can take time depending on your email provider:</p>
                <ul>
                    <li><strong>Gmail:</strong> Usually 1-2 minutes</li>
                    <li><strong>Outlook/Hotmail:</strong> 2-5 minutes</li>
                    <li><strong>University Email:</strong> 5-15 minutes</li>
                    <li><strong>Other providers:</strong> Up to 30 minutes</li>
                </ul>
            </div>

            <div class="step-card">
                <div class="step-title">
                    <span class="step-number">4</span>
                    Add to Safe Senders
                </div>
                <p>To ensure future emails are delivered to your inbox, add <strong><EMAIL></strong> to your safe senders list.</p>
            </div>

            <div class="info-section">
                <div class="info-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    Still Can't Find the Email?
                </div>
                <p>If you don't receive the email within 15 minutes, you can:</p>
                <ul>
                    <li>Request a new password reset email</li>
                    <li>Try using a different email address</li>
                    <li>Contact your system administrator</li>
                </ul>
            </div>

            <div class="info-section">
                <div class="info-title">
                    <i class="fas fa-shield-alt"></i>
                    Security Information
                </div>
                <ul>
                    <li>The password reset link expires in <strong>1 hour</strong> for security</li>
                    <li>Each link can only be used <strong>once</strong></li>
                    <li>Never share your password reset link with anyone</li>
                    <li>If you didn't request this reset, contact your administrator</li>
                </ul>
            </div>

            <div class="btn-group">
                <a href="modules/forgot_password.php" class="submit-btn">
                    <i class="fas fa-redo"></i>
                    Request New Email
                </a>
                <a href="index.php" class="submit-btn secondary-btn">
                    <i class="fas fa-arrow-left"></i>
                    Back to Login
                </a>
            </div>

            <div style="text-align: center; margin-top: var(--spacing-lg); color: var(--text-secondary); font-size: 0.8rem;">
                <i class="fas fa-clock"></i>
                Generated on <?php echo date('Y-m-d H:i:s'); ?>
            </div>
        </div>
    </div>

    <!-- Auto-refresh reminder -->
    <script>
        // Show a reminder to check email after 2 minutes
        setTimeout(function() {
            if (confirm('It\'s been 2 minutes. Would you like to check if the email has arrived? Click OK to refresh this page.')) {
                location.reload();
            }
        }, 120000); // 2 minutes
    </script>
</body>
</html>
