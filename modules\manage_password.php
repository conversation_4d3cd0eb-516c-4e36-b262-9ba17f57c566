<?php
session_start();
require '../config/config.php';

if (!isset($_SESSION['user_id']) || !isset($_SESSION['role'])) {
    header("Location: ../index.php");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    $user_id = $_SESSION['user_id'];
    $role = $_SESSION['role'];

    if ($new_password !== $confirm_password) {
        $error = "Passwords do not match.";
    } else {
        $table = $role === 'student' ? 'student' : 'lecturer';
        $query = $conn->prepare("UPDATE $table SET Password = ?, is_first_login = FALSE WHERE {$table}ID = ?");
        $query->bind_param("si", $new_password, $user_id);
        if ($query->execute()) {
            header("Location: ../dashboard/{$role}.php");
            exit();
        } else {
            $error = "Failed to update password.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Change Password</title>
    <link rel="stylesheet" href="../test-styles.css">
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <style>
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            overflow-y: auto;
        }

        .change-password-wrapper {
            max-width: 500px;
            margin: 60px auto;
            padding: 30px;
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .change-password-wrapper h2 {
            text-align: center;
            color: #0078d4;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
            position: relative;
        }

        label {
            font-weight: bold;
            display: block;
            margin-bottom: 5px;
            color: #333;
        }

        input[type="password"] {
            width: 100%;
            padding: 12px;
            border-radius: 5px;
            border: 1px solid #ccc;
            font-size: 14px;
        }

        .toggle-password-icon {
            position: absolute;
            top: 38px;
            right: 10px;
            cursor: pointer;
            color: #0078d4;
        }

        .btn {
            width: 100%;
            padding: 12px;
            background-color: #0078d4;
            color: white;
            font-weight: bold;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        .btn:hover {
            background-color: #00509e;
        }

        .strength {
            font-size: 13px;
            margin-top: 5px;
        }

        .strength.weak { color: red; }
        .strength.medium { color: orange; }
        .strength.strong { color: green; }

        .message {
            text-align: center;
            color: red;
            margin-bottom: 10px;
        }

        .header-logo {
            text-align: center;
            margin-top: 30px;
        }

        .header-logo img {
            height: 60px;
        }
    </style>
</head>
<body>

<div class="header-logo">
    <img src="../assets/images/logo-uthm2.png" alt="UTHM Logo">
</div>

<div class="change-password-wrapper">
    <h2>Set Your New Password</h2>

    <?php if ($message): ?>
        <p class="message"><?= $message ?></p>
    <?php endif; ?>

    <form method="POST">
        <div class="form-group">
            <label for="new_password">New Password</label>
            <input type="password" name="new_password" id="new_password" required>
            <i class="fas fa-eye toggle-password-icon" onclick="togglePassword('new_password', this)"></i>
            <div id="strengthMessage" class="strength"></div>
        </div>

        <div class="form-group">
            <label for="confirm_password">Confirm Password</label>
            <input type="password" name="confirm_password" id="confirm_password" required>
            <i class="fas fa-eye toggle-password-icon" onclick="togglePassword('confirm_password', this)"></i>
        </div>

        <button type="submit" class="btn">Update Password</button>
    </form>
</div>

<footer>
    <p>UNIVERSITI TUN HUSSEIN ONN MALAYSIA</p>
</footer>

<script>
    const passwordInput = document.getElementById('new_password');
    const strengthMessage = document.getElementById('strengthMessage');

    passwordInput.addEventListener('input', function () {
        const val = passwordInput.value;
        let strength = '';
        if (val.length < 8 || !val.match(/[A-Z]/) || !val.match(/[a-z]/) || !val.match(/[0-9]/) || !val.match(/[^a-zA-Z0-9]/)) {
            strength = 'Weak';
            strengthMessage.className = 'strength weak';
        } else if (val.length >= 10 && val.match(/[A-Z]/) && val.match(/[a-z]/) && val.match(/[0-9]/) && val.match(/[^a-zA-Z0-9]/)) {
            strength = 'Strong';
            strengthMessage.className = 'strength strong';
        } else {
            strength = 'Medium';
            strengthMessage.className = 'strength medium';
        }
        strengthMessage.textContent = "Password Strength: " + strength;
    });

    function togglePassword(id, icon) {
        const input = document.getElementById(id);
        const isVisible = input.type === "text";
        input.type = isVisible ? "password" : "text";
        icon.classList.toggle("fa-eye");
        icon.classList.toggle("fa-eye-slash");
    }
</script>
</body>
</html>
