// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/// @title UTHM Attendance Smart Contract (matric-based duplicate guard)
/// @notice Records student attendance per course in an immutable ledger
contract Attendance {
    struct Record {
        address student;    // the wallet that submitted
        string  studentID;  // e.g. "AI250"
        string  studentName;
        uint256 courseId;
        string  courseName;
        uint256 timestamp;
    }

    // courseId → keccak256(matric) → whether already marked
    mapping(uint256 => mapping(bytes32 => bool)) public hasAttended;

    // chronological attendance logs
    Record[] public records;

    event AttendanceRecorded(
        address indexed student,
        string  indexed studentID,
        uint256 indexed courseId,
        string  studentName,
        string  courseName,
        uint256 timestamp
    );

    /// @notice Mark attendance for a given matric + course
    /// @param courseId numeric course identifier
    /// @param studentID student’s matric (e.g. "AI250")
    /// @param studentName full name of the student
    /// @param courseName name of the course/session
    function markAttendance(
        uint256 courseId,
        string calldata studentID,
        string calldata studentName,
        string calldata courseName
    ) external
    {
        bytes32 key = keccak256(bytes(studentID));
        require(
            !hasAttended[courseId][key],
            "Already marked attendance for this matric"
        );

        hasAttended[courseId][key] = true;

        records.push(Record({
            student:     msg.sender,
            studentID:   studentID,
            studentName: studentName,
            courseId:    courseId,
            courseName:  courseName,
            timestamp:   block.timestamp
        }));

        emit AttendanceRecorded(
            msg.sender,
            studentID,
            courseId,
            studentName,
            courseName,
            block.timestamp
        );
    }

    /// @notice Number of attendance records stored
    function getRecordCount() external view returns (uint256) {
        return records.length;
    }

    /// @notice Fetch a specific attendance record by index
    function getRecord(uint256 idx)
        external
        view
        returns (
            address student,
            string memory studentID,
            string memory studentName,
            uint256 courseId,
            string memory courseName,
            uint256 timestamp
        )
    {
        require(idx < records.length, "Index out of bounds");
        Record storage r = records[idx];
        return (
            r.student,
            r.studentID,
            r.studentName,
            r.courseId,
            r.courseName,
            r.timestamp
        );
    }
}
