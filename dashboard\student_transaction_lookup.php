<?php
// ─────────────────────────────────────────────────────────────────────────────
// STUDENT BLOCKCHAIN TRANSACTION LOOKUP PAGE
// ─────────────────────────────────────────────────────────────────────────────
// Allows students to lookup specific blockchain transactions by hash
// and view detailed attendance data stored on-chain
// ─────────────────────────────────────────────────────────────────────────────

session_start();
require_once '../config/config.php';

// ─────────────────────────────────────────────────────────────────────────────
// 1) AUTHENTICATION & SESSION VALIDATION
// ─────────────────────────────────────────────────────────────────────────────
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header("Location: ../login.php");
    exit();
}

// ─────────────────────────────────────────────────────────────────────────────
// 2) FETCH STUDENT INFO (for header/sidebar display)
// ─────────────────────────────────────────────────────────────────────────────
$student_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT Name, UserID, Photo FROM student WHERE StudentID = ?");
$stmt->bind_param("i", $student_id);
$stmt->execute();
$res = $stmt->get_result();
$student = $res->fetch_assoc();
$student_name   = $student['Name']   ?? 'Student Name';
$student_userid = $student['UserID'] ?? 'N/A';
$stmt->close();

// Build photo URL (fallback to default if empty)
$photo_url = !empty($student['Photo'])
    ? "../uploads/" . rawurlencode($student['Photo'])
    : "../assets/images/user1.png";

// ─────────────────────────────────────────────────────────────────────────────
// 3) TRANSACTION LOOKUP PROCESSING
// ─────────────────────────────────────────────────────────────────────────────
$transaction_hash = '';
$lookup_performed = false;
$lookup_error = '';
$transaction_data = null;
$attendance_data = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['transaction_hash'])) {
    $transaction_hash = trim($_POST['transaction_hash']);
    $lookup_performed = true;
    
    // Basic validation
    if (empty($transaction_hash)) {
        $lookup_error = 'Please enter a transaction hash.';
    } elseif (!preg_match('/^0x[a-fA-F0-9]{64}$/', $transaction_hash)) {
        $lookup_error = 'Invalid transaction hash format. Must be 66 characters starting with 0x.';
    } else {
        // Check if this transaction exists in our database and belongs to this student
        $stmt = $conn->prepare("
            SELECT 
                br.RecordID,
                br.AttendanceID,
                br.Timestamp as DB_Timestamp,
                br.Blockchain_Hash,
                ar.StudentID,
                ar.CourseID,
                ar.Att_Date,
                ar.Att_Status,
                ar.Remark,
                ar.Timestamp as Attendance_Timestamp,
                s.Name as StudentName,
                s.UserID as StudentUserID,
                c.Course_Name,
                c.Course_Code
            FROM blockchain_record br
            JOIN attendance_report ar ON br.AttendanceID = ar.AttendanceID
            JOIN student s ON ar.StudentID = s.StudentID
            JOIN course c ON ar.CourseID = c.CourseID
            WHERE br.Blockchain_Hash = ? AND ar.StudentID = ?
        ");
        $stmt->bind_param("si", $transaction_hash, $student_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            $lookup_error = 'Transaction not found or does not belong to your attendance records.';
        } else {
            $attendance_data = $result->fetch_assoc();
        }
        $stmt->close();
    }
}

// ─────────────────────────────────────────────────────────────────────────────
// 4) GREETING BASED ON TIME
// ─────────────────────────────────────────────────────────────────────────────
$hour = date('H');
if ($hour < 12) {
    $greeting = "Good Morning";
} elseif ($hour < 17) {
    $greeting = "Good Afternoon";
} else {
    $greeting = "Good Evening";
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Transaction Lookup - Student Dashboard</title>

  <!-- FontAwesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Google Font (Inter) -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- Base + Modular CSS from Student Dashboard -->
  <link rel="stylesheet" href="css/base-styles.css">
  <link rel="stylesheet" href="css/lecturer-header.css">
  <link rel="stylesheet" href="css/lecturer-sidebar.css">
  <link rel="stylesheet" href="css/lecturer-footer.css">
  <link rel="stylesheet" href="css/student-dashboard-enhanced.css">

  <!-- Web3.js for blockchain connectivity -->
  <script src="https://cdn.jsdelivr.net/npm/web3@1.9.0/dist/web3.min.js"></script>
</head>
<body>
  <!-- ─────────── HEADER ─────────── -->
  <div class="header">
    <div class="header-left">
      <img src="../assets/images/logo-uthm2.png" alt="UTHM Logo" class="logo">
    </div>
    <div class="header-right">
      <a href="../modules/qr_scan.php" class="qr-button">
        <i class="fas fa-qrcode"></i> Scan QR
      </a>
      <span class="user-id"><?= htmlspecialchars($student_userid) ?></span>
    </div>
  </div>

  <!-- ─────────── CONTAINER (SIDEBAR + MAIN) ─────────── -->
  <div class="container">
    <!-- ─────────── SIDEBAR ─────────── -->
    <div class="sidebar">
      <div class="profile">
        <!-- DYNAMIC PROFILE PHOTO -->
        <img src="<?= $photo_url ?>" alt="Profile Photo" class="profile-pic">
        <p class="profile-name"><?= htmlspecialchars($student_name) ?></p>
        <p class="profile-id"><?= htmlspecialchars($student_userid) ?></p>
      </div>
      <ul class="menu">
        <li>
          <a href="student.php">
            <i class="fas fa-tachometer-alt"></i> Dashboard
          </a>
        </li>
        <li>
          <a href="../modules/profile_student.php">
            <i class="fas fa-user"></i> Profile
          </a>
        </li>
        <li>
          <a href="../modules/register_courses.php">
            <i class="fas fa-edit"></i> Register Courses
          </a>
        </li>
        <li>
          <a href="../modules/report.php">
            <i class="fas fa-book"></i> Attendance Details
          </a>
        </li>
        <li>
          <a href="student_blockchain_records.php">
            <i class="fas fa-link"></i> Blockchain Records
          </a>
        </li>
        <li>
          <a href="student_transaction_lookup.php" class="active">
            <i class="fas fa-search"></i> Transaction Lookup
          </a>
        </li>
        <li>
          <a href="../logout.php">
            <i class="fas fa-sign-out-alt"></i> Logout
          </a>
        </li>
      </ul>
    </div>

    <!-- ─────────── MAIN CONTENT ─────────── -->
    <div class="main-content">
      <!-- Page Header -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-text">
            <h1><i class="fas fa-search"></i> Blockchain Transaction Lookup</h1>
            <p>Verify your attendance records by searching for specific blockchain transaction hashes</p>
          </div>
          <div class="header-date">
            <i class="fas fa-calendar-alt"></i>
            <span><?= date('l, F j, Y') ?></span>
          </div>
        </div>
      </div>

      <!-- Network Status Card -->
      <div class="card-container">
        <div class="stats-card">
          <div class="card-icon">
            <i class="fas fa-network-wired"></i>
          </div>
          <div class="card-content">
            <h3 id="networkStatus">Connecting...</h3>
            <p>Blockchain Network</p>
            <span class="card-trend" id="networkTrend">
              <i class="fas fa-sync fa-spin"></i> Initializing
            </span>
          </div>
        </div>
      </div>

      <!-- Transaction Search Form -->
      <div class="table-card">
        <div class="table-header">
          <h2><i class="fas fa-search"></i> Transaction Hash Lookup</h2>
          <p style="font-size: 0.875rem; color: var(--text-secondary); margin: 0.5rem 0 0 0;">
            Enter a blockchain transaction hash to view detailed attendance information stored on-chain.
          </p>
        </div>
        
        <form method="POST" class="lookup-form">
          <div class="form-group">
            <label for="transaction_hash">Transaction Hash</label>
            <input 
              type="text" 
              id="transaction_hash" 
              name="transaction_hash" 
              value="<?= htmlspecialchars($transaction_hash) ?>"
              placeholder="0x..." 
              class="form-input"
              pattern="^0x[a-fA-F0-9]{64}$"
              title="Must be a valid 66-character transaction hash starting with 0x"
              required
            >
            <small class="form-help">
              Enter a 66-character transaction hash (e.g., 0x1234...abcd)
            </small>
          </div>
          
          <div class="form-actions">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-search"></i> Lookup Transaction
            </button>
            <?php if ($lookup_performed): ?>
            <button type="button" onclick="clearForm()" class="btn btn-secondary">
              <i class="fas fa-times"></i> Clear
            </button>
            <?php endif; ?>
          </div>
        </form>

        <?php if ($lookup_performed): ?>
          <?php if ($lookup_error): ?>
            <!-- Error Message -->
            <div class="alert alert-error">
              <i class="fas fa-exclamation-triangle"></i>
              <?= htmlspecialchars($lookup_error) ?>
            </div>
          <?php elseif ($attendance_data): ?>
            <!-- Success - Show Transaction Details -->
            <div class="transaction-results">
              <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                Transaction found! This attendance record belongs to you.
              </div>
              
              <!-- Database Information -->
              <div class="info-section">
                <h3><i class="fas fa-database"></i> Database Record</h3>
                <div class="info-grid">
                  <div class="info-item">
                    <label>Student Name:</label>
                    <span><?= htmlspecialchars($attendance_data['StudentName']) ?></span>
                  </div>
                  <div class="info-item">
                    <label>Student ID:</label>
                    <span><?= htmlspecialchars($attendance_data['StudentUserID']) ?></span>
                  </div>
                  <div class="info-item">
                    <label>Course:</label>
                    <span><?= htmlspecialchars($attendance_data['Course_Code'] . ' - ' . $attendance_data['Course_Name']) ?></span>
                  </div>
                  <div class="info-item">
                    <label>Attendance Date:</label>
                    <span><?= htmlspecialchars(date('M j, Y', strtotime($attendance_data['Att_Date']))) ?></span>
                  </div>
                  <div class="info-item">
                    <label>Status:</label>
                    <span class="status-badge status-<?= strtolower($attendance_data['Att_Status']) ?>">
                      <?= htmlspecialchars($attendance_data['Att_Status']) ?>
                    </span>
                  </div>
                  <div class="info-item">
                    <label>Database Timestamp:</label>
                    <span><?= htmlspecialchars(date('M j, Y H:i:s', strtotime($attendance_data['DB_Timestamp']))) ?></span>
                  </div>
                </div>
              </div>

              <!-- Blockchain Information (will be populated by JavaScript) -->
              <div class="info-section">
                <h3><i class="fas fa-link"></i> Blockchain Transaction Details</h3>
                <div id="blockchainDetails" class="info-grid">
                  <div class="loading-message">
                    <i class="fas fa-sync fa-spin"></i> Loading blockchain data...
                  </div>
                </div>
              </div>
            </div>
          <?php endif; ?>
        <?php endif; ?>
      </div>
    </div>
  </div>

  <!-- Custom Styles for Transaction Lookup -->
  <style>
    .lookup-form {
      background: var(--card-bg);
      padding: var(--spacing-lg);
      border-radius: var(--border-radius);
      margin-top: var(--spacing-md);
    }

    .form-group {
      margin-bottom: var(--spacing-md);
    }

    .form-group label {
      display: block;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: var(--spacing-xs);
    }

    .form-input {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid var(--border-color);
      border-radius: var(--border-radius);
      font-size: 14px;
      font-family: 'Courier New', monospace;
      transition: var(--transition-smooth);
    }

    .form-input:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }

    .form-help {
      display: block;
      font-size: 12px;
      color: var(--text-secondary);
      margin-top: var(--spacing-xs);
    }

    .form-actions {
      display: flex;
      gap: var(--spacing-sm);
      margin-top: var(--spacing-lg);
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: var(--border-radius);
      font-weight: 600;
      cursor: pointer;
      transition: var(--transition-smooth);
      display: inline-flex;
      align-items: center;
      gap: var(--spacing-xs);
    }

    .btn-primary {
      background: var(--primary-color);
      color: white;
    }

    .btn-primary:hover {
      background: var(--primary-dark);
    }

    .btn-secondary {
      background: var(--secondary-color);
      color: white;
    }

    .btn-secondary:hover {
      background: #475569;
    }

    .alert {
      padding: var(--spacing-md);
      border-radius: var(--border-radius);
      margin: var(--spacing-md) 0;
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
    }

    .alert-error {
      background: #fef2f2;
      color: #dc2626;
      border: 1px solid #fecaca;
    }

    .alert-success {
      background: #f0fdf4;
      color: #16a34a;
      border: 1px solid #bbf7d0;
    }

    .transaction-results {
      margin-top: var(--spacing-lg);
    }

    .info-section {
      background: var(--card-bg);
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius);
      padding: var(--spacing-lg);
      margin: var(--spacing-md) 0;
    }

    .info-section h3 {
      margin: 0 0 var(--spacing-md) 0;
      color: var(--text-primary);
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: var(--spacing-md);
    }

    .info-item {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xs);
    }

    .info-item label {
      font-weight: 600;
      color: var(--text-secondary);
      font-size: 12px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .info-item span {
      color: var(--text-primary);
      font-weight: 500;
      word-break: break-all;
    }

    .status-badge {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      display: inline-block;
      width: fit-content;
    }

    .status-present {
      background: #dcfce7;
      color: #16a34a;
    }

    .status-absent {
      background: #fef2f2;
      color: #dc2626;
    }

    .loading-message {
      grid-column: 1 / -1;
      text-align: center;
      color: var(--text-secondary);
      padding: var(--spacing-lg);
    }

    .blockchain-data {
      font-family: 'Courier New', monospace;
      font-size: 13px;
    }

    .hash-display {
      font-family: 'Courier New', monospace;
      font-size: 12px;
      background: #f8fafc;
      padding: 8px;
      border-radius: 4px;
      border: 1px solid var(--border-color);
      word-break: break-all;
    }

    @media (max-width: 768px) {
      .info-grid {
        grid-template-columns: 1fr;
      }

      .form-actions {
        flex-direction: column;
      }

      .btn {
        justify-content: center;
      }
    }
  </style>

  <!-- JavaScript for Web3 Integration and Blockchain Data Retrieval -->
  <script>
    let web3, web3Ready = false;
    let AttendanceABI, AttendanceAddress;

    // Initialize Web3 connection on page load
    document.addEventListener('DOMContentLoaded', async function() {
      await initializeWeb3();
      await loadContractABI();

      // If we have attendance data, fetch blockchain details
      <?php if ($attendance_data): ?>
        await fetchBlockchainDetails('<?= $attendance_data['Blockchain_Hash'] ?>');
      <?php endif; ?>
    });

    async function initializeWeb3() {
      const statusElement = document.getElementById('networkStatus');
      const trendElement = document.getElementById('networkTrend');

      try {
        // Try MetaMask first
        if (window.ethereum) {
          web3 = new Web3(window.ethereum);
          console.log('Web3 initialized with MetaMask');
        } else {
          // Fallback to direct Ganache connection
          web3 = new Web3(new Web3.providers.HttpProvider('http://127.0.0.1:7545'));
          console.log('Web3 initialized with direct Ganache connection');
        }

        // Test connection with timeout
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Connection timeout')), 5000)
        );

        const networkId = await Promise.race([
          web3.eth.net.getId(),
          timeoutPromise
        ]);

        console.log('Connected to network:', networkId);
        web3Ready = true;

        // Update network status to connected
        if (statusElement) {
          statusElement.textContent = 'Connected';
        }
        if (trendElement) {
          trendElement.innerHTML = '<i class="fas fa-check"></i> Online';
        }
      } catch (error) {
        console.error('Failed to initialize Web3:', error);
        web3Ready = false;

        // Update network status to offline
        if (statusElement) {
          statusElement.textContent = 'Offline';
        }
        if (trendElement) {
          trendElement.innerHTML = '<i class="fas fa-times"></i> Disconnected';
        }
      }
    }

    async function loadContractABI() {
      try {
        const response = await fetch('../assets/js/Attendance.json');
        const artifact = await response.json();
        AttendanceABI = artifact.abi;

        // Get contract address for current network
        const networkId = await web3.eth.net.getId();
        const deployedNetwork = artifact.networks[networkId];
        if (deployedNetwork) {
          AttendanceAddress = deployedNetwork.address;
          console.log('Contract address loaded:', AttendanceAddress);
        }
      } catch (error) {
        console.error('Failed to load contract ABI:', error);
      }
    }

    async function fetchBlockchainDetails(txHash) {
      const detailsContainer = document.getElementById('blockchainDetails');

      if (!txHash || !web3Ready) {
        detailsContainer.innerHTML = `
          <div class="loading-message">
            <i class="fas fa-exclamation-triangle"></i>
            ${!txHash ? 'No transaction hash available' : 'Blockchain connection not available'}
          </div>
        `;
        return;
      }

      try {
        console.log('Fetching blockchain data for transaction:', txHash);

        // Create timeout promise
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Request timeout')), 10000)
        );

        // Fetch transaction data
        const [transaction, receipt, currentBlock] = await Promise.race([
          Promise.all([
            web3.eth.getTransaction(txHash),
            web3.eth.getTransactionReceipt(txHash),
            web3.eth.getBlockNumber()
          ]),
          timeoutPromise
        ]);

        console.log('Transaction data:', transaction);
        console.log('Receipt data:', receipt);
        console.log('Current block:', currentBlock);

        if (!transaction || !receipt) {
          throw new Error('Transaction not found on blockchain');
        }

        // Calculate confirmations
        const confirmations = currentBlock - receipt.blockNumber + 1;

        // Get block timestamp
        const block = await web3.eth.getBlock(receipt.blockNumber);
        const blockTimestamp = new Date(block.timestamp * 1000);

        // Try to decode attendance data from logs
        let attendanceData = null;
        if (receipt.logs && receipt.logs.length > 0 && AttendanceABI) {
          try {
            const contract = new web3.eth.Contract(AttendanceABI, AttendanceAddress);
            const decodedLogs = receipt.logs.map(log => {
              try {
                return web3.eth.abi.decodeLog(
                  AttendanceABI.find(item => item.name === 'AttendanceRecorded').inputs,
                  log.data,
                  log.topics.slice(1)
                );
              } catch (e) {
                return null;
              }
            }).filter(log => log !== null);

            if (decodedLogs.length > 0) {
              attendanceData = decodedLogs[0];
            }
          } catch (e) {
            console.warn('Could not decode attendance data:', e);
          }
        }

        // Display the blockchain details
        displayBlockchainDetails({
          transaction,
          receipt,
          confirmations,
          blockTimestamp,
          attendanceData
        });

      } catch (error) {
        console.error('Error fetching blockchain details:', error);
        detailsContainer.innerHTML = `
          <div class="loading-message">
            <i class="fas fa-exclamation-triangle"></i>
            Error loading blockchain data: ${error.message}
          </div>
        `;
      }
    }

    function displayBlockchainDetails(data) {
      const { transaction, receipt, confirmations, blockTimestamp, attendanceData } = data;
      const detailsContainer = document.getElementById('blockchainDetails');

      let attendanceInfo = '';
      if (attendanceData) {
        const onChainTimestamp = new Date(attendanceData.timestamp * 1000);
        attendanceInfo = `
          <div class="info-item">
            <label>On-Chain Student ID:</label>
            <span class="blockchain-data">${attendanceData.studentID || 'N/A'}</span>
          </div>
          <div class="info-item">
            <label>On-Chain Student Name:</label>
            <span class="blockchain-data">${attendanceData.studentName || 'N/A'}</span>
          </div>
          <div class="info-item">
            <label>On-Chain Course ID:</label>
            <span class="blockchain-data">${attendanceData.courseId || 'N/A'}</span>
          </div>
          <div class="info-item">
            <label>On-Chain Course Name:</label>
            <span class="blockchain-data">${attendanceData.courseName || 'N/A'}</span>
          </div>
          <div class="info-item">
            <label>On-Chain Timestamp:</label>
            <span class="blockchain-data">${onChainTimestamp.toLocaleString()}</span>
          </div>
        `;
      }

      detailsContainer.innerHTML = `
        <div class="info-item">
          <label>Transaction Hash:</label>
          <span class="hash-display">${transaction.hash}</span>
        </div>
        <div class="info-item">
          <label>Block Number:</label>
          <span class="blockchain-data">${receipt.blockNumber}</span>
        </div>
        <div class="info-item">
          <label>Block Timestamp:</label>
          <span class="blockchain-data">${blockTimestamp.toLocaleString()}</span>
        </div>
        <div class="info-item">
          <label>Confirmations:</label>
          <span class="blockchain-data">${confirmations}</span>
        </div>
        <div class="info-item">
          <label>From Address:</label>
          <span class="hash-display">${transaction.from}</span>
        </div>
        <div class="info-item">
          <label>To Address (Contract):</label>
          <span class="hash-display">${transaction.to}</span>
        </div>
        <div class="info-item">
          <label>Gas Used:</label>
          <span class="blockchain-data">${receipt.gasUsed.toLocaleString()}</span>
        </div>
        <div class="info-item">
          <label>Gas Price:</label>
          <span class="blockchain-data">${web3.utils.fromWei(transaction.gasPrice, 'gwei')} Gwei</span>
        </div>
        <div class="info-item">
          <label>Transaction Fee:</label>
          <span class="blockchain-data">${web3.utils.fromWei((BigInt(receipt.gasUsed) * BigInt(transaction.gasPrice)).toString(), 'ether')} ETH</span>
        </div>
        <div class="info-item">
          <label>Status:</label>
          <span class="status-badge ${receipt.status ? 'status-present' : 'status-absent'}">
            ${receipt.status ? 'Success' : 'Failed'}
          </span>
        </div>
        ${attendanceInfo}
      `;
    }

    function clearForm() {
      document.getElementById('transaction_hash').value = '';
      // Reload page to clear results
      window.location.href = window.location.pathname;
    }

    // Form validation
    document.getElementById('transaction_hash').addEventListener('input', function(e) {
      const value = e.target.value;
      const isValid = /^0x[a-fA-F0-9]{64}$/.test(value) || value === '';

      if (value && !isValid) {
        e.target.style.borderColor = '#dc2626';
      } else {
        e.target.style.borderColor = '';
      }
    });
  </script>
</body>
</html>
