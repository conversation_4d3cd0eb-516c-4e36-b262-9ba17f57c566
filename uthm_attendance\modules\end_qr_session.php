<?php
/**
 * ===================================================================
 * SECURE QR SESSION END ENDPOINT
 * AJAX endpoint for ending attendance sessions with security logging
 * ===================================================================
 */

session_start();
header('Content-Type: application/json');

// Security check: ensure user is authenticated
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

// Log QR session end for security audit
if (isset($_SESSION['active_attendance']) || isset($_SESSION['qr_token'])) {
    $user_id = $_SESSION['user_id'];
    $role = $_SESSION['role'];
    error_log("QR SESSION END: $role ID $user_id ended QR session at " . date('Y-m-d H:i:s'));
}

// Securely unset attendance-related session keys (leave other login data intact)
$attendanceKeys = ['active_attendance', 'qr_token', 'attendance_course_id', 'attendance_start_time'];
foreach ($attendanceKeys as $key) {
    if (isset($_SESSION[$key])) {
        unset($_SESSION[$key]);
    }
}

// Clear any attendance-related cookies
$attendanceCookies = ['qr_token', 'attendance_session'];
foreach ($attendanceCookies as $cookieName) {
    if (isset($_COOKIE[$cookieName])) {
        setcookie(
            $cookieName,
            '',
            time() - 3600,
            '/',
            '',
            isset($_SERVER['HTTPS']),
            true
        );
    }
}

// Optionally, you could also clean up any leftover PNGs for this course here.
// For now, we’ll rely on generate_qr_helper.php to auto‐cleanup older files
// next time a new QR is generated.

echo json_encode(['success' => true]);
exit();
