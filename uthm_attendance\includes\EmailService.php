<?php
/**
 * Enhanced Email Service for UTHM Attendance System
 * Provides robust email functionality with proper error handling,
 * validation, and retry mechanisms for warning letters.
 */

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../config/config.php';

use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\Exception;
use PHPMailer\PHPMailer\SMTP;

class EmailService {
    private $mail;
    private $lastError;
    private $debugLog;
    
    public function __construct() {
        $this->mail = new PHPMailer(true);
        $this->lastError = '';
        $this->debugLog = [];
        $this->configureSMTP();
    }
    
    /**
     * Configure SMTP settings from config.php
     */
    private function configureSMTP() {
        try {
            // Server settings
            $this->mail->isSMTP();
            $this->mail->Host       = SMTP_HOST;
            $this->mail->SMTPAuth   = true;
            $this->mail->Username   = SMTP_USER;
            $this->mail->Password   = SMTP_PASS;
            $this->mail->SMTPSecure = SMTP_ENCRYPTION;
            $this->mail->Port       = SMTP_PORT;
            
            // Debug settings
            if (defined('EMAIL_DEBUG_MODE') && EMAIL_DEBUG_MODE) {
                $this->mail->SMTPDebug = SMTP::DEBUG_SERVER;
                $this->mail->Debugoutput = function($str, $level) {
                    $this->debugLog[] = "SMTP Debug [{$level}]: {$str}";
                    error_log("SMTP Debug [{$level}]: {$str}");
                };
            } else {
                $this->mail->SMTPDebug = 0;
            }
            
            // Additional settings for better reliability
            $this->mail->SMTPOptions = array(
                'ssl' => array(
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true
                )
            );
            
        } catch (Exception $e) {
            $this->lastError = "SMTP Configuration Error: " . $e->getMessage();
            error_log($this->lastError);
        }
    }
    
    /**
     * Validate email address
     */
    public function validateEmail($email) {
        if (!defined('EMAIL_VALIDATION_ENABLED') || !EMAIL_VALIDATION_ENABLED) {
            return true;
        }
        
        // Basic email validation
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return false;
        }
        
        // Check if domain exists (optional, can be slow)
        $domain = substr(strrchr($email, "@"), 1);
        if (!checkdnsrr($domain, "MX") && !checkdnsrr($domain, "A")) {
            error_log("Email domain validation failed for: {$email}");
            return false;
        }
        
        return true;
    }
    
    /**
     * Send warning letter email with attachment
     */
    public function sendWarningLetter($studentEmail, $studentName, $courseName, $attDate, $lecturerName, $attachmentPath, $attachmentName) {
        $attempts = 0;
        $maxAttempts = defined('MAX_EMAIL_ATTEMPTS') ? MAX_EMAIL_ATTEMPTS : 3;
        
        while ($attempts < $maxAttempts) {
            $attempts++;
            
            try {
                // Validate email address
                if (!$this->validateEmail($studentEmail)) {
                    throw new Exception("Invalid student email address: {$studentEmail}");
                }
                
                // Clear any previous recipients
                $this->mail->clearAddresses();
                $this->mail->clearAttachments();
                $this->mail->clearCustomHeaders();
                
                // Set sender
                $fromName = defined('MAIL_FROM_NAME') ? MAIL_FROM_NAME : $lecturerName;
                $this->mail->setFrom(SMTP_USER, $fromName);
                $this->mail->addReplyTo(SMTP_USER, $lecturerName);
                
                // Set recipient
                $this->mail->addAddress($studentEmail, $studentName);
                
                // Add attachment if provided
                if ($attachmentPath && file_exists($attachmentPath)) {
                    $this->mail->addAttachment($attachmentPath, $attachmentName);
                }
                
                // Email content
                $this->mail->isHTML(true);
                $this->mail->Subject = "Warning Letter: Absence in \"{$courseName}\"";
                
                // HTML body
                $this->mail->Body = $this->generateHTMLBody($studentName, $courseName, $attDate, $lecturerName);
                
                // Plain text alternative
                $this->mail->AltBody = $this->generatePlainTextBody($studentName, $courseName, $attDate, $lecturerName);
                
                // Send email
                $result = $this->mail->send();
                
                if ($result) {
                    $this->logSuccess($studentEmail, $studentName, $courseName, $attempts);
                    return [
                        'success' => true,
                        'message' => "Warning letter successfully sent to {$studentName} ({$studentEmail})",
                        'attempts' => $attempts
                    ];
                }
                
            } catch (Exception $e) {
                $this->lastError = $e->getMessage();
                error_log("Email sending attempt {$attempts} failed: " . $this->lastError);
                
                if ($attempts >= $maxAttempts) {
                    $this->logFailure($studentEmail, $studentName, $courseName, $attempts, $this->lastError);
                    return [
                        'success' => false,
                        'message' => "Failed to send warning letter after {$maxAttempts} attempts. Error: " . $this->lastError,
                        'attempts' => $attempts
                    ];
                }
                
                // Wait before retry (exponential backoff)
                sleep($attempts * 2);
            }
        }
        
        return [
            'success' => false,
            'message' => "Failed to send warning letter after {$maxAttempts} attempts",
            'attempts' => $attempts
        ];
    }
    
    /**
     * Generate HTML email body
     */
    private function generateHTMLBody($studentName, $courseName, $attDate, $lecturerName) {
        return "
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .header { background-color: #f8f9fa; padding: 20px; text-align: center; border-bottom: 3px solid #007bff; }
                .content { padding: 20px; }
                .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
                .footer { background-color: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h2>UTHM Attendance System</h2>
                <h3>Official Warning Letter</h3>
            </div>
            <div class='content'>
                <p>Dear <strong>{$studentName}</strong>,</p>
                
                <div class='warning'>
                    <p><strong>⚠️ ATTENDANCE WARNING</strong></p>
                    <p>This letter serves as a formal warning regarding your absence from:</p>
                    <ul>
                        <li><strong>Course:</strong> {$courseName}</li>
                        <li><strong>Date:</strong> {$attDate}</li>
                    </ul>
                </div>
                
                <p>Your continued absence may negatively affect your final grade and academic standing. Please ensure you:</p>
                <ul>
                    <li>Submit any required documentation for this absence</li>
                    <li>Contact your lecturer immediately to discuss this matter</li>
                    <li>Improve your attendance to avoid further disciplinary action</li>
                </ul>
                
                <p>If you have any questions or concerns, please reply to this email or contact the lecturer directly.</p>
                
                <p>Best regards,<br>
                <strong>{$lecturerName}</strong><br>
                Course Lecturer</p>
            </div>
            <div class='footer'>
                <p>This is an automated message from the UTHM Attendance System.<br>
                Generated on " . date('Y-m-d H:i:s') . "</p>
            </div>
        </body>
        </html>";
    }
    
    /**
     * Generate plain text email body
     */
    private function generatePlainTextBody($studentName, $courseName, $attDate, $lecturerName) {
        return "UTHM ATTENDANCE SYSTEM - OFFICIAL WARNING LETTER\n\n" .
               "Dear {$studentName},\n\n" .
               "This letter serves as a formal warning regarding your absence from:\n" .
               "- Course: {$courseName}\n" .
               "- Date: {$attDate}\n\n" .
               "Your continued absence may negatively affect your final grade and academic standing.\n\n" .
               "Please ensure you:\n" .
               "- Submit any required documentation for this absence\n" .
               "- Contact your lecturer immediately to discuss this matter\n" .
               "- Improve your attendance to avoid further disciplinary action\n\n" .
               "If you have any questions or concerns, please reply to this email.\n\n" .
               "Best regards,\n{$lecturerName}\nCourse Lecturer\n\n" .
               "---\nThis is an automated message from the UTHM Attendance System.\n" .
               "Generated on " . date('Y-m-d H:i:s');
    }
    
    /**
     * Log successful email sending
     */
    private function logSuccess($email, $studentName, $courseName, $attempts) {
        $logMessage = "SUCCESS: Warning letter sent to {$studentName} ({$email}) for course {$courseName} after {$attempts} attempt(s)";
        error_log($logMessage);
    }
    
    /**
     * Log failed email sending
     */
    private function logFailure($email, $studentName, $courseName, $attempts, $error) {
        $logMessage = "FAILURE: Failed to send warning letter to {$studentName} ({$email}) for course {$courseName} after {$attempts} attempt(s). Error: {$error}";
        error_log($logMessage);
    }
    
    /**
     * Get last error message
     */
    public function getLastError() {
        return $this->lastError;
    }
    
    /**
     * Get debug log
     */
    public function getDebugLog() {
        return $this->debugLog;
    }
    
    /**
     * Send password reset email
     */
    public function sendPasswordResetEmail($email, $userName, $subject, $htmlBody, $textBody) {
        $attempts = 0;
        $maxAttempts = defined('MAX_EMAIL_ATTEMPTS') ? MAX_EMAIL_ATTEMPTS : 3;

        while ($attempts < $maxAttempts) {
            $attempts++;

            try {
                // Validate email address
                if (!$this->validateEmail($email)) {
                    throw new Exception("Invalid email address: {$email}");
                }

                // Clear any previous recipients
                $this->mail->clearAddresses();
                $this->mail->clearAttachments();
                $this->mail->clearCustomHeaders();

                // Set sender
                $fromName = defined('MAIL_FROM_NAME') ? MAIL_FROM_NAME : 'UTHM Attendance System';
                $this->mail->setFrom(SMTP_USER, $fromName);
                $this->mail->addReplyTo(SMTP_USER, $fromName);

                // Set recipient
                $this->mail->addAddress($email, $userName);

                // Email content
                $this->mail->isHTML(true);
                $this->mail->Subject = $subject;
                $this->mail->Body = $htmlBody;
                $this->mail->AltBody = $textBody;

                // Send email
                $result = $this->mail->send();

                if ($result) {
                    error_log("Password reset email successfully sent to {$userName} ({$email})");
                    return [
                        'success' => true,
                        'message' => "Password reset email successfully sent to {$userName} ({$email})",
                        'attempts' => $attempts
                    ];
                }

            } catch (Exception $e) {
                $this->lastError = $e->getMessage();
                error_log("Password reset email attempt {$attempts} failed: " . $this->lastError);

                if ($attempts >= $maxAttempts) {
                    return [
                        'success' => false,
                        'message' => "Failed to send password reset email after {$maxAttempts} attempts. Error: " . $this->lastError,
                        'attempts' => $attempts
                    ];
                }

                // Wait before retry (exponential backoff)
                sleep($attempts * 2);
            }
        }

        return [
            'success' => false,
            'message' => "Failed to send password reset email after {$maxAttempts} attempts",
            'attempts' => $attempts
        ];
    }

    /**
     * Test email configuration
     */
    public function testConfiguration() {
        try {
            // Test SMTP connection
            $this->mail->smtpConnect();
            $this->mail->smtpClose();

            return [
                'success' => true,
                'message' => 'SMTP configuration is valid and connection successful'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'SMTP configuration test failed: ' . $e->getMessage()
            ];
        }
    }
}
?>
