// assets/js/attendance.js

window.addEventListener("load", async () => {
  // ------------------------------------------------------------
  // 1) Detect Ethereum provider (MetaMask) for write calls
  // ------------------------------------------------------------
  let web3Write = null;
  if (window.ethereum) {
    web3Write = new Web3(window.ethereum);
    try {
      // Request account access for write transactions
      await window.ethereum.request({ method: "eth_requestAccounts" });
    } catch (error) {
      console.error("MetaMask access denied:", error);
      alert("Please allow MetaMask access to continue.");
      return;
    }
  } else if (window.web3) {
    // Fallback for older dapp browsers
    web3Write = new Web3(window.web3.currentProvider);
  }

  // ------------------------------------------------------------
  // 2) Create a read-only Web3 instance (HTTP) for history
  // ------------------------------------------------------------
  const GANACHE_HTTP = "http://127.0.0.1:7545";
  const web3Read = new Web3(new Web3.providers.HttpProvider(GANACHE_HTTP));

  // ------------------------------------------------------------
  // 3) Check network ID (use read-only provider)
  // ------------------------------------------------------------
  let networkId;
  try {
    networkId = await web3Read.eth.net.getId();
  } catch (err) {
    console.error("Cannot connect to Ganache:", err);
    alert("Cannot connect to the blockchain node. Make sure Ganache is running on " + GANACHE_HTTP);
    return;
  }

  // ------------------------------------------------------------
  // 4) Load Attendance.json (ABI + deployed addresses)
  // ------------------------------------------------------------
  let AttendanceArtifact;
  try {
    const response = await fetch("../assets/js/Attendance.json");
    AttendanceArtifact = await response.json();
  } catch (err) {
    console.error("Failed to load Attendance.json:", err);
    alert("Cannot load contract ABI. Ensure ../assets/js/Attendance.json exists.");
    return;
  }

  // ------------------------------------------------------------
  // 5) Confirm the contract is deployed on this network
  // ------------------------------------------------------------
  const deployedNetwork = AttendanceArtifact.networks[networkId];
  if (!deployedNetwork) {
    alert("Attendance contract is not deployed to network ID: " + networkId);
    return;
  }

  // ------------------------------------------------------------
  // 6) Instantiate two contract objects:
  //    - contractWrite for sending transactions (web3Write)
  //    - contractRead  for read-only calls (web3Read)
  // ------------------------------------------------------------
  let contractWrite = null;
  if (web3Write) {
    contractWrite = new web3Write.eth.Contract(
      AttendanceArtifact.abi,
      deployedNetwork.address
    );
  }
  const contractRead = new web3Read.eth.Contract(
    AttendanceArtifact.abi,
    deployedNetwork.address
  );

  // ------------------------------------------------------------
  // 7) Get current MetaMask account (for write)
  // ------------------------------------------------------------
  let currentAccount = null;
  if (web3Write) {
    try {
      const accounts = await web3Write.eth.getAccounts();
      if (accounts.length === 0) {
        alert("No MetaMask accounts found. Unlock MetaMask to mark attendance.");
      } else {
        currentAccount = accounts[0];
      }
    } catch (err) {
      console.warn("MetaMask locked or access denied:", err);
    }
    // Listen for account / network changes
    window.ethereum.on("accountsChanged", (accs) => {
      currentAccount = accs[0] || null;
      if (!currentAccount) {
        alert("MetaMask account disconnected.");
      }
    });
    window.ethereum.on("chainChanged", () => {
      window.location.reload();
    });
  }

  // ------------------------------------------------------------
  // 8) BIND “Mark Attendance” BUTTON (student use-case)
  // ------------------------------------------------------------
  const markBtn = document.getElementById("markAttendanceBtn");
  if (markBtn && contractWrite) {
    markBtn.addEventListener("click", async () => {
      const inputEl = document.getElementById("courseIdInput");
      const courseId = parseInt(inputEl.value);
      if (isNaN(courseId) || courseId <= 0) {
        alert("Enter a valid Course ID (positive integer).");
        return;
      }
      if (!currentAccount) {
        alert("Unlock MetaMask to send transactions.");
        return;
      }

      try {
        // 8.1) Send transaction: markAttendance(courseId)
        await contractWrite.methods
          .markAttendance(courseId)
          .send({ from: currentAccount })
          .on("transactionHash", (hash) => {
            console.log("Transaction sent, hash:", hash);
          })
          .on("receipt", (receipt) => {
            console.log("Transaction mined, receipt:", receipt);
            alert("Attendance recorded on blockchain!\nTxHash: " + receipt.transactionHash);

            // 8.2) Save txHash off-chain via AJAX
            fetch("/save_tx.php", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                courseId: courseId,
                txHash: receipt.transactionHash
              }),
            })
            .then((res) => res.json())
            .then((data) => {
              console.log("save_tx response:", data);
              if (data.success) {
                console.log("Blockchain record saved for AttendanceID:", data.attendanceId);
              } else {
                console.error("Error saving blockchain record:", data.error);
              }
            })
            .catch((err) => {
              console.error("Failed to call save_tx.php:", err);
            });
          })
          .on("error", (err) => {
            console.error("Error in markAttendance call:", err);
            alert("Failed to mark attendance. Check console for details.");
          });
      } catch (err) {
        console.error("Unexpected error:", err);
        alert("An unexpected error occurred. See console.");
      }
    });
  }

  // ------------------------------------------------------------
  // 9) AUTO-TRIGGER for STUDENT: if courseIdInput is prefilled
  // ------------------------------------------------------------
  const prefilledInput = document.getElementById("courseIdInput");
  if (prefilledInput && markBtn) {
    const preValue = prefilledInput.value.trim();
    if (preValue && !isNaN(preValue) && Number(preValue) > 0) {
      // Delay slightly to ensure Web3 + contract are initialized
      setTimeout(() => {
        markBtn.click();
      }, 500);
    }
  }

  // ------------------------------------------------------------
  // 10) LOAD STUDENT’s ON-CHAIN HISTORY (if #historyList exists)
  // ------------------------------------------------------------
  const historyList = document.getElementById("historyList");
  if (historyList) {
    try {
      const total = await contractRead.methods.getRecordCount().call();
      historyList.innerHTML = ""; // Clear previous entries
      for (let i = 0; i < total; i++) {
        const rec = await contractRead.methods.getRecord(i).call();
        const [studentAddr, recCourseId, timestamp] = rec;
        if (studentAddr.toLowerCase() === (currentAccount || "").toLowerCase()) {
          const date = new Date(timestamp * 1000).toLocaleString();
          const li = document.createElement("li");
          li.textContent = `Course ${recCourseId} — ${date}`;
          historyList.appendChild(li);
        }
      }
    } catch (err) {
      console.error("Error loading attendance history:", err);
      historyList.innerHTML = "<li>Error loading history</li>";
    }
  }
});
