# 📧 Email Configuration Setup Guide
## UTHM Attendance System - Warning Letter Email Functionality

This guide will help you configure the email functionality for sending automated warning letters to students.

## 🔍 Current Issue
The error "Invalid address: (From): your-smtp-username" indicates that the email configuration is using placeholder values instead of actual email credentials.

## 🛠️ Quick Fix Steps

### Step 1: Configure Email Credentials
Edit the file `config/config.php` and replace the placeholder values:

```php
// Replace these placeholder values with your actual email credentials:
define('SMTP_USER', '<EMAIL>');     // Your real email address
define('SMTP_PASS', 'your-actual-app-password');        // Your real app password
```

### Step 2: Choose Your Email Provider

#### Option A: Gmail (Recommended)
1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate App Password:**
   - Go to Google Account → Security → App passwords
   - Select "Mail" and generate a password
   - Use this App Password (not your regular password)

```php
define('SMTP_HOST',       'smtp.gmail.com');
define('SMTP_PORT',        587);
define('SMTP_USER',       '<EMAIL>');
define('SMTP_PASS',       'your-16-char-app-password');
define('SMTP_ENCRYPTION', 'tls');
```

#### Option B: Outlook/Hotmail
```php
define('SMTP_HOST',       'smtp-mail.outlook.com');
define('SMTP_PORT',        587);
define('SMTP_USER',       '<EMAIL>');
define('SMTP_PASS',       'your-password');
define('SMTP_ENCRYPTION', 'tls');
```

#### Option C: Yahoo Mail
```php
define('SMTP_HOST',       'smtp.mail.yahoo.com');
define('SMTP_PORT',        587);
define('SMTP_USER',       '<EMAIL>');
define('SMTP_PASS',       'your-app-password');
define('SMTP_ENCRYPTION', 'tls');
```

### Step 3: Test Configuration
1. Navigate to: `modules/test_email_config.php`
2. Click "Test SMTP Connection"
3. If successful, send a test email to verify functionality

## 📋 Complete Configuration Example

Here's a complete example of properly configured email settings:

```php
// config/config.php

// Gmail Configuration Example
define('SMTP_HOST',       'smtp.gmail.com');
define('SMTP_PORT',        587);
define('SMTP_USER',       '<EMAIL>');
define('SMTP_PASS',       'abcd efgh ijkl mnop');  // 16-character App Password
define('SMTP_ENCRYPTION', 'tls');

// System settings
define('MAIL_FROM_EMAIL', SMTP_USER);
define('MAIL_FROM_NAME',  'UTHM Attendance System');
define('EMAIL_VALIDATION_ENABLED', true);
define('EMAIL_DEBUG_MODE', false);
define('MAX_EMAIL_ATTEMPTS', 3);
```

## 🔧 Enhanced Features Implemented

### 1. Robust Email Service
- **Automatic retry mechanism** (up to 3 attempts)
- **Email validation** before sending
- **Professional HTML email templates**
- **Comprehensive error logging**
- **SMTP connection testing**

### 2. Improved Error Handling
- **Detailed error messages** for troubleshooting
- **Debug logging** for technical issues
- **Graceful failure handling** with user feedback
- **Configuration validation** checks

### 3. Professional Email Templates
- **HTML formatted emails** with UTHM branding
- **Plain text alternatives** for compatibility
- **Structured warning content** with course details
- **Professional styling** and layout

### 4. Testing and Diagnostics
- **Email configuration test tool** (`modules/test_email_config.php`)
- **SMTP connection testing**
- **Test email sending functionality**
- **Configuration status checking**

## 🚀 How to Use

### For Lecturers:
1. Go to **Attendance Report** page
2. Find students marked as "Absent"
3. Click the **"Warning"** button next to absent students
4. System will automatically:
   - Generate a professional PDF warning letter
   - Send email with attachment to student
   - Log the action for record keeping

### For Administrators:
1. Use **"Test Email Configuration"** link to verify setup
2. Monitor email logs for delivery status
3. Adjust settings in `config/config.php` as needed

## 🔍 Troubleshooting

### Common Issues:

#### "Invalid address" Error
- **Cause:** SMTP_USER contains placeholder value
- **Solution:** Replace with actual email address

#### "Authentication failed" Error
- **Cause:** Incorrect password or 2FA not enabled
- **Solution:** Use App Password for Gmail, enable 2FA

#### "Connection timeout" Error
- **Cause:** Incorrect SMTP host or port
- **Solution:** Verify SMTP settings for your provider

#### "SSL/TLS" Errors
- **Cause:** Incorrect encryption setting
- **Solution:** Use 'tls' for port 587, 'ssl' for port 465

### Debug Mode:
Enable debug mode for detailed troubleshooting:
```php
define('EMAIL_DEBUG_MODE', true);
```

## 📊 Email Delivery Features

### Automatic Features:
- ✅ **Email validation** before sending
- ✅ **Retry mechanism** for failed deliveries
- ✅ **Professional templates** with UTHM branding
- ✅ **PDF attachment** with warning letter
- ✅ **Delivery confirmation** and error reporting
- ✅ **Comprehensive logging** for audit trails

### Email Content Includes:
- Student name and course information
- Specific absence date and details
- Professional warning message
- Lecturer contact information
- UTHM system branding
- PDF attachment with formal letter

## 🔐 Security Considerations

### Best Practices:
1. **Use App Passwords** instead of regular passwords
2. **Enable 2-Factor Authentication** on email accounts
3. **Limit email account permissions** to sending only
4. **Regular password rotation** for security
5. **Monitor email logs** for suspicious activity

### Privacy Protection:
- Student emails are validated before sending
- No email addresses are stored in logs
- Secure SMTP connection with TLS encryption
- PDF attachments are automatically cleaned up

## 📞 Support

If you continue to experience issues:

1. **Check the test tool:** `modules/test_email_config.php`
2. **Review error logs** in your server's error log
3. **Verify email provider settings** with their documentation
4. **Test with a different email provider** if needed

## ✅ Verification Checklist

Before going live, ensure:
- [ ] Email credentials are properly configured
- [ ] SMTP connection test passes
- [ ] Test email sends successfully
- [ ] PDF attachments are included
- [ ] Email templates display correctly
- [ ] Error handling works properly
- [ ] Logs are being generated

---

**Note:** This enhanced email system provides enterprise-level reliability and professional presentation for the UTHM Attendance System's warning letter functionality.
