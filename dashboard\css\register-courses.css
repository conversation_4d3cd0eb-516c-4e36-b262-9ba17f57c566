  /* ────────────────────────────────────────────────────────────────────
       Enhanced Page Styles - Consistent with <PERSON><PERSON> Template Design
       ──────────────────────────────────────────────────────────────────── */

    /* Enhanced Page Header - Matching Student Dashboard */
    .page-header {
      background: var(--card-bg);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-lg);
      margin-bottom: var(--spacing-lg);
      border: 1px solid var(--border-color);
      box-shadow: var(--shadow-sm);
      position: relative;
      overflow: hidden;
    }

    .page-header::before {
      content: '';
      position: absolute;
      top: 0; left: 0; right: 0;
      height: 4px;
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    }

    .page-header h1 {
      font-size: 1.75rem;
      font-weight: 700;
      color: var(--text-primary);
      margin: 0 0 var(--spacing-xs) 0;
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .page-header h1 i {
      color: var(--primary-color);
      -webkit-text-fill-color: var(--primary-color);
    }

    .page-header p {
      font-size: 0.875rem;
      color: var(--text-secondary);
      margin: 0;
      font-weight: 400;
    }

    /* Enhanced Search Box */
    .search-box {
      width: 100%;
      max-width: 300px;
      position: relative;
      margin-left: auto;
    }

    .search-box i {
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: var(--text-secondary);
      font-size: 0.875rem;
      z-index: 2;
    }

    .search-box input {
      width: 100%;
      padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) 2.5rem;
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius);
      font-size: 0.875rem;
      background-color: var(--card-bg);
      transition: all 0.3s ease;
      font-weight: 400;
    }

    .search-box input:focus {
      outline: none;
      border-color: var(--primary-color);
      background-color: var(--card-bg);
      box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }

    .search-box input::placeholder {
      color: var(--text-secondary);
      font-style: normal;
    }

    /* Enhanced Register Button */
    .register-btn {
      background: var(--primary-color);
      color: var(--card-bg);
      border: none;
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
      font-size: 0.75rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      gap: 0.375rem;
      text-decoration: none;
      box-shadow: var(--shadow-sm);
      text-transform: capitalize;
    }

    .register-btn::before {
      content: '\f067';
      font-family: 'Font Awesome 6 Free';
      font-weight: 900;
      font-size: 0.75rem;
    }

    .register-btn:hover {
      background: var(--primary-dark);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }

    .register-btn:active {
      transform: scale(0.98);
    }

    /* Enhanced Alert Messages */
    .alert {
      padding: 1rem 1.5rem;
      border-radius: var(--border-radius-lg);
      margin-bottom: var(--spacing-lg);
      font-size: 0.875rem;
      display: flex;
      align-items: center;
      gap: 0.75rem;
      border-left: 4px solid;
      box-shadow: var(--shadow-sm);
      animation: slideIn 0.4s ease-out;
    }

    .alert.success {
      background: #f0fdf4;
      color: #166534;
      border-left-color: var(--success-color);
    }

    .alert.success::before {
      content: '\f00c';
      font-family: 'Font Awesome 6 Free';
      font-weight: 900;
      color: var(--success-color);
    }

    .alert.error {
      background: #fef2f2;
      color: #991b1b;
      border-left-color: var(--danger-color);
    }

    .alert.error::before {
      content: '\f071';
      font-family: 'Font Awesome 6 Free';
      font-weight: 900;
      color: var(--danger-color);
    }

    /* Two‐column layout for Registered & Available sections */
    .dual-section {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--space-lg);
      margin-bottom: var(--space-lg);
    }
    @media (max-width: 768px) {
      .dual-section {
        grid-template-columns: 1fr;
      }
    }

    /* Extra padding below the “Registered Courses” header so it visually balances with the search bar in Available section */
    .registered-header {
      margin-bottom: var(--spacing-md);
    }

    /* Summary Statistics Section */
    .stats-summary {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-lg);
    }

    .stat-card {
      background: var(--card-bg);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-lg);
      border: 1px solid var(--border-color);
      box-shadow: var(--shadow-sm);
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      transition: all 0.3s ease;
    }

    .stat-card:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }

    .stat-icon {
      width: 3rem;
      height: 3rem;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
      color: var(--card-bg);
      font-size: 1.25rem;
    }

    .stat-icon.registered {
      background: linear-gradient(135deg, var(--success-color) 0%, #34d399 100%);
    }

    .stat-icon.available {
      background: linear-gradient(135deg, var(--info-color) 0%, #38bdf8 100%);
    }

    .stat-icon.completion {
      background: linear-gradient(135deg, var(--warning-color) 0%, #fbbf24 100%);
    }

    .stat-content {
      flex: 1;
    }

    .stat-number {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--text-primary);
      line-height: 1;
      margin-bottom: 0.25rem;
    }

    .stat-label {
      font-size: 0.875rem;
      color: var(--text-secondary);
      font-weight: 500;
    }

    /* Enhanced Table Styling - Professional Azia Design */
    .table-card {
      background: var(--card-bg);
      border-radius: var(--border-radius-lg);
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--border-color);
      overflow: hidden;
      margin-bottom: var(--spacing-lg);
    }

    .table-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0 0 var(--spacing-md) 0;
      padding: var(--spacing-lg) var(--spacing-lg) 0;
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
    }

    .table-title::before {
      content: '\f02d';
      font-family: 'Font Awesome 6 Free';
      font-weight: 900;
      color: var(--primary-color);
      font-size: 1rem;
    }

    .table-wrapper {
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
      padding: 0 var(--spacing-lg) var(--spacing-lg);
    }

    .courses-table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
      margin: 0;
      background: var(--card-bg);
      font-size: 0.875rem;
    }

    .courses-table th {
      background: #f8fafc;
      color: #64748b;
      padding: 1rem 0.75rem;
      text-align: left;
      font-weight: 600;
      font-size: 0.75rem;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      border-bottom: 2px solid var(--border-color);
      position: sticky;
      top: 0;
      z-index: 10;
      white-space: nowrap;
    }

    .courses-table th:first-child {
      padding-left: 1.5rem;
    }

    .courses-table th:last-child {
      padding-right: 1.5rem;
    }

    .courses-table td {
      padding: 1.25rem 0.75rem;
      text-align: left;
      border-bottom: 1px solid #f1f5f9;
      vertical-align: middle;
      color: var(--text-primary);
      background: var(--card-bg);
      transition: all 0.2s ease;
    }

    .courses-table td:first-child {
      padding-left: 1.5rem;
    }

    .courses-table td:last-child {
      padding-right: 1.5rem;
    }

    .courses-table tbody tr:nth-child(even) {
      background: #fafbfc;
    }

    .courses-table tbody tr:hover {
      background: #f1f5f9;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    /* Enhanced Empty State */
    .empty-state {
      text-align: center;
      padding: 3rem 2rem;
      color: var(--text-secondary);
      background: var(--card-bg);
      border-radius: var(--border-radius-lg);
    }

    .empty-state i {
      font-size: 4rem;
      color: #e2e8f0;
      margin-bottom: 1.5rem;
      opacity: 0.7;
    }

    .empty-state h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0 0 0.75rem 0;
    }

    .empty-state p {
      font-size: 0.875rem;
      color: var(--text-secondary);
      margin: 0 0 2rem 0;
      max-width: 400px;
      margin-left: auto;
      margin-right: auto;
    }

    .empty-state .cta-button {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
      color: var(--card-bg);
      text-decoration: none;
      padding: 0.75rem 1.5rem;
      border-radius: 0.5rem;
      font-weight: 600;
      font-size: 0.875rem;
      transition: all 0.3s ease;
      box-shadow: var(--shadow-sm);
    }

    .empty-state .cta-button:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
      background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    }

    /* Enhanced Responsive Design */
    @media (max-width: 1024px) {
      .main-content {
        padding: var(--spacing-md);
      }

      .page-header {
        padding: var(--spacing-md);
      }

      .dual-section {
        gap: var(--spacing-md);
      }
    }

    @media (max-width: 768px) {
      .main-content {
        padding: var(--spacing-sm);
      }

      .page-header {
        padding: var(--spacing-sm);
      }

      .page-header h1 {
        font-size: 1.5rem;
      }

      .dual-section {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
      }

      .stats-summary {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: var(--spacing-sm);
      }

      .stat-card {
        padding: var(--spacing-md);
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
      }

      .stat-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
      }

      .stat-number {
        font-size: 1.25rem;
      }

      .stat-label {
        font-size: 0.75rem;
      }

      .courses-table {
        font-size: 0.75rem;
      }

      .courses-table th,
      .courses-table td {
        padding: 0.75rem 0.375rem;
      }

      .courses-table th:first-child,
      .courses-table td:first-child {
        padding-left: 1rem;
      }

      .courses-table th:last-child,
      .courses-table td:last-child {
        padding-right: 1rem;
      }

      .register-btn {
        font-size: 0.625rem;
        padding: 0.375rem 0.75rem;
      }

      .empty-state {
        padding: 2rem 1rem;
      }

      .empty-state i {
        font-size: 3rem;
      }
    }

    @media (max-width: 480px) {
      .page-header h1 {
        font-size: 1.25rem;
      }

      .stats-summary {
        grid-template-columns: 1fr;
      }

      .stat-card {
        padding: var(--spacing-sm);
      }

      .stat-icon {
        width: 2rem;
        height: 2rem;
        font-size: 0.875rem;
      }

      .stat-number {
        font-size: 1.125rem;
      }

      .courses-table th,
      .courses-table td {
        padding: 0.5rem 0.25rem;
      }

      .register-btn {
        font-size: 0.625rem;
        padding: 0.25rem 0.5rem;
      }
    }

    /* Animation for slide-in effect */
    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateX(-20px);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    .alert {
      animation: slideIn 0.4s ease-out;
    }
    /* Add this to style the Unregister button */
.unregister-btn {
  background: #e24c4b;
  color: #fff;
  border: none;
  padding: 6px 16px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  font-size: 1rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5em;
  transition: background 0.18s;
}
.unregister-btn:hover, .unregister-btn:focus {
  background: #c03030;
  color: #fff;
  outline: none;
}
.unregister-btn i {
  margin-right: 3px;
}