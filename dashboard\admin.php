<?php
session_start();
require '../config/config.php';

// ─────────────────────────────────────────────────────────────────────────────
//  SECURITY CHECK: Only 'admin' role can access this page
// ─────────────────────────────────────────────────────────────────────────────
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// ─────────────────────────────────────────────────────────────────────────────
//  FETCH STATS FROM DATABASE
// ─────────────────────────────────────────────────────────────────────────────
$total_students  = (int) $conn->query("SELECT COUNT(*) AS cnt FROM student")->fetch_assoc()['cnt'];
$total_lecturers = (int) $conn->query("SELECT COUNT(*) AS cnt FROM lecturer")->fetch_assoc()['cnt'];
$total_courses   = (int) $conn->query("SELECT COUNT(*) AS cnt FROM course")->fetch_assoc()['cnt'];

// Get admin info
$admin_userid = $_SESSION['user_id'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Admin Dashboard – UTHM Attendance</title>

  <!-- FontAwesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Base + Modular CSS (same as other dashboards) -->
  <link rel="stylesheet" href="css/base-styles.css">
  <link rel="stylesheet" href="css/lecturer-header.css">
  <link rel="stylesheet" href="css/lecturer-sidebar.css">
  <link rel="stylesheet" href="css/lecturer-footer.css">
  <link rel="stylesheet" href="css/lecturer-dashboard-styles.css">
  <link rel="stylesheet" href="css/admin-dashboard.css">
</head>
<body>
  <!-- ─────────────────── HEADER ─────────────────── -->
  <div class="header">
    <div class="header-left">
      <img src="../assets/images/logo-uthm2.png" alt="UTHM Logo" class="logo">
    </div>
    <div class="header-right">
      <span class="user-id">Admin: <?= htmlspecialchars($admin_userid) ?></span>
    </div>
  </div>

  <!-- ─────────────────── MAIN CONTAINER (SIDEBAR + CONTENT) ─────────────────── -->
  <div class="container">
    <!-- ─────────────────── SIDEBAR ─────────────────── -->
    <div class="sidebar">
      <div class="profile">
        <img src="../assets/images/user1.png" alt="Profile" class="profile-pic">
        <p class="profile-name">Administrator</p>
        <p class="profile-id"><?= htmlspecialchars($admin_userid) ?></p>
      </div>
      <ul class="menu">
        <li><a href="admin.php" class="active"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
        <li><a href="../modules/manage_users.php"><i class="fas fa-users"></i> Manage Users</a></li>
        <li><a href="../modules/manage_courses.php"><i class="fas fa-book"></i> Manage Courses</a></li>
        <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
      </ul>
    </div>

    <!-- ─────────────────── MAIN CONTENT ─────────────────── -->
    <div class="main-content">
      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">Admin Dashboard</h1>
        <p class="page-subtitle">Welcome back, Administrator. Here's your system overview.</p>
      </div>

      <!-- ─────────────────── SUMMARY CARDS ─────────────────── -->
      <div class="stats-container">
        <div class="stats-card">
          <div class="stats-icon">
            <i class="fas fa-user-graduate"></i>
          </div>
          <div class="stats-info">
            <span class="stats-number"><?= $total_students ?></span>
            <span class="stats-label">Total Students</span>
          </div>
        </div>
        <div class="stats-card">
          <div class="stats-icon">
            <i class="fas fa-chalkboard-teacher"></i>
          </div>
          <div class="stats-info">
            <span class="stats-number"><?= $total_lecturers ?></span>
            <span class="stats-label">Total Lecturers</span>
          </div>
        </div>
        <div class="stats-card">
          <div class="stats-icon">
            <i class="fas fa-book-open"></i>
          </div>
          <div class="stats-info">
            <span class="stats-number"><?= $total_courses ?></span>
            <span class="stats-label">Total Courses</span>
          </div>
        </div>
      </div>

      <!-- ─────────────────── RECENT ACTIVITY TABLE ─────────────────── -->
      <div class="courses-container">
        <div class="section-header">
          <h2 class="section-title"><i class="fas fa-history"></i> Recent Registrations</h2>
          <p class="section-subtitle">Latest course registrations in the system.</p>
        </div>

        <?php
        // Fetch recent registrations
        $recent_q = "
          SELECT
            s.Name AS student_name,
            c.Course_Name AS course_name,
            COALESCE(r.Section, 'N/A') AS section,
            DATE_FORMAT(r.RegistrationDate, '%Y-%m-%d') AS reg_date
          FROM course_registration r
          JOIN student s ON r.StudentID = s.StudentID
          JOIN course c ON r.CourseID = c.CourseID
          ORDER BY r.RegistrationDate DESC
          LIMIT 10
        ";
        $recent_res = $conn->query($recent_q);
        ?>

        <?php if ($recent_res && $recent_res->num_rows > 0): ?>
          <div class="table-container">
            <table class="courses-table">
              <thead>
                <tr>
                  <th>Student Name</th>
                  <th>Course Name</th>
                  <th>Section</th>
                  <th>Date Registered</th>
                </tr>
              </thead>
              <tbody>
                <?php while ($row = $recent_res->fetch_assoc()): ?>
                  <tr>
                    <td data-label="Student Name"><?= htmlspecialchars($row['student_name']) ?></td>
                    <td data-label="Course Name"><?= htmlspecialchars($row['course_name']) ?></td>
                    <td data-label="Section"><?= htmlspecialchars($row['section']) ?></td>
                    <td data-label="Date Registered"><?= htmlspecialchars($row['reg_date']) ?></td>
                  </tr>
                <?php endwhile; ?>
              </tbody>
            </table>
          </div>
        <?php else: ?>
          <div class="no-data-msg">
            <i class="fas fa-info-circle"></i>
            No recent registrations found.
          </div>
        <?php endif; ?>
      </div>
    </div> <!-- end .main-content -->
  </div>   <!-- end .container -->

  <!-- ─────────────────── FOOTER ─────────────────── -->
  <footer>
    <p>UNIVERSITI TUN HUSSEIN ONN MALAYSIA</p>
  </footer>
</body>
</html>
