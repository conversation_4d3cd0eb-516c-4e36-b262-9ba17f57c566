/* ===================================================================
   LECTURER DASHBOARD STYLES
   (Aligned with Attendance Report & Base Styles)
   ================================================================= */

/* Reuse the same CSS custom properties defined in base-styles.css */

/* ─────────── PAGE HEADER ─────────── */
.page-header {
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.page-title {
  color: var(--text-primary);
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 var(--spacing-xs) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.page-title::before {
  content: '\f2dc'; /* Dashboard icon (FontAwesome) */
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  color: var(--primary-color);
  font-size: 1.5rem;
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0;
  font-weight: 400;
}

/* ─────────── STATS CARDS ─────────── */
.stats-container {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
}

.stats-card {
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-md);
  flex: 1 1 220px;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: var(--transition-smooth);
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stats-icon {
  font-size: 2.5rem;
  color: var(--primary-color);
  width: 48px;
  text-align: center;
}

.stats-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.stats-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
}

.stats-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

/* ─────────── SECTION HEADER ─────────── */
.section-header {
  background: var(--card-bg);
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
  padding: var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-bottom: none;
  box-shadow: var(--shadow-sm);
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.section-title::before {
  content: '\f19d'; /* Blackboard Teacher icon */
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  color: var(--primary-color);
  font-size: 1.25rem;
}

.section-subtitle {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
  font-weight: 400;
}

/* ─────────── NO DATA MESSAGE ─────────── */
.no-data-msg {
  background: var(--light-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.9rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.no-data-msg i {
  font-size: 2rem;
  color: var(--warning-color);
}

/* ─────────── COURSES TABLE ─────────── */
.courses-container {
  background: var(--card-bg);
  border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.courses-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
  table-layout: fixed;
}

/* Column widths */
.courses-table th:nth-child(1),
.courses-table td:nth-child(1) { width: 15%; } /* CourseID */
.courses-table th:nth-child(2),
.courses-table td:nth-child(2) { width: 50%; } /* Course Name */
.courses-table th:nth-child(3),
.courses-table td:nth-child(3) { width: 15%; } /* Reg Count */
.courses-table th:nth-child(4),
.courses-table td:nth-child(4) { width: 20%; } /* Actions */

/* Header row */
.courses-table thead th {
  background: #f8fafc;
  color: #64748b;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 2px solid var(--border-color);
}

.courses-table thead th:first-child {
  padding-left: var(--spacing-md);
  border-top-left-radius: var(--border-radius-lg);
}

.courses-table thead th:last-child {
  padding-right: var(--spacing-md);
  border-top-right-radius: var(--border-radius-lg);
}

/* Body rows */
.courses-table tbody tr:nth-child(even) {
  background: #fafbfc;
}

.courses-table tbody tr:hover {
  background: #f1f5f9;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.courses-table tbody tr:last-child td {
  border-bottom: none;
}

.courses-table tbody tr:last-child td:first-child {
  border-bottom-left-radius: var(--border-radius-lg);
}

.courses-table tbody tr:last-child td:last-child {
  border-bottom-right-radius: var(--border-radius-lg);
}

/* Cells */
.courses-table td {
  padding: 1rem;
  border-bottom: 1px solid #f1f5f9;
  color: var(--text-primary);
  background: var(--card-bg);
  vertical-align: middle;
}

.courses-table td:first-child {
  padding-left: var(--spacing-md);
}

.courses-table td:last-child {
  padding-right: var(--spacing-md);
}

/* Action Buttons in table */
.courses-table .action-btn {
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  text-decoration: none;
  margin: 0.125rem;
  min-width: 66px;
  justify-content: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  text-transform: capitalize;
}

/* Generate QR Button (Cyan) */
.courses-table .generate-qr {
  background: #06b6d4;
  color: white;
}
.courses-table .generate-qr::before {
  content: '\f093';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  font-size: 0.75rem;
}
.courses-table .generate-qr:hover {
  background: #0891b2;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(6, 182, 212, 0.25);
}

/* View Attendance Button (Purple) */
.courses-table .view-att {
  background: #8b5cf6;
  color: white;
}
.courses-table .view-att::before {
  content: '\f06e';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  font-size: 0.75rem;
}
.courses-table .view-att:hover {
  background: #7c3aed;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(139, 92, 246, 0.25);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .stats-container {
    flex-direction: column;
    gap: var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .stats-card {
    padding: var(--spacing-md);
  }
  .stats-icon {
    font-size: 2rem;
  }
  .stats-number {
    font-size: 1.75rem;
  }
  .stats-label {
    font-size: 0.875rem;
  }

  .section-header {
    padding: var(--spacing-md);
  }
  .section-title {
    font-size: 1.125rem;
  }
  .section-subtitle {
    font-size: 0.8rem;
  }

  .courses-table {
    font-size: 0.8rem;
    min-width: 600px;
  }

  .courses-table thead th,
  .courses-table td {
    padding: 0.75rem;
  }
  .courses-table .action-btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.625rem;
    min-width: 55px;
  }
}

@media (max-width: 480px) {
  .courses-table {
    min-width: 500px;
  }
  .courses-table .action-btn {
    padding: 0.25rem 0.375rem;
    font-size: 0.625rem;
    min-width: 50px;
  }
}

/* ─────────── FORM ELEMENTS ─────────── */
.add-course-form {
  background: var(--card-bg);
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: var(--spacing-lg);
  align-items: end;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.required {
  color: #ef4444;
  margin-left: 2px;
}

.form-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.875rem;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--card-bg);
  transition: var(--transition-smooth);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
}

.form-input::placeholder {
  color: var(--text-secondary);
}

/* Popup Message */
.popup-message {
  position: fixed;
  top: var(--spacing-lg);
  left: 50%;
  transform: translateX(-50%);
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 300px;
  font-size: 0.875rem;
  font-weight: 500;
}

.popup-message .close-btn {
  background: transparent;
  border: none;
  font-size: 1.25rem;
  color: #155724;
  cursor: pointer;
  margin-left: var(--spacing-md);
  padding: 0;
  line-height: 1;
}

.popup-message .close-btn:hover {
  opacity: 0.7;
}

/* ─────────── USER MANAGEMENT FORM STYLES ─────────── */
.radio-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  cursor: pointer;
}

.radio-label input[type="radio"] {
  accent-color: var(--primary-color);
  margin: 0;
}

.password-container {
  position: relative;
}

.toggle-password {
  position: absolute;
  right: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 0.875rem;
}

.toggle-password:hover {
  color: var(--primary-color);
}

.hint {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
  font-style: italic;
}

/* ─────────── BLOCKCHAIN VERIFICATION STYLES ─────────── */
/* Compact Course Selection */
.course-selection-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.course-card.compact {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--card-bg);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 60px;
}

.course-card.compact:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color);
}

.course-card.compact.selected {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, var(--primary-color) 0%, rgba(99, 102, 241, 0.1) 100%);
}

.course-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  color: var(--white);
  border-radius: var(--border-radius);
  font-size: 1rem;
}

.course-card.compact.selected .course-icon {
  background: var(--white);
  color: var(--primary-color);
}

.course-info {
  flex: 1;
}

.course-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.course-card.compact.selected .course-name {
  color: var(--primary-color);
}

/* Clickable table rows */
.clickable-row {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.clickable-row:hover {
  background-color: rgba(99, 102, 241, 0.05);
}

.hash-display {
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  color: var(--text-primary);
  cursor: pointer;
}

.hash-display:hover {
  color: var(--primary-color);
}

.loading-block {
  color: var(--text-secondary);
  font-style: italic;
}

/* ─────────── BLOCKCHAIN MODAL STYLES ─────────── */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--spacing-lg);
}

.modal-container {
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background: var(--primary-color);
  color: var(--white);
}

.modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.modal-close {
  background: none;
  border: none;
  color: var(--white);
  font-size: 1.25rem;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius);
  transition: background-color 0.2s ease;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
}

.modal-loading,
.modal-error {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xl);
  color: var(--text-secondary);
  font-size: 1rem;
}

.modal-loading i {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.modal-error {
  color: var(--danger);
}

.info-section {
  margin-bottom: var(--spacing-xl);
}

.info-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-xs);
  border-bottom: 2px solid var(--border-color);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-md);
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-item label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
}

.info-item span {
  font-size: 0.875rem;
  color: var(--text-primary);
  word-break: break-all;
}

.hash-full {
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  background: var(--gray-50);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

/* Timestamp display styling */
.timestamp-display {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.timestamp-value {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.timestamp-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-style: italic;
}

.timestamp-diff {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

.timestamp-diff.delayed {
  background-color: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

.timestamp-diff.early {
  background-color: rgba(16, 185, 129, 0.1);
  color: #059669;
}

.timestamp-diff.same {
  background-color: rgba(99, 102, 241, 0.1);
  color: var(--primary-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .timestamp-display {
    align-items: flex-start;
  }

  .timestamp-value {
    font-size: 0.8rem;
  }

  .timestamp-diff {
    font-size: 0.7rem;
    padding: 1px 4px;
  }

  .hash-display {
    font-size: 0.7rem;
  }

  /* Course selection responsive */
  .course-selection-container {
    grid-template-columns: 1fr;
  }

  .course-card.compact {
    min-height: 50px;
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .course-icon {
    width: 35px;
    height: 35px;
    font-size: 0.875rem;
  }

  .course-name {
    font-size: 0.8rem;
  }

  /* Modal responsive */
  .modal-overlay {
    padding: var(--spacing-sm);
  }

  .modal-container {
    max-height: 95vh;
  }

  .modal-header {
    padding: var(--spacing-md);
  }

  .modal-title {
    font-size: 1.1rem;
  }

  .modal-content {
    padding: var(--spacing-md);
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .section-title {
    font-size: 0.9rem;
  }

  .info-item label,
  .info-item span {
    font-size: 0.8rem;
  }

  .hash-full {
    font-size: 0.7rem;
    padding: var(--spacing-xs);
  }
}

/* ===================================================================
   FILTER SECTION STYLES
   Professional, minimalist filter interface consistent with other pages
   ================================================================= */

.filter-section {
  background: var(--card-bg);
  border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
  border: 1px solid var(--border-color);
  border-top: none;
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.filter-form {
  margin: 0;
}

.filter-controls {
  display: grid;
  grid-template-columns: 2fr 1fr auto;
  gap: var(--spacing-lg);
  align-items: end;
}

.filter-controls .form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.filter-controls .form-group label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.filter-controls .form-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.875rem;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--card-bg);
  transition: var(--transition-smooth);
}

.filter-controls .form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
}

.filter-controls .form-input::placeholder {
  color: var(--text-secondary);
}

/* Date input styling */
.filter-controls input[type="date"] {
  cursor: pointer;
}

.filter-controls input[type="date"]::-webkit-calendar-picker-indicator {
  cursor: pointer;
  color: var(--primary-color);
  font-size: 1rem;
}

/* Action buttons */
.form-actions {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.action-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.875rem;
  font-weight: 600;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  text-decoration: none;
  white-space: nowrap;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.action-btn.primary {
  background: var(--primary-color);
  color: var(--white);
}

.action-btn.primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(99, 102, 241, 0.25);
}

.action-btn.secondary {
  background: var(--text-secondary);
  color: var(--white);
}

.action-btn.secondary:hover {
  background: var(--text-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);
}

.action-btn i {
  font-size: 0.75rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .filter-controls {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
  }

  .form-actions {
    grid-column: 1 / -1;
    justify-content: center;
    margin-top: var(--spacing-sm);
  }
}

@media (max-width: 768px) {
  .filter-section {
    padding: var(--spacing-md);
  }

  .filter-controls {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .form-actions {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .filter-section {
    padding: var(--spacing-sm);
  }

  .filter-controls {
    gap: var(--spacing-sm);
  }

  .action-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.8rem;
  }
}

/* ===================================================================
   FILTER STATUS BAR STYLES
   Compact status indicator for applied filters
   ================================================================= */

.filter-status-bar {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: var(--border-radius);
  padding: var(--spacing-sm) var(--spacing-md);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-md);
  font-size: 0.875rem;
}

.status-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
  font-weight: 500;
}

.status-info i {
  font-size: 0.875rem;
  color: var(--primary-color);
}

.status-filters {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

.filter-tag {
  background: var(--primary-color);
  color: var(--white);
  padding: 2px var(--spacing-xs);
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
}

.clear-filters {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--danger);
  text-decoration: none;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 2px var(--spacing-xs);
  border-radius: var(--border-radius);
  transition: all 0.2s ease;
}

.clear-filters:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

.clear-filters i {
  font-size: 0.75rem;
}

/* Responsive filter status bar */
@media (max-width: 768px) {
  .filter-status-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .status-filters {
    width: 100%;
  }

  .clear-filters {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .filter-status-bar {
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .status-filters {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }
}

/* ===================================================================
   COURSE DETAILS PAGE STYLES
   Specific styles for the course details page
   ================================================================= */

/* Course Information Container */
.course-info-container {
  background: var(--card-bg);
  border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
  border: 1px solid var(--border-color);
  border-top: none;
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.info-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.info-value.student-count {
  color: var(--primary-color);
  font-weight: 600;
}

.info-value.student-count i {
  font-size: 0.875rem;
}

/* Students Container */
.students-container {
  background: var(--card-bg);
  border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
  border: 1px solid var(--border-color);
  border-top: none;
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-lg);
}

.students-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
}

.students-table thead {
  background: linear-gradient(135deg, var(--primary-color) 0%, rgba(99, 102, 241, 0.9) 100%);
  color: var(--white);
}

.students-table th {
  padding: var(--spacing-md) var(--spacing-lg);
  text-align: left;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: none;
}

.students-table tbody tr {
  border-bottom: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.students-table tbody tr:nth-child(even) {
  background: rgba(99, 102, 241, 0.02);
}

.students-table tbody tr:hover {
  background: rgba(99, 102, 241, 0.05);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.students-table td {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 0.875rem;
  color: var(--text-primary);
  border: none;
  vertical-align: middle;
}

.students-table td:first-child {
  font-weight: 600;
  color: var(--text-secondary);
  width: 60px;
  text-align: center;
}

.matric-badge {
  background: var(--primary-color);
  color: var(--white);
  padding: 2px var(--spacing-xs);
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

/* Responsive Design for Course Details */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .course-info-container {
    padding: var(--spacing-md);
  }

  .students-table {
    font-size: 0.8rem;
  }

  .students-table thead {
    display: none;
  }

  .students-table tbody tr {
    display: block;
    margin-bottom: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    background: var(--card-bg);
  }

  .students-table td {
    display: block;
    padding: var(--spacing-xs) 0;
    border: none;
    text-align: left;
  }

  .students-table td:before {
    content: attr(data-label) ": ";
    font-weight: 600;
    color: var(--text-secondary);
    display: inline-block;
    width: 120px;
  }

  .students-table td:first-child:before {
    content: "No.: ";
  }
}

@media (max-width: 480px) {
  .course-info-container {
    padding: var(--spacing-sm);
  }

  .info-grid {
    gap: var(--spacing-sm);
  }

  .students-table td:before {
    width: 100px;
    font-size: 0.75rem;
  }
}

/* Print Styles */
@media print {
  .header, .sidebar, .stats-container, .section-header, .filter-section, .filter-status-bar, footer {
    display: none !important;
  }
  .main-content {
    padding: 0;
    background: white;
  }
  .courses-container, .course-info-container, .students-container {
    box-shadow: none;
    border: 1px solid #000;
  }
  .courses-table, .students-table {
    font-size: 0.75rem;
  }
  .courses-table th, .courses-table td,
  .students-table th, .students-table td {
    padding: 0.5rem;
    border: 1px solid #000 !important;
  }
  .courses-container *, .course-info-container *, .students-container *,
  .courses-table *, .students-table * {
    box-shadow: none !important;
  }
}
