/* ===================================================================
   QR GENERATE STYLES (qr-generate-styles.css)
   Matches base‐styles, lecturer‐header, lecturer‐sidebar, lecturer‐footer
   ================================================================= */

/* ─────────── PAGE HEADER ─────────── */
.page-header {
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.page-title {
  color: var(--text-primary);
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 var(--spacing-xs) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}
.page-title::before {
  content: '\f2db'; /* QR Code icon (FontAwesome) */
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  color: var(--primary-color);
  font-size: 1.5rem;
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0;
  font-weight: 400;
}

/* ─────────── FORM CONTAINER ─────────── */
.form-container-qr {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.generate-qr-form {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.form-group {
  flex: 1 1 250px;
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  font-size: 0.9rem;
}

.form-group select,
.form-group input[type="date"] {
  padding: var(--spacing-xs);
  font-size: 0.9rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  background: var(--light-bg);
  color: var(--text-primary);
  transition: border-color 0.2s ease;
}

.form-group select:focus,
.form-group input[type="date"]:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* ─────────── FORM ACTION BUTTON ─────────── */
.form-actions {
  flex: 1 1 100%;
  display: flex;
  justify-content: flex-end;
  margin-top: var(--spacing-md);
}

.btn-generate {
  background: var(--primary-color);
  color: #fff;
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: 0.95rem;
  font-weight: 600;
  border: none;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.btn-generate i {
  font-size: 1rem;
}

.btn-generate:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* ─────────── ERROR MESSAGES ─────────── */
.msg.error {
  background: #f8d7da;
  color: #721c24;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-sm);
  border: 1px solid #f5c2c7;
  margin-bottom: var(--spacing-lg);
}
.msg.error ul {
  padding-left: var(--spacing-md);
  margin: 0;
}
.msg.error li {
  margin-bottom: var(--spacing-xs);
  font-size: 0.9rem;
}

/* ─────────── QR MODAL ─────────── */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(15, 23, 42, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  max-width: 400px;
  width: 90%;
  text-align: center;
  position: relative;
}

.qr-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}
.qr-title i {
  color: var(--success-color);
  font-size: 1.25rem;
}

.qr-subtext {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

/* Container for QR image + countdown badge */
#qr-code-container {
  position: relative;
  display: inline-block;
  margin-bottom: var(--spacing-md);
}

#qr-code-image {
  width: 250px;
  height: 250px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
}

#countdown {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: rgba(0,0,0,0.7);
  color: #fff;
  padding: 6px 10px;
  border-radius: 50%;
  font-weight: bold;
  font-size: 0.9rem;
}

/* Modal Actions */
.modal-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.modal-actions .btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: 0.9rem;
  font-weight: 600;
  border-radius: var(--border-radius-sm);
  border: none;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

/* End Session Button (red/danger) */
.modal-actions .btn-secondary {
  background: #ef4444;
  color: #fff;
}
.modal-actions .btn-secondary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* Close Button (blue/primary) */
.modal-actions .btn-primary {
  background: var(--primary-color);
  color: #fff;
}
.modal-actions .btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* ─────────── RESPONSIVE ADJUSTMENTS ─────────── */
@media (max-width: 768px) {
  .generate-qr-form {
    flex-direction: column;
  }
  .form-group {
    flex: 1 1 100%;
  }
  .form-actions, .modal-actions {
    flex-direction: column;
  }
  #qr-code-image {
    width: 200px;
    height: 200px;
  }
}

/* ─────────── PRINT STYLES ─────────── */
@media print {
  .header, .sidebar, .form-container-qr, footer {
    display: none !important;
  }
  .modal-content {
    box-shadow: none !important;
    border: none !important;
  }
  #qr-code-image {
    width: 150px !important;
    height: 150px !important;
  }
}
