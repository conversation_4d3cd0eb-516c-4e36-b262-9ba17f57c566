<?php
session_start();
require '../config/config.php';

// Only lecturers may access
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'lecturer') {
    header("Location: ../index.php");
    exit();
}

// 1) DATABASE CAPABILITIES DETECTION
function checkDatabaseCapabilities($conn) {
    $capabilities = [
        'course_code' => false,
        'course_status' => false,
        'course_assignments' => false,
        'registration_section' => false
    ];

    // Check if Course_Code column exists
    $checkCodeColumn = "SHOW COLUMNS FROM course LIKE 'Course_Code'";
    $result = $conn->query($checkCodeColumn);
    $capabilities['course_code'] = $result && $result->num_rows > 0;

    // Check if Status column exists
    $checkStatusColumn = "SHOW COLUMNS FROM course LIKE 'Status'";
    $result = $conn->query($checkStatusColumn);
    $capabilities['course_status'] = $result && $result->num_rows > 0;

    // Check if course_assignments table exists
    $checkAssignmentsTable = "SHOW TABLES LIKE 'course_assignments'";
    $result = $conn->query($checkAssignmentsTable);
    $capabilities['course_assignments'] = $result && $result->num_rows > 0;

    // Check if Section column exists in course_registration
    $checkSectionColumn = "SHOW COLUMNS FROM course_registration LIKE 'Section'";
    $result = $conn->query($checkSectionColumn);
    $capabilities['registration_section'] = $result && $result->num_rows > 0;

    return $capabilities;
}

$dbCapabilities = checkDatabaseCapabilities($conn);

// 2) Fetch lecturer info for sidebar
$lecturer_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT Name, UserID, profile_pic FROM lecturer WHERE LectID = ?");
$stmt->bind_param("i", $lecturer_id);
$stmt->execute();
$lectResult  = $stmt->get_result();
$lecturer    = $lectResult->fetch_assoc();
$stmt->close();

$lecturer_name   = $lecturer['Name']   ?? 'Lecturer Name';
$lecturer_userid = $lecturer['UserID'] ?? 'N/A';

// Profile picture URL
$photo_url = !empty($lecturer['profile_pic'])
    ? "../assets/images/" . rawurlencode($lecturer['profile_pic'])
    : "../assets/images/user1.png";

// 3) Enhanced filtering: support assignment, course, and date filtering
$filterAssignment = isset($_GET['assignment']) ? intval($_GET['assignment']) : 0;
$filterCourse = isset($_GET['course']) ? intval($_GET['course']) : 0;
$filterDate = isset($_GET['filter_date']) ? trim($_GET['filter_date']) : '';

$whereClause = "";
$params = [];
$types = "";

// Build base WHERE clause for lecturer access
if ($dbCapabilities['course_assignments']) {
    // Use EXISTS subquery to check lecturer assignment
    $whereClause = "WHERE EXISTS (
        SELECT 1 FROM course_assignments ca
        WHERE ca.CourseID = ar.CourseID
        AND ca.LectID = ?
    )";
    $params = [$lecturer_id];
    $types = "i";
} else {
    $whereClause = "WHERE c.LectID = ?";
    $params = [$lecturer_id];
    $types = "i";
}

// Add assignment/course filter
if ($dbCapabilities['course_assignments']) {
    if ($filterAssignment > 0) {
        $whereClause .= " AND EXISTS (
            SELECT 1 FROM course_assignments ca
            WHERE ca.CourseID = ar.CourseID
            AND ca.AssignmentID = ?
        )";
        $params[] = $filterAssignment;
        $types .= "i";
    } elseif ($filterCourse > 0) {
        $whereClause .= " AND ar.CourseID = ?";
        $params[] = $filterCourse;
        $types .= "i";
    }
} else {
    if ($filterCourse > 0) {
        $whereClause .= " AND ar.CourseID = ?";
        $params[] = $filterCourse;
        $types .= "i";
    }
}

// Add date filter
if ($filterDate !== '') {
    $whereClause .= " AND ar.Att_Date = ?";
    $params[] = $filterDate;
    $types .= "s";
}

// 4) Build enhanced SQL query
if ($dbCapabilities['course_assignments']) {
    $sql = "
        SELECT DISTINCT
            ar.AttendanceID,
            s.Name               AS StudentName,
            s.EthAddress         AS StudentEth,
            " . ($dbCapabilities['course_code'] ? "c.Course_Code," : "") . "
            c.Course_Name        AS CourseName,
            COALESCE(
                (SELECT ca.Section FROM course_assignments ca
                 WHERE ca.CourseID = ar.CourseID
                 AND ca.LectID = {$lecturer_id}
                 LIMIT 1),
                ''
            ) as Section,
            ar.CourseID,
            ar.Att_Date,
            ar.Att_Status,
            ar.Remark,
            ar.Timestamp         AS ReportedAt,
            br.Blockchain_Hash,
            br.Timestamp         AS OnChainAt
        FROM attendance_report ar
        JOIN student s    ON ar.StudentID = s.StudentID
        JOIN course c     ON ar.CourseID = c.CourseID
        LEFT JOIN blockchain_record br
          ON ar.AttendanceID = br.AttendanceID
        $whereClause
        ORDER BY ar.Att_Date DESC, ar.Timestamp DESC
    ";
} else {
    $sql = "
        SELECT DISTINCT
            ar.AttendanceID,
            s.Name               AS StudentName,
            s.EthAddress         AS StudentEth,
            " . ($dbCapabilities['course_code'] ? "c.Course_Code," : "") . "
            c.Course_Name        AS CourseName,
            '' as Section,
            ar.CourseID,
            ar.Att_Date,
            ar.Att_Status,
            ar.Remark,
            ar.Timestamp         AS ReportedAt,
            br.Blockchain_Hash,
            br.Timestamp         AS OnChainAt
        FROM attendance_report ar
        JOIN student s    ON ar.StudentID = s.StudentID
        JOIN course c     ON ar.CourseID = c.CourseID
        LEFT JOIN blockchain_record br
          ON ar.AttendanceID = br.AttendanceID
        $whereClause
        ORDER BY ar.Att_Date DESC, ar.Timestamp DESC
    ";
}

// Execute query with enhanced parameter handling and error handling
$result = false;
try {
    if (!empty($params)) {
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $conn->error);
        }
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        if (!$result) {
            throw new Exception("Query execution failed: " . $stmt->error);
        }
    } else {
        $result = $conn->query($sql);
        if (!$result) {
            throw new Exception("Query failed: " . $conn->error);
        }
    }
} catch (Exception $e) {
    // Log the error and provide fallback
    error_log("Lecturer blockchain records query error: " . $e->getMessage());
    error_log("SQL: " . $sql);

    // Create empty result set for graceful degradation
    $result = new stdClass();
    $result->num_rows = 0;
}

// 5) Fetch enhanced course list for filtering
$courseList = [];

try {
    if ($dbCapabilities['course_assignments']) {
        // New system: fetch course assignments with sections
        $courseStmt = $conn->prepare("
            SELECT
                ca.AssignmentID,
                c.CourseID,
                " . ($dbCapabilities['course_code'] ? "c.Course_Code," : "c.CourseID as Course_Code,") . "
                c.Course_Name,
                ca.Section
            FROM course_assignments ca
            JOIN course c ON ca.CourseID = c.CourseID
            WHERE ca.LectID = ? AND ca.Status = 'Active'
            " . ($dbCapabilities['course_status'] ? "AND c.Status = 'Active'" : "") . "
            ORDER BY c.Course_Name ASC, ca.Section ASC
        ");
        if ($courseStmt) {
            $courseStmt->bind_param("i", $lecturer_id);
            $courseStmt->execute();
            $courseRes = $courseStmt->get_result();
            while ($row = $courseRes->fetch_assoc()) {
                $courseList[] = [
                    'AssignmentID' => $row['AssignmentID'],
                    'CourseID'     => $row['CourseID'],
                    'Course_Code'  => $row['Course_Code'],
                    'Course_Name'  => $row['Course_Name'],
                    'Section'      => $row['Section'],
                    'DisplayName'  => $row['Course_Code'] . ' - ' . $row['Course_Name'] . ' (' . $row['Section'] . ')'
                ];
            }
            $courseStmt->close();
        }
    } else {
        // Legacy system: fetch courses directly
        $courseStmt = $conn->prepare("
            SELECT
                CourseID,
                " . ($dbCapabilities['course_code'] ? "Course_Code," : "CourseID as Course_Code,") . "
                Course_Name
            FROM course
            WHERE LectID = ?
            " . ($dbCapabilities['course_status'] ? "AND Status = 'Active'" : "") . "
            ORDER BY Course_Name
        ");
        if ($courseStmt) {
            $courseStmt->bind_param("i", $lecturer_id);
            $courseStmt->execute();
            $courseRes = $courseStmt->get_result();
            while ($row = $courseRes->fetch_assoc()) {
                $courseList[] = [
                    'CourseID'    => $row['CourseID'],
                    'Course_Code' => $row['Course_Code'],
                    'Course_Name' => $row['Course_Name'],
                    'Section'     => '',
                    'DisplayName' => $row['Course_Code'] . ' - ' . $row['Course_Name']
                ];
            }
            $courseStmt->close();
        }
    }
} catch (Exception $e) {
    // Log error and continue with empty course list
    error_log("Course list query error: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Blockchain Report – UTHM Attendance</title>

  <!-- FontAwesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Base + Modular CSS (same as other dashboards) -->
  <link rel="stylesheet" href="css/base-styles.css">
  <link rel="stylesheet" href="css/lecturer-header.css">
  <link rel="stylesheet" href="css/lecturer-sidebar.css">
  <link rel="stylesheet" href="css/lecturer-footer.css">
  <link rel="stylesheet" href="css/lecturer-dashboard-styles.css">

  <!-- 1) Load Web3.js for read‐only calls -->
  <script src="https://cdn.jsdelivr.net/npm/web3@1.9.0/dist/web3.min.js"></script>
  <!-- 2) Contract ABI + deployed address -->
  <script src="../assets/js/Attendance.json" type="application/json"></script> 
  <!-- 3) Read‐only verification script -->
  <script src="../assets/js/attendance_read.js" defer></script>


  
  <script>
    let web3, AttendanceABI, AttendanceNet;
    let web3Ready = false;
    let connectionStatus = 'initializing';

    window.addEventListener("DOMContentLoaded", async () => {
      console.log("🔄 Initializing blockchain connection...");

      // 1) Load ABI/contract info
      try {
        const res  = await fetch('../assets/js/Attendance.json');
        const json = await res.json();
        AttendanceABI = json.abi;
        console.log("✅ Contract ABI loaded successfully");

        // Check if network 5777 (Ganache) exists
        if (json.networks && json.networks["5777"]) {
          AttendanceNet = json.networks["5777"].address;
          console.log("✅ Contract address loaded:", AttendanceNet);
        } else {
          console.warn("⚠️ Contract not deployed on Ganache network (5777)");
          connectionStatus = 'contract_not_deployed';
        }

        // Initialize Web3 connection with multiple fallbacks
        await initializeWeb3Connection();

      } catch (err) {
        console.error("❌ Could not load contract ABI:", err);
        connectionStatus = 'abi_load_failed';
      }
    });

    async function initializeWeb3Connection() {
      try {
        // Method 1: Try MetaMask first
        if (window.ethereum) {
          console.log("🔄 Attempting MetaMask connection...");
          web3 = new Web3(window.ethereum);

          // Test the connection
          const networkId = await web3.eth.net.getId();
          console.log("🌐 Connected to network:", networkId);

          if (networkId.toString() === "5777") {
            console.log("✅ Successfully connected to Ganache via MetaMask");
            web3Ready = true;
            connectionStatus = 'connected_metamask';
            return;
          } else {
            console.warn("⚠️ MetaMask connected to wrong network:", networkId);
            // Try to switch to Ganache or use direct connection
          }
        }

        // Method 2: Direct HTTP connection to Ganache
        console.log("🔄 Attempting direct Ganache connection...");
        const ganacheWeb3 = new Web3(new Web3.providers.HttpProvider('http://127.0.0.1:7545'));

        // Test direct connection
        const networkId = await ganacheWeb3.eth.net.getId();
        const blockNumber = await ganacheWeb3.eth.getBlockNumber();

        if (networkId.toString() === "5777") {
          console.log("✅ Successfully connected to Ganache directly");
          console.log("📊 Current block number:", blockNumber);
          web3 = ganacheWeb3;
          web3Ready = true;
          connectionStatus = 'connected_direct';
          return;
        }

      } catch (err) {
        console.error("❌ All Web3 connection methods failed:", err);
        connectionStatus = 'connection_failed';
        web3Ready = false;
      }
    }

    // 2) verifyOnChain: Called when "Check" button is clicked
    async function verifyOnChain(btn, attendanceID, recordedHash) {
      btn.disabled    = true;
      btn.textContent = "Checking...";
      btn.classList.remove("status-ok","status-fail");
      btn.classList.add("status-pending");

      try {
        // If no recordedHash in DB, immediate fail
        if (!recordedHash) {
          btn.textContent = "❌ No TX";
          btn.classList.remove("status-pending");
          btn.classList.add("status-fail");
          return;
        }

        if (!web3 || !AttendanceABI || !AttendanceNet) {
          throw new Error("Web3 or contract ABI not ready");
        }
        const contract = new web3.eth.Contract(AttendanceABI, AttendanceNet);

        // Fetch the on-chain transaction receipt
        const receipt = await web3.eth.getTransactionReceipt(recordedHash);
        if (receipt && receipt.blockNumber) {
          btn.textContent = "✅ On‐Chain (Block " + receipt.blockNumber + ")";
          btn.classList.remove("status-pending");
          btn.classList.add("status-ok");
        } else {
          btn.textContent = "❌ Not yet mined";
          btn.classList.remove("status-pending");
          btn.classList.add("status-fail");
        }
      } catch (err) {
        console.error("verifyOnChain error:", err);
        btn.textContent = "❌ Error";
        btn.classList.remove("status-pending");
        btn.classList.add("status-fail");
      }
    }
  </script>


</head>

<body>
  <!-- ─────────── HEADER ─────────── -->
  <div class="header">
    <div class="header-left">
      <img src="../assets/images/logo-uthm2.png" alt="UTHM Logo" class="logo">
    </div>
    <div class="header-right">
      <span class="user-id"><?= htmlspecialchars($lecturer_userid) ?></span>
    </div>
  </div>

  <!-- ─────────── MAIN CONTAINER (SIDEBAR + CONTENT) ─────────── -->
  <div class="container">
    <!-- ─────────── SIDEBAR ─────────── -->
    <div class="sidebar">
      <div class="profile">
        <img src="<?= $photo_url ?>" alt="Profile" class="profile-pic">
        <p class="profile-name"><?= htmlspecialchars($lecturer_name) ?></p>
        <p class="profile-id"><?= htmlspecialchars($lecturer_userid) ?></p>
      </div>
      <ul class="menu">
        <li><a href="lecturer.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
        <li><a href="../modules/profile_lecturer.php"><i class="fas fa-user"></i> Profile</a></li>
        <li><a href="../modules/qr_generate.php"><i class="fas fa-qrcode"></i> Generate QR Code</a></li>
        <li><a href="attendance_report.php"><i class="fas fa-book"></i> Attendance Report</a></li>
        <li><a href="../modules/courses_details.php"><i class="fas fa-graduation-cap"></i> Course Details</a></li>
        <li><a href="blockchain_records.php" class="active"><i class="fas fa-link"></i> Blockchain Report</a></li>
        <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
      </ul>
    </div>

    <!-- ─────────── MAIN CONTENT ─────────── -->
    <div class="main-content">
      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">Blockchain Report</h1>
        <p class="page-subtitle">View blockchain attendance records for your courses.</p>
      </div>

      <!-- ─────────────────── FILTER SECTION ─────────────────── -->
      <div class="section-header">
        <h2 class="section-title"><i class="fas fa-filter"></i> Filter Records</h2>
        <p class="section-subtitle">Filter blockchain records by course and date.</p>
      </div>

      <div class="filter-section">
        <form method="GET" action="blockchain_records.php" class="filter-form">
          <div class="filter-controls">
            <!-- Course Filter -->
            <div class="form-group">
              <label for="course_filter">Course</label>
              <select name="<?= $dbCapabilities['course_assignments'] ? 'assignment' : 'course' ?>" id="course_filter" class="form-input">
                <option value="0">All <?= $dbCapabilities['course_assignments'] ? 'Course Sections' : 'Courses' ?></option>
                <?php foreach ($courseList as $c):
                  if ($dbCapabilities['course_assignments']) {
                      $cid = intval($c['AssignmentID']);
                      $cname = htmlspecialchars($c['DisplayName']);
                      $isSelected = ($filterAssignment === $cid) ? 'selected' : '';
                  } else {
                      $cid = intval($c['CourseID']);
                      $cname = htmlspecialchars($c['DisplayName']);
                      $isSelected = ($filterCourse === $cid) ? 'selected' : '';
                  }
                ?>
                  <option value="<?= $cid ?>" <?= $isSelected ?>><?= $cname ?></option>
                <?php endforeach; ?>
              </select>
            </div>

            <!-- Date Filter -->
            <div class="form-group">
              <label for="filter_date">Date</label>
              <input type="date" name="filter_date" id="filter_date" class="form-input" value="<?= htmlspecialchars($filterDate) ?>">
            </div>

            <!-- Action Buttons -->
            <div class="form-group form-actions">
              <button type="submit" class="action-btn primary">
                <i class="fas fa-search"></i> Apply
              </button>
              <a href="blockchain_records.php" class="action-btn secondary">
                <i class="fas fa-times"></i> Clear
              </a>
            </div>
          </div>
        </form>
      </div>

      <!-- ─────────────────── ACTIVE FILTERS STATUS ─────────────────── -->
      <?php if ($filterCourse > 0 || $filterAssignment > 0 || $filterDate !== ''): ?>
        <div class="filter-status-bar">
          <div class="status-info">
            <i class="fas fa-filter"></i>
            <span>Filtered results:</span>
          </div>
          <div class="status-filters">
            <?php if ($filterAssignment > 0 || $filterCourse > 0): ?>
              <span class="filter-tag">
                <?php
                  $selectedCourse = '';
                  foreach ($courseList as $c) {
                    if ($dbCapabilities['course_assignments']) {
                      if ($c['AssignmentID'] == $filterAssignment) {
                        $selectedCourse = $c['DisplayName'];
                        break;
                      }
                    } else {
                      if ($c['CourseID'] == $filterCourse) {
                        $selectedCourse = $c['DisplayName'];
                        break;
                      }
                    }
                  }
                  echo htmlspecialchars($selectedCourse);
                ?>
              </span>
            <?php endif; ?>

            <?php if ($filterDate !== ''): ?>
              <span class="filter-tag">
                <?= htmlspecialchars($filterDate) ?>
              </span>
            <?php endif; ?>
          </div>
          <a href="blockchain_records.php" class="clear-filters">
            <i class="fas fa-times"></i> Clear
          </a>
        </div>
      <?php endif; ?>

      <!-- ─────────────────── BLOCKCHAIN RECORDS TABLE ─────────────────── -->
      <div class="section-header">
        <h2 class="section-title"><i class="fas fa-link"></i> Blockchain Records</h2>
        <p class="section-subtitle">
          <?php if ($filterCourse > 0 || $filterAssignment > 0 || $filterDate !== ''): ?>
            Filtered blockchain attendance records.
          <?php else: ?>
            View attendance records that have been recorded on the blockchain.
          <?php endif; ?>
        </p>
      </div>

      <?php if ($result && $result->num_rows > 0): ?>
        <div class="courses-container">
          <table class="courses-table">
            <thead>
              <tr>
                <th>Student ID</th>
                <th>Student Name</th>
                <th>DB Timestamp</th>
                <th>On-chain Timestamp</th>
                <th>Transaction Hash</th>
                <th>Block Number</th>
              </tr>
            </thead>
            <tbody>
              <?php while ($row = $result->fetch_assoc()):
                $studentName    = htmlspecialchars($row['StudentName'] ?? 'Unknown');
                $attDate        = htmlspecialchars($row['Att_Date'] ?? '');
                $reportedAt     = htmlspecialchars($row['ReportedAt'] ?? '');
                $blockchainHash = trim($row['Blockchain_Hash'] ?? '');
                $onChainAtRaw   = trim($row['OnChainAt'] ?? '');

                // Format DB timestamp - use the exact Timestamp field from attendance_report
                $dbTimestamp = '';
                if ($reportedAt) {
                  // The ReportedAt field IS the Timestamp from attendance_report table
                  $dbTimestamp = $reportedAt; // Already in YYYY-MM-DD HH:MM:SS format
                } else {
                  $dbTimestamp = '—';
                }

                // Format on-chain timestamp
                $onChainAtDisp = '';
                $onChainAtRaw_clean = '';
                if ($onChainAtRaw !== '') {
                  $onChainAtDisp = date("Y-m-d H:i:s", strtotime($onChainAtRaw));
                  $onChainAtRaw_clean = $onChainAtDisp;
                } else {
                  $onChainAtDisp = '<em style="color: var(--text-secondary);">Not recorded</em>';
                  $onChainAtRaw_clean = 'Not recorded';
                }

                // Get student ID from the student table
                $studentIdQuery = $conn->prepare("SELECT UserID FROM student WHERE Name = ?");
                $studentIdQuery->bind_param("s", $studentName);
                $studentIdQuery->execute();
                $studentIdResult = $studentIdQuery->get_result();
                $studentData = $studentIdResult->fetch_assoc();
                $studentId = $studentData['UserID'] ?? 'N/A';
                $studentIdQuery->close();

                // Get block number if hash exists
                $blockNumber = '—';
                if ($blockchainHash !== '') {
                  $blockNumber = '<span class="loading-block" data-hash="' . htmlspecialchars($blockchainHash) . '">Loading...</span>';
                }

                // Calculate time difference between DB and blockchain
                $timeDiff = '';
                $timeDiffClass = '';
                if ($onChainAtRaw !== '' && $reportedAt !== '') {
                  $dbTime = strtotime($reportedAt);
                  $chainTime = strtotime($onChainAtRaw);
                  $diffSeconds = $chainTime - $dbTime;

                  if ($diffSeconds > 0) {
                    $timeDiffClass = 'delayed';
                    if ($diffSeconds < 60) {
                      $timeDiff = '+' . $diffSeconds . 's';
                    } elseif ($diffSeconds < 3600) {
                      $timeDiff = '+' . round($diffSeconds / 60) . 'm';
                    } else {
                      $timeDiff = '+' . round($diffSeconds / 3600, 1) . 'h';
                    }
                  } elseif ($diffSeconds < 0) {
                    $timeDiffClass = 'early';
                    $diffSeconds = abs($diffSeconds);
                    if ($diffSeconds < 60) {
                      $timeDiff = '-' . $diffSeconds . 's';
                    } elseif ($diffSeconds < 3600) {
                      $timeDiff = '-' . round($diffSeconds / 60) . 'm';
                    } else {
                      $timeDiff = '-' . round($diffSeconds / 3600, 1) . 'h';
                    }
                  } else {
                    $timeDiffClass = 'same';
                    $timeDiff = 'Same time';
                  }
                }
              ?>
              <tr class="clickable-row"
                  data-attendance-id="<?= $row['AttendanceID'] ?>"
                  data-tx-hash="<?= htmlspecialchars($blockchainHash) ?>"
                  data-student-name="<?= htmlspecialchars($studentName) ?>"
                  data-student-id="<?= htmlspecialchars($studentId) ?>"
                  data-course-name="<?= htmlspecialchars($row['CourseName']) ?>"
                  data-db-timestamp="<?= htmlspecialchars($dbTimestamp) ?>"
                  data-chain-timestamp="<?= htmlspecialchars($onChainAtRaw_clean) ?>">
                <td data-label="Student ID"><?= htmlspecialchars($studentId) ?></td>
                <td data-label="Student Name"><?= $studentName ?></td>
                <td data-label="DB Timestamp">
                  <div class="timestamp-display">
                    <span class="timestamp-value"><?= $dbTimestamp ?></span>
                    <small class="timestamp-label">Database Record</small>
                  </div>
                </td>
                <td data-label="On-chain Timestamp">
                  <div class="timestamp-display">
                    <span class="timestamp-value"><?= $onChainAtDisp ?></span>
                    <?php if ($timeDiff): ?>
                      <small class="timestamp-diff <?= $timeDiffClass ?>" title="Time difference from DB to blockchain">
                        <?= $timeDiff ?>
                      </small>
                    <?php else: ?>
                      <small class="timestamp-label">Blockchain Record</small>
                    <?php endif; ?>
                  </div>
                </td>
                <td data-label="Transaction Hash">
                  <?php if ($blockchainHash !== ''): ?>
                    <span class="hash-display" title="<?= htmlspecialchars($blockchainHash) ?>">
                      <?= htmlspecialchars(substr($blockchainHash, 0, 10)) ?>...<?= htmlspecialchars(substr($blockchainHash, -8)) ?>
                    </span>
                  <?php else: ?>
                    <em style="color: var(--text-secondary);">Not recorded</em>
                  <?php endif; ?>
                </td>
                <td data-label="Block Number"><?= $blockNumber ?></td>
              </tr>
              <?php endwhile; ?>
            </tbody>
          </table>
        </div>
      <?php else: ?>
        <div class="no-data-msg">
          <i class="fas fa-link"></i>
          <strong>No blockchain records found.</strong><br>
          <?php if ($filterCourse > 0): ?>
            No attendance has been recorded on the blockchain for this course yet.
          <?php else: ?>
            No attendance has been recorded on the blockchain for any of your courses yet.
          <?php endif; ?>
        </div>
      <?php endif; ?>
    </div> <!-- end .main-content -->
  </div>   <!-- end .container -->

  <!-- ─────────── BLOCKCHAIN DETAILS MODAL ─────────── -->
  <div id="blockchainModal" class="modal-overlay">
    <div class="modal-container">
      <div class="modal-header">
        <h3 class="modal-title">
          <i class="fas fa-link"></i> Blockchain Record Details
        </h3>
        <button class="modal-close">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-content">
        <div class="modal-loading" id="modalLoading">
          <i class="fas fa-spinner fa-spin"></i>
          <span>Loading blockchain data...</span>
        </div>

        <div class="modal-data" id="modalData" style="display: none;">
          <!-- Student Information -->
          <div class="info-section">
            <h4 class="section-title">
              <i class="fas fa-user-graduate"></i> Student Information
            </h4>
            <div class="info-grid">
              <div class="info-item">
                <label>Student ID:</label>
                <span id="modalStudentId">-</span>
              </div>
              <div class="info-item">
                <label>Student Name:</label>
                <span id="modalStudentName">-</span>
              </div>
              <div class="info-item">
                <label>Email:</label>
                <span id="modalStudentEmail">-</span>
              </div>
            </div>
          </div>

          <!-- Course Information -->
          <div class="info-section">
            <h4 class="section-title">
              <i class="fas fa-book"></i> Course Information
            </h4>
            <div class="info-grid">
              <div class="info-item">
                <label>Course Name:</label>
                <span id="modalCourseName">-</span>
              </div>
            </div>
          </div>

          <!-- Timestamp Information -->
          <div class="info-section">
            <h4 class="section-title">
              <i class="fas fa-clock"></i> Timestamp Information
            </h4>
            <div class="info-grid">
              <div class="info-item">
                <label>Database Timestamp:</label>
                <span id="modalDbTimestamp">-</span>
              </div>
              <div class="info-item">
                <label>Blockchain Timestamp:</label>
                <span id="modalChainTimestamp">-</span>
              </div>
              <div class="info-item">
                <label>Time Difference:</label>
                <span id="modalTimeDiff">-</span>
              </div>
            </div>
          </div>

          <!-- Blockchain Information -->
          <div class="info-section">
            <h4 class="section-title">
              <i class="fas fa-link"></i> Blockchain Information
            </h4>
            <div class="info-grid">
              <div class="info-item full-width">
                <label>Transaction Hash:</label>
                <span id="modalTxHash" class="hash-full">-</span>
              </div>
              <div class="info-item full-width">
                <label>Sender Address:</label>
                <span id="modalSenderAddress" class="hash-full">-</span>
              </div>
              <div class="info-item">
                <label>Block Number:</label>
                <span id="modalBlockNumber">-</span>
              </div>
              <div class="info-item">
                <label>Block Hash:</label>
                <span id="modalBlockHash" class="hash-display">-</span>
              </div>
              <div class="info-item">
                <label>Gas Used:</label>
                <span id="modalGasUsed">-</span>
              </div>
              <div class="info-item">
                <label>Transaction Fee:</label>
                <span id="modalTxFee">-</span>
              </div>
              <div class="info-item">
                <label>Confirmations:</label>
                <span id="modalConfirmations">-</span>
              </div>
              <div class="info-item">
                <label>Status:</label>
                <span id="modalTxStatus">-</span>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-error" id="modalError" style="display: none;">
          <i class="fas fa-exclamation-triangle"></i>
          <span>Error loading blockchain data</span>
        </div>
      </div>
    </div>
  </div>

  <!-- ─────────── FOOTER ─────────── -->
  <footer>
    <p>UNIVERSITI TUN HUSSEIN ONN MALAYSIA</p>
  </footer>

  <script>

    // Modal functionality
    function openBlockchainModal(attendanceId, txHash, studentName, studentId, courseName, dbTimestamp, chainTimestamp) {
      const modal = document.getElementById('blockchainModal');
      const modalLoading = document.getElementById('modalLoading');
      const modalData = document.getElementById('modalData');
      const modalError = document.getElementById('modalError');

      // Show modal and loading state
      modal.style.display = 'flex';
      modalLoading.style.display = 'flex';
      modalData.style.display = 'none';
      modalError.style.display = 'none';

      // Set basic data
      document.getElementById('modalStudentId').textContent = studentId;
      document.getElementById('modalStudentName').textContent = studentName;
      document.getElementById('modalCourseName').textContent = courseName;
      document.getElementById('modalDbTimestamp').textContent = dbTimestamp;
      document.getElementById('modalChainTimestamp').textContent = chainTimestamp;
      document.getElementById('modalTxHash').textContent = txHash || 'Not recorded';

      // Fetch additional student data and blockchain data
      fetchDetailedBlockchainData(attendanceId, txHash, studentId);
    }

    // Event delegation for table row clicks
    document.addEventListener('click', function(event) {
      const row = event.target.closest('.clickable-row');
      if (row) {
        const attendanceId = row.dataset.attendanceId;
        const txHash = row.dataset.txHash;
        const studentName = row.dataset.studentName;
        const studentId = row.dataset.studentId;
        const courseName = row.dataset.courseName;
        const dbTimestamp = row.dataset.dbTimestamp;
        const chainTimestamp = row.dataset.chainTimestamp;

        openBlockchainModal(attendanceId, txHash, studentName, studentId, courseName, dbTimestamp, chainTimestamp);
      }
    });

    function closeBlockchainModal() {
      document.getElementById('blockchainModal').style.display = 'none';
    }

    async function fetchDetailedBlockchainData(attendanceId, txHash, studentId) {
      try {
        // Fetch student email
        try {
          const studentResponse = await fetch(`../api/get_student_details.php?student_id=${encodeURIComponent(studentId)}`);
          if (studentResponse.ok) {
            const studentData = await studentResponse.json();
            document.getElementById('modalStudentEmail').textContent = studentData.email || 'Not available';
          } else {
            document.getElementById('modalStudentEmail').textContent = 'Error loading email';
          }
        } catch (emailErr) {
          console.warn('Error fetching student email:', emailErr);
          document.getElementById('modalStudentEmail').textContent = 'Error loading email';
        }

        if (txHash && txHash.trim() !== '') {
          console.log('🔍 Fetching blockchain data for transaction:', txHash);
          console.log('🔗 Web3 ready status:', web3Ready);
          console.log('🌐 Connection status:', connectionStatus);

          // Check if Web3 is ready
          if (!web3Ready || !web3) {
            console.warn('⚠️ Web3 not ready, attempting to reinitialize...');
            await initializeWeb3Connection();

            if (!web3Ready || !web3) {
              throw new Error(`Web3 connection not available. Status: ${connectionStatus}`);
            }
          }

          try {
            // Check Web3 connection first
            console.log('🔄 Checking network connection...');
            const networkId = await web3.eth.net.getId();
            console.log('🌐 Current network ID:', networkId);

            if (networkId.toString() !== "5777") {
              throw new Error(`Wrong network. Expected Ganache (5777), got ${networkId}`);
            }

            console.log('✅ Network verification successful');

            // Fetch transaction details with timeout
            console.log('🔄 Fetching transaction details...');
            const timeoutPromise = new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Request timeout after 15 seconds')), 15000)
            );

            const [transaction, receipt, currentBlock] = await Promise.race([
              Promise.all([
                web3.eth.getTransaction(txHash),
                web3.eth.getTransactionReceipt(txHash),
                web3.eth.getBlockNumber()
              ]),
              timeoutPromise
            ]);

            console.log('📊 Transaction data:', transaction);
            console.log('📋 Receipt data:', receipt);
            console.log('🔢 Current block:', currentBlock);

            if (transaction && receipt) {
              console.log('✅ Transaction and receipt data retrieved successfully');

              // Calculate transaction fee
              const gasPrice = BigInt(transaction.gasPrice || 0);
              const gasUsed = BigInt(receipt.gasUsed || 0);
              const txFee = gasPrice * gasUsed;
              const txFeeEth = web3.utils.fromWei(txFee.toString(), 'ether');

              // Calculate confirmations
              const confirmations = currentBlock - receipt.blockNumber;

              console.log('💰 Transaction fee calculated:', txFeeEth, 'ETH');
              console.log('✅ Confirmations:', confirmations);

              // Update modal with blockchain data
              document.getElementById('modalSenderAddress').textContent = transaction.from || 'Not available';
              document.getElementById('modalBlockNumber').textContent = receipt.blockNumber || 'Not available';
              document.getElementById('modalBlockHash').textContent = receipt.blockHash ?
                receipt.blockHash.substring(0, 10) + '...' + receipt.blockHash.substring(receipt.blockHash.length - 8) :
                'Not available';
              document.getElementById('modalGasUsed').textContent = receipt.gasUsed ? receipt.gasUsed.toLocaleString() : 'Not available';
              document.getElementById('modalTxFee').textContent = parseFloat(txFeeEth).toFixed(6) + ' ETH';
              document.getElementById('modalConfirmations').textContent = confirmations.toLocaleString();
              document.getElementById('modalTxStatus').textContent = receipt.status ? 'Success' : 'Failed';

              console.log('✅ Modal updated with blockchain data successfully');

              // Calculate time difference
              const dbTime = new Date(document.getElementById('modalDbTimestamp').textContent);
              const chainTime = new Date(document.getElementById('modalChainTimestamp').textContent);
              if (!isNaN(dbTime) && !isNaN(chainTime)) {
                const diffMs = chainTime - dbTime;
                const diffSeconds = Math.abs(diffMs / 1000);
                let timeDiffText = '';

                if (diffSeconds < 60) {
                  timeDiffText = `${Math.round(diffSeconds)}s`;
                } else if (diffSeconds < 3600) {
                  timeDiffText = `${Math.round(diffSeconds / 60)}m`;
                } else {
                  timeDiffText = `${(diffSeconds / 3600).toFixed(1)}h`;
                }

                if (diffMs > 0) {
                  timeDiffText = '+' + timeDiffText + ' (blockchain later)';
                } else if (diffMs < 0) {
                  timeDiffText = '-' + timeDiffText + ' (blockchain earlier)';
                } else {
                  timeDiffText = 'Same time';
                }

                document.getElementById('modalTimeDiff').textContent = timeDiffText;
              } else {
                document.getElementById('modalTimeDiff').textContent = 'Cannot calculate';
              }
            } else {
              console.error('❌ Transaction or receipt not found');
              throw new Error('Transaction or receipt not found on blockchain');
            }
          } catch (blockchainErr) {
            console.error('❌ Blockchain data fetch error:', blockchainErr);
            console.error('🔍 Error details:', {
              message: blockchainErr.message,
              txHash: txHash,
              web3Ready: web3Ready,
              connectionStatus: connectionStatus
            });

            // Determine error type and provide appropriate message
            let errorMessage = blockchainErr.message;
            if (blockchainErr.message.includes('timeout')) {
              errorMessage = 'Connection timeout - Ganache may be offline';
            } else if (blockchainErr.message.includes('Wrong network')) {
              errorMessage = 'Wrong network - Please connect to Ganache (5777)';
            } else if (blockchainErr.message.includes('not found')) {
              errorMessage = 'Transaction not found on blockchain';
            }

            // Set error messages for blockchain fields
            document.getElementById('modalSenderAddress').textContent = `Error: ${errorMessage}`;
            document.getElementById('modalBlockNumber').textContent = 'Error loading';
            document.getElementById('modalBlockHash').textContent = 'Error loading';
            document.getElementById('modalGasUsed').textContent = 'Error loading';
            document.getElementById('modalTxFee').textContent = 'Error loading';
            document.getElementById('modalConfirmations').textContent = 'Error loading';
            document.getElementById('modalTxStatus').textContent = 'Error loading';
            document.getElementById('modalTimeDiff').textContent = 'Error loading';
          }
        } else {
          // No blockchain data available
          const reason = !txHash || txHash.trim() === '' ? 'No transaction hash in database' : `Web3 not available (${connectionStatus})`;
          console.warn('⚠️ No blockchain data available:', reason);
          console.log('🔍 Debug info:', {
            txHash: txHash,
            web3Ready: web3Ready,
            connectionStatus: connectionStatus,
            web3Available: !!web3
          });

          document.getElementById('modalSenderAddress').textContent = 'Not recorded';
          document.getElementById('modalBlockNumber').textContent = 'Not recorded';
          document.getElementById('modalBlockHash').textContent = 'Not recorded';
          document.getElementById('modalGasUsed').textContent = 'Not recorded';
          document.getElementById('modalTxFee').textContent = 'Not recorded';
          document.getElementById('modalConfirmations').textContent = 'Not recorded';
          document.getElementById('modalTxStatus').textContent = 'Not recorded';
          document.getElementById('modalTimeDiff').textContent = 'Not recorded';
        }

        // Hide loading and show data
        document.getElementById('modalLoading').style.display = 'none';
        document.getElementById('modalData').style.display = 'block';

      } catch (error) {
        console.error('Error fetching detailed blockchain data:', error);
        document.getElementById('modalLoading').style.display = 'none';
        document.getElementById('modalError').style.display = 'flex';

        // Update error message with more details
        const errorElement = document.querySelector('#modalError span');
        if (errorElement) {
          errorElement.textContent = `Error loading blockchain data: ${error.message}`;
        }
      }
    }

    // Load block numbers for blockchain hashes
    document.addEventListener('DOMContentLoaded', async function() {
      // Wait a bit for Web3 to initialize
      setTimeout(async () => {
        const loadingBlocks = document.querySelectorAll('.loading-block');
        console.log('🔄 Loading block numbers for', loadingBlocks.length, 'transactions');

        for (const element of loadingBlocks) {
          const hash = element.getAttribute('data-hash');
          if (hash) {
            try {
              // Ensure Web3 is ready
              if (!web3Ready || !web3) {
                await initializeWeb3Connection();
              }

              if (web3Ready && web3) {
                const receipt = await web3.eth.getTransactionReceipt(hash);
                if (receipt && receipt.blockNumber) {
                  element.textContent = receipt.blockNumber;
                  element.classList.remove('loading-block');
                  console.log('✅ Block number loaded for', hash.substring(0, 10) + '...:', receipt.blockNumber);
                } else {
                  element.textContent = 'Pending';
                  element.style.color = 'var(--text-secondary)';
                  console.log('⏳ Transaction pending for', hash.substring(0, 10) + '...');
                }
              } else {
                element.textContent = 'No connection';
                element.style.color = 'var(--danger)';
                console.warn('⚠️ Web3 not available for', hash.substring(0, 10) + '...');
              }
            } catch (error) {
              element.textContent = 'Error';
              element.style.color = 'var(--danger)';
              console.error('❌ Error loading block for', hash.substring(0, 10) + '...:', error.message);
            }
          }
        }
      }, 2000); // Wait 2 seconds for Web3 initialization
    });

    // Close modal when clicking outside or on close button
    document.addEventListener('click', function(event) {
      const modal = document.getElementById('blockchainModal');
      const closeBtn = event.target.closest('.modal-close');

      if (event.target === modal || closeBtn) {
        closeBlockchainModal();
      }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', function(event) {
      if (event.key === 'Escape') {
        closeBlockchainModal();
      }
    });
  </script>
</body>
</html>