-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 16, 2025 at 11:49 AM
-- Server version: 10.4.28-MariaDB
-- PHP Version: 8.2.4

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `uthm_attendance`
--

-- --------------------------------------------------------

--
-- Table structure for table `admin`
--

CREATE TABLE `admin` (
  `AdminID` int(11) NOT NULL,
  `Username` varchar(50) NOT NULL,
  `Email` varchar(100) NOT NULL,
  `Password` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admin`
--

INSERT INTO `admin` (`AdminID`, `Username`, `Email`, `Password`) VALUES
(1, 'admin1', '<EMAIL>', '$2y$10$r0SFliBYIdsNlf0BupT.eOuX8JGrdb3zKMACef8NN3liDisD02oe6');

-- --------------------------------------------------------

--
-- Table structure for table `attendance_report`
--

CREATE TABLE `attendance_report` (
  `AttendanceID` int(11) NOT NULL,
  `StudentID` int(11) NOT NULL,
  `CourseID` int(11) NOT NULL,
  `LectID` int(11) NOT NULL,
  `Att_Date` date NOT NULL,
  `Att_Status` enum('Present','Absent') NOT NULL,
  `Code` varchar(4) NOT NULL,
  `Timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `Remark` varchar(255) DEFAULT NULL,
  `AbsentLetter` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `attendance_report`
--

INSERT INTO `attendance_report` (`AttendanceID`, `StudentID`, `CourseID`, `LectID`, `Att_Date`, `Att_Status`, `Code`, `Timestamp`, `Remark`, `AbsentLetter`) VALUES
(108, 45, 129, 15, '2025-06-16', 'Present', '', '2025-06-16 07:50:18', 'Assignment ID: 8 (Section 1)', NULL),
(109, 42, 129, 15, '2025-06-16', 'Absent', '', '2025-06-16 07:52:26', 'Assignment ID: 8 - Absent record created for warning letter; Warning letter sent on 2025-06-16 15:52:43; Warning letter sent on 2025-06-16 16:48:49', NULL),
(110, 47, 132, 16, '2025-06-16', 'Present', '', '2025-06-16 08:43:56', 'Assignment ID: 15 (Section 1)', NULL),
(111, 42, 132, 16, '2025-06-16', 'Absent', '', '2025-06-16 08:46:03', 'Assignment ID: 15 - Absent record created for warning letter', 'absent_111_1750063588.pdf'),
(112, 45, 132, 16, '2025-06-16', 'Present', '', '2025-06-16 08:56:45', 'Assignment ID: 15', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `blockchain_record`
--

CREATE TABLE `blockchain_record` (
  `RecordID` int(11) NOT NULL,
  `AttendanceID` int(11) NOT NULL,
  `Timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `Blockchain_Hash` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `blockchain_record`
--

INSERT INTO `blockchain_record` (`RecordID`, `AttendanceID`, `Timestamp`, `Blockchain_Hash`) VALUES
(26, 108, '2025-06-16 07:50:32', '0x76cbd8e0d252e748d3b685a76af87659da86d18ed3e9d5cc1e85dc8a3d6ffa9c'),
(27, 110, '2025-06-16 08:44:14', '0x094f0571095d17b87058104e5d1bd4112fe90985804af1644a6a29aec090b6ba');

-- --------------------------------------------------------

--
-- Table structure for table `course`
--

CREATE TABLE `course` (
  `CourseID` int(11) NOT NULL,
  `Course_Code` varchar(20) DEFAULT NULL,
  `Course_Name` varchar(100) NOT NULL,
  `Status` enum('Active','Inactive') DEFAULT 'Active',
  `LectID` int(11) DEFAULT NULL,
  `StudentID` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `course`
--

INSERT INTO `course` (`CourseID`, `Course_Code`, `Course_Name`, `Status`, `LectID`, `StudentID`) VALUES
(127, 'BIC21203', 'Web Development', 'Active', NULL, NULL),
(128, 'BIS20303', 'Web Security', 'Active', NULL, NULL),
(129, 'BIT34503', 'Data Science', 'Active', NULL, NULL),
(130, 'BIS30903', 'Network Security', 'Active', NULL, NULL),
(131, 'BIC21404', 'Database', 'Active', NULL, NULL),
(132, 'BIS20503', 'Software Security', 'Active', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `courses_enhanced`
--

CREATE TABLE `courses_enhanced` (
  `id` int(11) NOT NULL,
  `course_name` varchar(255) NOT NULL,
  `course_code` varchar(20) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `courses_enhanced`
--

INSERT INTO `courses_enhanced` (`id`, `course_name`, `course_code`, `created_at`, `updated_at`) VALUES
(1, 'Cryptography', 'BIS30015', '2025-06-12 06:23:52', '2025-06-12 06:23:52');

-- --------------------------------------------------------

--
-- Table structure for table `course_assignments`
--

CREATE TABLE `course_assignments` (
  `AssignmentID` int(11) NOT NULL,
  `CourseID` int(11) NOT NULL,
  `LectID` int(11) NOT NULL,
  `Section` enum('Section 1','Section 2') NOT NULL,
  `Status` enum('Active','Inactive') DEFAULT 'Active',
  `CreatedDate` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `course_assignments`
--

INSERT INTO `course_assignments` (`AssignmentID`, `CourseID`, `LectID`, `Section`, `Status`, `CreatedDate`) VALUES
(5, 127, 13, 'Section 1', 'Active', '2025-06-14 16:06:02'),
(7, 128, 13, 'Section 1', 'Active', '2025-06-14 18:23:40'),
(8, 129, 15, 'Section 1', 'Active', '2025-06-15 20:00:27'),
(9, 127, 13, 'Section 2', 'Active', '2025-06-16 03:26:53'),
(14, 131, 13, 'Section 1', 'Active', '2025-06-16 06:17:06'),
(15, 132, 16, 'Section 1', 'Active', '2025-06-16 08:30:33');

-- --------------------------------------------------------

--
-- Table structure for table `course_registration`
--

CREATE TABLE `course_registration` (
  `RegistrationID` int(11) NOT NULL,
  `StudentID` int(11) NOT NULL,
  `CourseID` int(11) NOT NULL,
  `Section` enum('Section 1','Section 2') DEFAULT NULL,
  `RegistrationDate` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `course_registration`
--

INSERT INTO `course_registration` (`RegistrationID`, `StudentID`, `CourseID`, `Section`, `RegistrationDate`) VALUES
(49, 42, 129, 'Section 1', '2025-06-16 07:37:04'),
(50, 41, 129, 'Section 1', '2025-06-16 07:37:23'),
(51, 43, 129, 'Section 1', '2025-06-16 07:44:48'),
(52, 45, 129, 'Section 1', '2025-06-16 07:47:43'),
(54, 47, 132, 'Section 1', '2025-06-16 08:33:16'),
(55, 47, 129, 'Section 1', '2025-06-16 08:33:22'),
(56, 41, 132, 'Section 1', '2025-06-16 08:36:25'),
(57, 42, 132, 'Section 1', '2025-06-16 08:36:48'),
(58, 43, 132, 'Section 1', '2025-06-16 08:39:17'),
(59, 45, 132, 'Section 1', '2025-06-16 08:39:40');

-- --------------------------------------------------------

--
-- Table structure for table `course_sections`
--

CREATE TABLE `course_sections` (
  `id` int(11) NOT NULL,
  `course_id` int(11) NOT NULL,
  `section_number` int(11) NOT NULL,
  `lecturer_id` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `course_sections`
--

INSERT INTO `course_sections` (`id`, `course_id`, `section_number`, `lecturer_id`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 1, 1, NULL, 1, '2025-06-12 06:23:52', '2025-06-12 06:23:52');

-- --------------------------------------------------------

--
-- Table structure for table `lecturer`
--

CREATE TABLE `lecturer` (
  `LectID` int(11) NOT NULL,
  `Name` varchar(100) NOT NULL,
  `Email` varchar(100) NOT NULL,
  `Password` varchar(255) NOT NULL,
  `UserID` varchar(20) DEFAULT NULL,
  `Faculty` varchar(100) DEFAULT NULL,
  `PhoneNumber` varchar(15) DEFAULT NULL,
  `ID_Type` enum('IC','Passport') NOT NULL DEFAULT 'IC',
  `ID_Number` varchar(50) NOT NULL DEFAULT '',
  `Gender` enum('Male','Female') NOT NULL DEFAULT 'Male',
  `Address` text DEFAULT NULL,
  `profile_pic` varchar(255) DEFAULT NULL,
  `is_first_login` tinyint(1) DEFAULT 1,
  `FirstLogin` tinyint(1) DEFAULT 1,
  `secret_2fa` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `lecturer`
--

INSERT INTO `lecturer` (`LectID`, `Name`, `Email`, `Password`, `UserID`, `Faculty`, `PhoneNumber`, `ID_Type`, `ID_Number`, `Gender`, `Address`, `profile_pic`, `is_first_login`, `FirstLogin`, `secret_2fa`) VALUES
(13, 'AHMAD AIMAN', '<EMAIL>', '$2y$10$KIaFD.pbhEZY8lbE1lXFl.N5YA/s4lLCwr1u1dWHD1DW2x9xtLAmK', '00001', 'Fakulti Sains Komputer dan Teknologi Maklumat', '011-56210402', 'IC', '031111-02-1111', 'Male', NULL, NULL, 1, 0, NULL),
(15, 'AHMAD FARISH', '<EMAIL>', '$2y$10$1tGAFDTfjRLGIx55orQOB.pOCv4biqUdnzT5tB/uMrrof2EUOL2yC', '00002', 'Fakulti Sains Komputer dan Teknologi Maklumat', '011-56210402', 'IC', '031111-03-1111', 'Male', NULL, NULL, 1, 0, NULL),
(16, 'HAZIQ AIDIL', '<EMAIL>', '$2y$10$0AzpDZtZLN35y4Wtv8yph.Tbr7sOzpZSV2XpI54KD9TEvOK3p.wcS', '00003', 'Fakulti Sains Komputer dan Teknologi Maklumat', '011-1122345', 'IC', '031111-06-1111', 'Male', NULL, NULL, 1, 0, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `id` int(11) NOT NULL,
  `user_id` varchar(50) NOT NULL,
  `user_role` varchar(20) NOT NULL,
  `token` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `expires_at` datetime NOT NULL,
  `used` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `used_at` timestamp NULL DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `password_reset_tokens`
--

INSERT INTO `password_reset_tokens` (`id`, `user_id`, `user_role`, `token`, `email`, `expires_at`, `used`, `created_at`, `used_at`, `ip_address`, `user_agent`) VALUES
(6, '1', 'student', '81bec24317ac30162e5174b07f2dc5cce9e99c6edfc0230c68fc0d0ab784a670', '<EMAIL>', '2025-06-10 22:45:54', 0, '2025-06-10 19:45:54', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(7, '1', 'student', '3f0543753c05c1a4c2eb7c72487a5ea80472a61381fd336b13f573c8486d6bcb', '<EMAIL>', '2025-06-10 22:46:27', 0, '2025-06-10 19:46:27', NULL, NULL, NULL),
(9, '38', 'student', '160a2ad01744ec72afa995154909913aa4c8af458954bbf7b13385306c62cb3e', '<EMAIL>', '2025-06-11 04:59:57', 1, '2025-06-10 19:59:57', '2025-06-10 20:00:59', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(10, '39', 'student', 'e5370ad5d4e87a84d7b2c0125f3afe89e8202059ccbf370d17eafc306f177910', '<EMAIL>', '2025-06-11 05:16:10', 1, '2025-06-10 20:16:10', '2025-06-10 20:18:19', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(11, '12', 'lecturer', 'a8b62ccc960b734860eae36183f70ba088c9684acaa491a0a843b83f8a181786', '<EMAIL>', '2025-06-11 22:42:59', 1, '2025-06-11 13:42:59', '2025-06-11 13:44:00', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(12, '40', 'student', '057c971fb545c40e5e3bcf23374843f41eaf2ccad0321adb10086655b630779f', '<EMAIL>', '2025-06-13 08:54:09', 1, '2025-06-12 23:54:09', '2025-06-12 23:54:52', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(13, '13', 'lecturer', 'cc9a93dcea0ef3780c5f8e6abeba921a02fd0befc2b2ac94aa6e354c1ab8c65d', '<EMAIL>', '2025-06-14 21:44:40', 1, '2025-06-14 12:44:40', '2025-06-14 12:46:12', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(16, '15', 'lecturer', '67301b6d7d6de910dd33a24c42b368cd1cbf991510f8057e2eb6615ca3b3d3db', '<EMAIL>', '2025-06-15 01:19:37', 1, '2025-06-14 16:19:37', '2025-06-14 16:20:09', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(17, '41', 'student', 'ee89b9cd18e9ef30765c53647fbaac3cb4ac817ad13c68889149428dad2ac95b', '<EMAIL>', '2025-06-16 16:29:17', 1, '2025-06-16 07:29:17', '2025-06-16 07:30:25', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(18, '42', 'student', 'df8ff0782d94a08a921d0c21c9152e4e5889528d5ea6248b60d675c918a03311', '<EMAIL>', '2025-06-16 16:35:52', 1, '2025-06-16 07:35:52', '2025-06-16 07:36:22', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(19, '43', 'student', '5fc1d8dbebad9807b9085c4e7b8e235a739b5cda5c816622d682e5593d587689', '<EMAIL>', '2025-06-16 16:39:36', 1, '2025-06-16 07:39:36', '2025-06-16 07:40:10', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(20, '45', 'student', 'b8c8cd951d9e59cd701403385a8f552d9bb30a9e0c2bf959e59265ac94883ccd', '<EMAIL>', '2025-06-16 16:45:21', 1, '2025-06-16 07:45:21', '2025-06-16 07:47:15', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(22, '46', 'student', 'cb0162a4903310ef5e439dc3c5893c45ac80091bc46ec8ae2542fdfd7d80d41b', '<EMAIL>', '2025-06-16 16:59:25', 1, '2025-06-16 07:59:25', '2025-06-16 07:59:58', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(23, '47', 'student', '7ea282bc76dbec38c94a69f87c9787f0fe2947c0d77eba05d30dbf1098d9b747', '<EMAIL>', '2025-06-16 17:10:21', 1, '2025-06-16 08:10:21', '2025-06-16 08:12:06', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(25, '16', 'lecturer', 'b6e6db8ba834d6671c8aae44d2c4f593852a1fd4c9d1624e8ba486f8b8f1b665', '<EMAIL>', '2025-06-16 17:18:25', 1, '2025-06-16 08:18:25', '2025-06-16 08:19:06', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');

-- --------------------------------------------------------

--
-- Table structure for table `qr_tokens`
--

CREATE TABLE `qr_tokens` (
  `id` int(11) NOT NULL,
  `token` varchar(255) NOT NULL,
  `course_id` int(11) NOT NULL,
  `lecturer_id` int(11) NOT NULL,
  `assignment_id` int(11) DEFAULT NULL,
  `section` varchar(50) DEFAULT NULL,
  `expires_at` datetime NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `used` tinyint(1) DEFAULT 0,
  `used_at` timestamp NULL DEFAULT NULL,
  `session_id` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `qr_tokens`
--

INSERT INTO `qr_tokens` (`id`, `token`, `course_id`, `lecturer_id`, `assignment_id`, `section`, `expires_at`, `created_at`, `used`, `used_at`, `session_id`) VALUES
(7, 'ce2b859e97b3fba43867e78515bceabc', 127, 13, 5, '0', '2025-06-16 14:15:01', '2025-06-16 06:14:41', 1, '2025-06-16 06:15:01', NULL),
(8, '883e542f1d0cd8884f252565f49805e6', 127, 13, 5, '0', '2025-06-16 14:15:21', '2025-06-16 06:15:01', 1, '2025-06-16 06:15:21', '0qo2810cb45n00hi6n93676ftm'),
(9, '6be02a499d305bc14cdfb10611ae0881', 127, 13, 5, '0', '2025-06-16 14:15:41', '2025-06-16 06:15:21', 0, NULL, '0qo2810cb45n00hi6n93676ftm'),
(10, '66fe1859ed39711589b9a6e0f4588805', 127, 13, 5, '0', '2025-06-16 14:15:50', '2025-06-16 06:15:30', 0, NULL, NULL),
(11, '444e004eeaa70f54506be492968dd957', 131, 13, 14, '0', '2025-06-16 14:18:20', '2025-06-16 06:18:00', 1, '2025-06-16 06:18:20', NULL),
(12, 'a06d209d1d1aecc337e5354cb804d20b', 131, 13, 14, '0', '2025-06-16 14:18:40', '2025-06-16 06:18:20', 1, '2025-06-16 06:18:40', 'kcu52ic8dajf57c6r81ua787hb'),
(13, '9719fa0b56f1511240697f4388ed2a79', 131, 13, 14, '0', '2025-06-16 14:19:00', '2025-06-16 06:18:40', 1, '2025-06-16 06:31:10', 'kcu52ic8dajf57c6r81ua787hb'),
(14, '30a02d06a7059d9459e8cc04d529beeb', 131, 13, 14, '0', '2025-06-16 14:19:02', '2025-06-16 06:18:42', 1, '2025-06-16 06:31:10', NULL),
(19, '9dfd1efe3cb40842986feca431b60e10', 131, 13, 14, '0', '2025-06-16 14:31:10', '2025-06-16 06:30:50', 1, '2025-06-16 06:31:10', NULL),
(20, '5b2ef62a0462fa3bf30af8d1f42faa91', 131, 13, 14, '0', '2025-06-16 14:31:30', '2025-06-16 06:31:10', 1, '2025-06-16 06:31:30', 'kcu52ic8dajf57c6r81ua787hb'),
(21, '37c718ec0407c8a1636c780efa6b9e06', 131, 13, 14, '0', '2025-06-16 14:31:50', '2025-06-16 06:31:30', 1, '2025-06-16 06:33:22', 'kcu52ic8dajf57c6r81ua787hb'),
(26, 'a9eb54cc0b52a1814900f8c4d3e42926', 131, 13, 14, '0', '2025-06-16 14:33:42', '2025-06-16 06:33:22', 1, '2025-06-16 06:44:40', 'kcu52ic8dajf57c6r81ua787hb'),
(29, 'a07e8ae983212a3b5796dbef2a6973b4', 127, 13, NULL, '0', '2025-06-16 14:45:40', '2025-06-16 06:40:40', 0, NULL, '5mi5nkk4b1uo7d46b9b1u3beef'),
(30, 'ecff48f5f004671e9ee39c180b0b4632', 131, 13, 14, '0', '2025-06-16 14:44:40', '2025-06-16 06:44:20', 1, '2025-06-16 06:44:40', NULL),
(31, '4351f2491ca3b3c304e41523486fbfd7', 131, 13, 14, '0', '2025-06-16 14:45:00', '2025-06-16 06:44:40', 1, '2025-06-16 06:45:42', 'kcu52ic8dajf57c6r81ua787hb'),
(32, 'a4d3c70d3481bf0eff430e7f16a0b69c', 127, 13, 5, '0', '2025-06-16 14:45:24', '2025-06-16 06:45:04', 0, NULL, NULL),
(33, '6644421111a2013ab3a7d19a30b5dd75', 127, 13, 5, '0', '2025-06-16 14:45:37', '2025-06-16 06:45:17', 0, NULL, NULL),
(34, 'c0aa1a68c2db53b9bd2bbf298e2aa266', 131, 13, 14, '0', '2025-06-16 14:45:42', '2025-06-16 06:45:22', 1, '2025-06-16 06:45:42', NULL),
(35, '72fb7d6c82c2e1f65f18b94128fa95f7', 131, 13, 14, '0', '2025-06-16 14:46:02', '2025-06-16 06:45:42', 1, '2025-06-16 06:46:02', 'kcu52ic8dajf57c6r81ua787hb'),
(36, '60519414ec72634a0160aa3d17f3a516', 131, 13, 14, '0', '2025-06-16 14:46:22', '2025-06-16 06:46:02', 1, '2025-06-16 06:46:22', 'kcu52ic8dajf57c6r81ua787hb'),
(37, '8f8b5bb46e826f0857866d385fc0dcbc', 131, 13, 14, '0', '2025-06-16 14:46:42', '2025-06-16 06:46:22', 1, '2025-06-16 06:55:07', 'kcu52ic8dajf57c6r81ua787hb'),
(38, 'a979a275effad5cd77527e65bc26b48a', 131, 13, 14, '0', '2025-06-16 14:55:07', '2025-06-16 06:54:47', 1, '2025-06-16 06:55:07', NULL),
(39, '85bdd04e2026f87cb7edf75196ee7c47', 131, 13, 14, '0', '2025-06-16 14:55:27', '2025-06-16 06:55:07', 1, '2025-06-16 06:55:27', 'kcu52ic8dajf57c6r81ua787hb'),
(40, 'abfd8518a3fb92fbe7666e81c5672b62', 131, 13, 14, '0', '2025-06-16 14:55:47', '2025-06-16 06:55:27', 1, '2025-06-16 06:55:47', 'kcu52ic8dajf57c6r81ua787hb'),
(41, '6ccfcb6552fe0524a2f4ce864de681d3', 131, 13, 14, '0', '2025-06-16 14:56:07', '2025-06-16 06:55:47', 1, '2025-06-16 06:56:07', 'kcu52ic8dajf57c6r81ua787hb'),
(42, '7cc1f149f8c6820692c3140f66386fea', 131, 13, 14, '0', '2025-06-16 14:56:27', '2025-06-16 06:56:07', 1, '2025-06-16 07:03:26', 'kcu52ic8dajf57c6r81ua787hb'),
(43, 'fa52dd83a1230c72a9f2146c7e16c04f', 131, 13, 14, '0', '2025-06-16 15:03:26', '2025-06-16 07:03:06', 1, '2025-06-16 07:03:26', NULL),
(44, 'cca9ed9fdba66015f6d835b2e6c71e75', 131, 13, 14, '0', '2025-06-16 15:03:46', '2025-06-16 07:03:26', 1, '2025-06-16 07:03:46', 'kcu52ic8dajf57c6r81ua787hb'),
(45, 'e8a7c58b0fa0d014033766973d4eb663', 131, 13, 14, '0', '2025-06-16 15:04:06', '2025-06-16 07:03:46', 1, '2025-06-16 07:04:06', 'kcu52ic8dajf57c6r81ua787hb'),
(46, 'cd84112e9c9882b275c4c4ec1434486b', 131, 13, 14, '0', '2025-06-16 15:04:26', '2025-06-16 07:04:06', 1, '2025-06-16 07:04:26', 'kcu52ic8dajf57c6r81ua787hb'),
(47, 'b8a9eb63e392365b4faab325d8959a1b', 131, 13, 14, '0', '2025-06-16 15:04:46', '2025-06-16 07:04:26', 1, '2025-06-16 07:13:16', 'kcu52ic8dajf57c6r81ua787hb'),
(48, '51fd2aa5398c0646fb1f2c576b84150f', 131, 13, 14, '0', '2025-06-16 15:13:16', '2025-06-16 07:12:56', 1, '2025-06-16 07:13:16', NULL),
(49, '90bef970b7de32a4c3600d175c1139e5', 131, 13, 14, '0', '2025-06-16 15:13:36', '2025-06-16 07:13:16', 1, '2025-06-16 07:13:36', 'kcu52ic8dajf57c6r81ua787hb'),
(50, '7a310275194c9b36a863ef08fa15bc9f', 131, 13, 14, '0', '2025-06-16 15:13:56', '2025-06-16 07:13:36', 1, '2025-06-16 07:13:56', 'kcu52ic8dajf57c6r81ua787hb'),
(51, '506988f7313722ed2b7dc6ad517a796c', 131, 13, 14, '0', '2025-06-16 15:14:16', '2025-06-16 07:13:56', 1, '2025-06-16 07:14:16', 'kcu52ic8dajf57c6r81ua787hb'),
(52, '8466e3e8f0cb233fdfc1ee5b27d82eeb', 131, 13, 14, '0', '2025-06-16 15:14:36', '2025-06-16 07:14:16', 0, NULL, 'kcu52ic8dajf57c6r81ua787hb'),
(53, '90d3c4954e42f9cff174bf89d8a6af75', 131, 13, 14, '0', '2025-06-16 15:14:39', '2025-06-16 07:14:19', 0, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `section`
--

CREATE TABLE `section` (
  `SectionID` int(11) NOT NULL,
  `CourseID` int(11) DEFAULT NULL,
  `Day` varchar(20) DEFAULT NULL,
  `Time` time DEFAULT NULL,
  `Class_Type` enum('Lecture','Lab') DEFAULT NULL,
  `Max_Size` int(11) DEFAULT 30
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `student`
--

CREATE TABLE `student` (
  `StudentID` int(11) NOT NULL,
  `Name` varchar(100) NOT NULL,
  `Email` varchar(100) NOT NULL,
  `Password` varchar(255) NOT NULL,
  `UserID` varchar(20) DEFAULT NULL,
  `Faculty` varchar(100) DEFAULT NULL,
  `PhoneNumber` varchar(15) DEFAULT NULL,
  `ID_Type` enum('IC','Passport') NOT NULL DEFAULT 'IC',
  `ID_Number` varchar(50) NOT NULL DEFAULT '',
  `Gender` enum('Male','Female') NOT NULL DEFAULT 'Male',
  `Address` text DEFAULT NULL,
  `Photo` varchar(255) DEFAULT NULL,
  `is_first_login` tinyint(1) DEFAULT 1,
  `FirstLogin` tinyint(1) DEFAULT 1,
  `secret_2fa` varchar(255) DEFAULT NULL,
  `EthAddress` varchar(42) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `student`
--

INSERT INTO `student` (`StudentID`, `Name`, `Email`, `Password`, `UserID`, `Faculty`, `PhoneNumber`, `ID_Type`, `ID_Number`, `Gender`, `Address`, `Photo`, `is_first_login`, `FirstLogin`, `secret_2fa`, `EthAddress`) VALUES
(41, 'AMIRUL BIN AIMAN', '<EMAIL>', '$2y$10$0hIDG4AXhsvz9M2dO2EU/en1I2idMRLsWm9vwSSwb6qU693nZj2W2', 'AI250001', 'Fakulti Sains Komputer dan Teknologi Maklumat', '011-55049931', 'IC', '031111-01-1111', 'Male', 'Batu Pahat', 'stu_41_1750059080.png', 1, 0, NULL, NULL),
(42, 'AHMAD BIN ADLY', '<EMAIL>', '$2y$10$3CXOJdESr962q2hby7Ugges984F9ViYMcyZY5UlGZPEtxSizON1cG', 'AI250002', 'Fakulti Sains Komputer dan Teknologi Maklumat', '011-56210402', 'IC', '031111-02-1111', 'Male', NULL, NULL, 1, 0, NULL, NULL),
(43, 'MUSTAQIM BIN YUSUF', '<EMAIL>', '$2y$10$7z0b7RqOwE.b/hPk6KyFtOeG6c9xHKjIa2ZgUDguuJupB3Wa05yAO', 'AI250003', 'Fakulti Sains Komputer dan Teknologi Maklumat', '012-3456789', 'IC', '031111-03-1111', 'Male', NULL, NULL, 1, 0, NULL, NULL),
(45, 'AMIN BIN HANIF', '<EMAIL>', '$2y$10$6Cg3huKa0zgRIwVmVQpeYe6iZphrRkj7wGtjOeErSmw/mfbjtegbq', 'AI250004', 'Fakulti Sains Komputer dan Teknologi Maklumat', '011-54209163', 'IC', '031111-04-1111', 'Male', NULL, NULL, 1, 0, NULL, NULL),
(47, 'DANISH BIN MD PUAD', '<EMAIL>', '$2y$10$FiHPh5QqBbCsSw.xnauTtOyyXlzkkV7uYjJu0Vc/eBo18O2zDnPs6', 'AI250005', 'Fakulti Sains Komputer dan Teknologi Maklumat', '011-55049931', 'IC', '030916-01-1349', 'Male', 'Batu Pahat', 'stu_47_1750061585.png', 1, 0, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `system_settings`
--

CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text NOT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `system_settings`
--

INSERT INTO `system_settings` (`id`, `setting_key`, `setting_value`, `updated_at`) VALUES
(1, 'student_registration_enabled', '1', '2025-06-12 06:23:36'),
(2, 'course_registration_enabled', '1', '2025-06-16 08:33:57');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin`
--
ALTER TABLE `admin`
  ADD PRIMARY KEY (`AdminID`),
  ADD UNIQUE KEY `Username` (`Username`),
  ADD UNIQUE KEY `Email` (`Email`);

--
-- Indexes for table `attendance_report`
--
ALTER TABLE `attendance_report`
  ADD PRIMARY KEY (`AttendanceID`),
  ADD KEY `StudentID` (`StudentID`),
  ADD KEY `LectID` (`LectID`),
  ADD KEY `attendance_report_ibfk_2` (`CourseID`);

--
-- Indexes for table `blockchain_record`
--
ALTER TABLE `blockchain_record`
  ADD PRIMARY KEY (`RecordID`),
  ADD KEY `AttendanceID` (`AttendanceID`);

--
-- Indexes for table `course`
--
ALTER TABLE `course`
  ADD PRIMARY KEY (`CourseID`),
  ADD UNIQUE KEY `Course_Code` (`Course_Code`),
  ADD KEY `course_lecturer_fk` (`LectID`),
  ADD KEY `course_student_fk` (`StudentID`);

--
-- Indexes for table `courses_enhanced`
--
ALTER TABLE `courses_enhanced`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_course_code` (`course_code`);

--
-- Indexes for table `course_assignments`
--
ALTER TABLE `course_assignments`
  ADD PRIMARY KEY (`AssignmentID`),
  ADD UNIQUE KEY `unique_assignment` (`CourseID`,`LectID`,`Section`),
  ADD KEY `LectID` (`LectID`);

--
-- Indexes for table `course_registration`
--
ALTER TABLE `course_registration`
  ADD PRIMARY KEY (`RegistrationID`),
  ADD UNIQUE KEY `StudentCourse` (`StudentID`,`CourseID`),
  ADD KEY `CourseID` (`CourseID`);

--
-- Indexes for table `course_sections`
--
ALTER TABLE `course_sections`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_course_section` (`course_id`,`section_number`),
  ADD KEY `lecturer_id` (`lecturer_id`);

--
-- Indexes for table `lecturer`
--
ALTER TABLE `lecturer`
  ADD PRIMARY KEY (`LectID`),
  ADD UNIQUE KEY `Email` (`Email`),
  ADD UNIQUE KEY `Username` (`UserID`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_token` (`token`),
  ADD KEY `idx_expires` (`expires_at`),
  ADD KEY `idx_user` (`user_id`,`user_role`);

--
-- Indexes for table `qr_tokens`
--
ALTER TABLE `qr_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `token` (`token`),
  ADD KEY `idx_token` (`token`),
  ADD KEY `idx_expires_at` (`expires_at`),
  ADD KEY `idx_course_lecturer` (`course_id`,`lecturer_id`),
  ADD KEY `idx_active_tokens` (`expires_at`,`used`),
  ADD KEY `lecturer_id` (`lecturer_id`);

--
-- Indexes for table `section`
--
ALTER TABLE `section`
  ADD PRIMARY KEY (`SectionID`),
  ADD KEY `CourseID` (`CourseID`);

--
-- Indexes for table `student`
--
ALTER TABLE `student`
  ADD PRIMARY KEY (`StudentID`),
  ADD UNIQUE KEY `Email` (`Email`),
  ADD UNIQUE KEY `Username` (`UserID`);

--
-- Indexes for table `system_settings`
--
ALTER TABLE `system_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin`
--
ALTER TABLE `admin`
  MODIFY `AdminID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `attendance_report`
--
ALTER TABLE `attendance_report`
  MODIFY `AttendanceID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=113;

--
-- AUTO_INCREMENT for table `blockchain_record`
--
ALTER TABLE `blockchain_record`
  MODIFY `RecordID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `course`
--
ALTER TABLE `course`
  MODIFY `CourseID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=133;

--
-- AUTO_INCREMENT for table `courses_enhanced`
--
ALTER TABLE `courses_enhanced`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `course_assignments`
--
ALTER TABLE `course_assignments`
  MODIFY `AssignmentID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `course_registration`
--
ALTER TABLE `course_registration`
  MODIFY `RegistrationID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=60;

--
-- AUTO_INCREMENT for table `course_sections`
--
ALTER TABLE `course_sections`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `lecturer`
--
ALTER TABLE `lecturer`
  MODIFY `LectID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=26;

--
-- AUTO_INCREMENT for table `qr_tokens`
--
ALTER TABLE `qr_tokens`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=54;

--
-- AUTO_INCREMENT for table `section`
--
ALTER TABLE `section`
  MODIFY `SectionID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `student`
--
ALTER TABLE `student`
  MODIFY `StudentID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=48;

--
-- AUTO_INCREMENT for table `system_settings`
--
ALTER TABLE `system_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `attendance_report`
--
ALTER TABLE `attendance_report`
  ADD CONSTRAINT `attendance_report_ibfk_1` FOREIGN KEY (`StudentID`) REFERENCES `student` (`StudentID`) ON DELETE CASCADE,
  ADD CONSTRAINT `attendance_report_ibfk_3` FOREIGN KEY (`LectID`) REFERENCES `lecturer` (`LectID`) ON DELETE CASCADE;

--
-- Constraints for table `blockchain_record`
--
ALTER TABLE `blockchain_record`
  ADD CONSTRAINT `blockchain_record_ibfk_1` FOREIGN KEY (`AttendanceID`) REFERENCES `attendance_report` (`AttendanceID`) ON DELETE CASCADE;

--
-- Constraints for table `course`
--
ALTER TABLE `course`
  ADD CONSTRAINT `course_ibfk_1` FOREIGN KEY (`LectID`) REFERENCES `lecturer` (`LectID`) ON DELETE CASCADE,
  ADD CONSTRAINT `course_lecturer_fk` FOREIGN KEY (`LectID`) REFERENCES `lecturer` (`LectID`) ON DELETE CASCADE,
  ADD CONSTRAINT `course_student_fk` FOREIGN KEY (`StudentID`) REFERENCES `student` (`StudentID`) ON DELETE CASCADE;

--
-- Constraints for table `course_assignments`
--
ALTER TABLE `course_assignments`
  ADD CONSTRAINT `course_assignments_ibfk_1` FOREIGN KEY (`CourseID`) REFERENCES `course` (`CourseID`) ON DELETE CASCADE,
  ADD CONSTRAINT `course_assignments_ibfk_2` FOREIGN KEY (`LectID`) REFERENCES `lecturer` (`LectID`) ON DELETE CASCADE;

--
-- Constraints for table `course_registration`
--
ALTER TABLE `course_registration`
  ADD CONSTRAINT `course_registration_ibfk_1` FOREIGN KEY (`StudentID`) REFERENCES `student` (`StudentID`) ON DELETE CASCADE,
  ADD CONSTRAINT `course_registration_ibfk_2` FOREIGN KEY (`CourseID`) REFERENCES `course` (`CourseID`) ON DELETE CASCADE;

--
-- Constraints for table `course_sections`
--
ALTER TABLE `course_sections`
  ADD CONSTRAINT `course_sections_ibfk_1` FOREIGN KEY (`course_id`) REFERENCES `courses_enhanced` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `course_sections_ibfk_2` FOREIGN KEY (`lecturer_id`) REFERENCES `lecturer` (`LectID`) ON DELETE SET NULL;

--
-- Constraints for table `qr_tokens`
--
ALTER TABLE `qr_tokens`
  ADD CONSTRAINT `qr_tokens_ibfk_1` FOREIGN KEY (`course_id`) REFERENCES `course` (`CourseID`) ON DELETE CASCADE,
  ADD CONSTRAINT `qr_tokens_ibfk_2` FOREIGN KEY (`lecturer_id`) REFERENCES `lecturer` (`LectID`) ON DELETE CASCADE;

--
-- Constraints for table `section`
--
ALTER TABLE `section`
  ADD CONSTRAINT `section_ibfk_1` FOREIGN KEY (`CourseID`) REFERENCES `course` (`CourseID`) ON DELETE CASCADE;

DELIMITER $$
--
-- Events
--
CREATE DEFINER=`root`@`localhost` EVENT `cleanup_expired_qr_tokens` ON SCHEDULE EVERY 1 MINUTE STARTS '2025-06-16 13:10:58' ON COMPLETION NOT PRESERVE ENABLE DO DELETE FROM qr_tokens 
      WHERE expires_at < DATE_SUB(NOW(), INTERVAL 1 HOUR)$$

DELIMITER ;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
