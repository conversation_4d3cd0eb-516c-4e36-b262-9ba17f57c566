/* ===================================================================
   LOGIN PAGE STYLES (dashboard/css/login.css)
   Icon alignment adjusted so placeholders don't overlap icons
   ================================================================= */

:root {
  /* Primary Color Palette */
  --primary: #6366F1;         /* Purple accent */
  --primary-dark: #4F46E5;    /* Darker purple on hover */
  --secondary: #3B82F6;       /* Blue accent */
  --white: #FFFFFF;
  --gray-50: #F9FAFB;
  --gray-100: #F3F4F6;
  --gray-200: #E5E7EB;
  --gray-300: #D1D5DB;
  --gray-400: #9CA3AF;
  --gray-500: #6B7280;
  --gray-600: #4B5563;
  --gray-700: #374151;
  --gray-800: #1F2937;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --transition-fast: 150ms ease-in-out;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Inter', sans-serif;
  background: linear-gradient(135deg, var(--gray-50), var(--gray-100));
  color: var(--gray-800);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ────────────── LOGIN PAGE LAYOUT ────────────── */
.login-page {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-lg);
}

.login-container {
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  width: 100%;
  max-width: 400px;
  padding: var(--space-2xl, 32px);
  text-align: center;
}

/* ────────────── LOGO SECTION ────────────── */
.logo-section {
  margin-bottom: var(--space-lg);
}
.logo-img {
  width: 150px;
  height: auto;
}

/* ────────────── TITLE ────────────── */
.login-title {
  font-size: var(--text-2xl, 1.5rem);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-md);
}

/* ────────────── ALERT MESSAGE ────────────── */
.alert-message {
  background: #FEE2E2;      /* red-100 */
  color: #991B1B;           /* red-700 */
  border: 1px solid #EF4444;/* red-500 */
  border-radius: var(--radius-sm);
  padding: var(--space-sm) var(--space-md);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  margin-bottom: var(--space-md);
  font-size: var(--text-sm);
}
.alert-message i {
  color: #EF4444;
}

/* ────────────── FORM STYLING ────────────── */
.login-form {
  text-align: left;
}

/* Label above each input */
.input-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: var(--space-xs);
}

/* Wrapper to position input + icon + toggle */
.input-wrapper {
  position: relative;
  margin-bottom: var(--space-md);
}

/* Text inputs (increased left padding so icon does not overlap placeholder) */
.text-input {
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  padding-left: 2.75rem; /* Enough space for the icon + margin */
  font-size: var(--text-base);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  background: var(--gray-50);
  transition: border-color var(--transition-fast), background var(--transition-fast);
}
.text-input:focus {
  outline: none;
  border-color: var(--primary);
  background: var(--white);
  box-shadow: var(--shadow-sm);
}

/* Input icons on left (lighter color, non‐interactive) */
.icon-input {
  position: absolute;
  left: 1rem;              /* match input's left padding minus icon width */
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
  font-size: var(--text-base);
  pointer-events: none;    /* so clicks go to the input */
}

/* Show/hide password button on right */
.password-toggle {
  position: absolute;
  right: var(--space-md);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: var(--gray-500);
  font-size: var(--text-base);
  padding: var(--space-xs);
}
.password-toggle:hover {
  color: var(--gray-700);
}

/* ────────────── SUBMIT BUTTON ────────────── */
.btn-submit {
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--white);
  background: var(--primary);
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: background var(--transition-fast), transform var(--transition-fast);
  margin-top: var(--space-sm);
}
.btn-submit:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
}

/* ────────────── HELP LINKS UNDER FORM ────────────── */
.help-links {
  margin-top: var(--space-md);
  font-size: var(--text-sm);
  color: var(--gray-600);
}
.help-link {
  color: var(--secondary);
  text-decoration: none;
  transition: color var(--transition-fast);
}
.help-link:hover {
  color: var(--primary-dark);
}
.separator {
  margin: 0 var(--space-xs);
  color: var(--gray-400);
}

/* ────────────── RESPONSIVE ADJUSTMENTS ────────────── */
@media (max-width: 480px) {
  .login-container {
    padding: var(--space-lg);
  }
  .login-title {
    font-size: var(--text-xl);
  }
  .text-input {
    font-size: var(--text-sm);
  }
  .btn-submit {
    font-size: var(--text-sm);
  }
}

/* ────────────── UTILITY SPACING (optional) ────────────── */
.space-2xl {
  padding: var(--space-2xl);
}
