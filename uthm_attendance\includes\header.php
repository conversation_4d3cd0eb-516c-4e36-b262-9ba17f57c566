<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: Arial, sans-serif;
        }

        /* Header styling */
        header {
        position: fixed; /* Supaya header kekal di atas */
        top: 0; /* Pastikan berada di atas */
        left: 0; /* Bermula dari hujung kiri */
        width: 100%; /* Lebar penuh */
        z-index: 1000; /* Supaya header di atas elemen lain */
        background-color: #003366; /* Warna latar */
        color: white; /* Warna teks */
        padding: 5px 5px; /* <PERSON><PERSON> dalam */
        text-align: center; /* Teks di tengah */
    }

        /* Sidebar styling */
        .sidebar {
            position: fixed;
            top: 60px;
            left: 0;
            width: 200px;
            height: 100%;
            background-color: #333;
            color: white;
            padding-top: 20px;
        }

        .sidebar a {
            display: block;
            color: white;
            padding: 10px;
            text-decoration: none;
            margin: 5px 0;
        }

        .sidebar a:hover {
            background-color: #575757;
        }

        /* Main content area */
        .main-content1 {
            margin-left: 200px; /* Same as sidebar width */
            padding: 20px;
            margin-top: 60px; /* Below the header */
            background-color: #f4f4f4;
            min-height: 100vh;
        }

        .main-content {
        margin-top: 60px; /* Sama dengan ketinggian header */
        padding: 20px; /* Tambah ruang untuk kandungan */
        }
        /* Responsive layout */
        @media screen and (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }

            .sidebar a {
                text-align: center;
                float: left;
                width: 100%;
            }

            .main-content {
                margin-left: 0;
                margin-top: 120px; /* Adjust for header and expanded sidebar */
            }
        }
    </style>
</head>
<body>

<header>
    <h1>Admin Dashboard</h1>
</header>