<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>SetY</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>SetY</h1>
<code>SetY(<b>float</b> y [, <b>boolean</b> resetX])</code>
<h2>Description</h2>
Sets the ordinate and optionally moves the current abscissa back to the left margin. If the value
is negative, it is relative to the bottom of the page.
<h2>Parameters</h2>
<dl class="param">
<dt><code>y</code></dt>
<dd>
The value of the ordinate.
</dd>
<dt><code>resetX</code></dt>
<dd>
Whether to reset the abscissa. Default value: <code>true</code>.
</dd>
</dl>
<h2>See also</h2>
<a href="getx.htm">GetX</a>,
<a href="gety.htm">GetY</a>,
<a href="setx.htm">SetX</a>,
<a href="setxy.htm">SetXY</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
