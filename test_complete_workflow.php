<?php
/**
 * Complete First-Time Login Workflow Test
 * Tests the entire flow: Login → First-time detection → Email → Password reset
 */

require_once 'config/config.php';
require_once 'includes/PasswordResetService.php';
require_once 'includes/EmailService.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Complete First-Time Login Workflow Test</h2>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .test-section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
    .step { background: #f0f8ff; padding: 15px; margin: 10px 0; border-left: 4px solid #007cba; }
    .result { background: #f9f9f9; padding: 10px; margin: 5px 0; border-radius: 3px; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
    .form-section { background: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0; }
    input, select { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
    button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
    button:hover { background: #005a87; }
</style>";

// Handle workflow test
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_workflow'])) {
    $testEmail = trim($_POST['test_email']);
    $testName = trim($_POST['test_name']);
    $userType = $_POST['user_type'];
    
    echo "<div class='test-section'>";
    echo "<h3>🔄 Complete Workflow Test</h3>";
    
    try {
        // Step 1: Create test user
        echo "<div class='step'>";
        echo "<h4>Step 1: Creating Test User</h4>";
        
        if ($userType === 'student') {
            // Get next student ID
            $result = $conn->query("SELECT MAX(CAST(SUBSTRING(StudentID, 3) AS UNSIGNED)) as max_id FROM student WHERE StudentID LIKE 'AI%'");
            $row = $result->fetch_assoc();
            $nextId = $row['max_id'] ? $row['max_id'] + 1 : 250001;
            $userId = 'AI' . str_pad($nextId, 6, '0', STR_PAD_LEFT);
            
            // Create test student
            $defaultPassword = password_hash('password123', PASSWORD_DEFAULT);
            $stmt = $conn->prepare("INSERT INTO student (StudentID, Name, Email, Password, FirstLogin) VALUES (?, ?, ?, ?, 1)");
            $stmt->bind_param("ssss", $userId, $testName, $testEmail, $defaultPassword);
            
        } else {
            // Get next lecturer ID
            $result = $conn->query("SELECT MAX(CAST(LectID AS UNSIGNED)) as max_id FROM lecturer");
            $row = $result->fetch_assoc();
            $nextId = $row['max_id'] ? $row['max_id'] + 1 : 1;
            $userId = str_pad($nextId, 5, '0', STR_PAD_LEFT);
            
            // Create test lecturer
            $defaultPassword = password_hash('password123', PASSWORD_DEFAULT);
            $stmt = $conn->prepare("INSERT INTO lecturer (LectID, Name, Email, Password, FirstLogin) VALUES (?, ?, ?, ?, 1)");
            $stmt->bind_param("ssss", $userId, $testName, $testEmail, $defaultPassword);
        }
        
        if ($stmt->execute()) {
            echo "<div class='result success'>✓ Test user created successfully!</div>";
            echo "<div class='result info'>User ID: $userId</div>";
            echo "<div class='result info'>Password: password123</div>";
            echo "<div class='result info'>FirstLogin: Yes</div>";
        } else {
            throw new Exception("Failed to create test user: " . $conn->error);
        }
        echo "</div>";
        
        // Step 2: Simulate login authentication
        echo "<div class='step'>";
        echo "<h4>Step 2: Simulating Login Authentication</h4>";
        
        $table = ($userType === 'student') ? 'student' : 'lecturer';
        $idField = ($userType === 'student') ? 'StudentID' : 'LectID';
        
        $query = $conn->prepare("SELECT * FROM $table WHERE $idField = ?");
        $query->bind_param("s", $userId);
        $query->execute();
        $result = $query->get_result();
        
        if ($result->num_rows === 1) {
            $userData = $result->fetch_assoc();
            echo "<div class='result success'>✓ User found in database</div>";
            
            if (password_verify('password123', $userData['Password'])) {
                echo "<div class='result success'>✓ Password verification successful</div>";
                
                if ($userData['FirstLogin']) {
                    echo "<div class='result success'>✓ First-time login detected</div>";
                    echo "<div class='result info'>→ Would redirect to: modules/password_reset_email.php</div>";
                } else {
                    echo "<div class='result warning'>⚠ FirstLogin flag is not set</div>";
                }
            } else {
                echo "<div class='result error'>✗ Password verification failed</div>";
            }
        } else {
            echo "<div class='result error'>✗ User not found after creation</div>";
        }
        echo "</div>";
        
        // Step 3: Test password reset email sending
        echo "<div class='step'>";
        echo "<h4>Step 3: Testing Password Reset Email</h4>";
        
        $passwordResetService = new PasswordResetService($conn);
        $emailResult = $passwordResetService->initiatePasswordReset($userId, $userType, $testEmail, $testName);
        
        if ($emailResult['success']) {
            echo "<div class='result success'>✓ Password reset email sent successfully</div>";
            echo "<div class='result info'>Message: " . $emailResult['message'] . "</div>";
            
            if (isset($emailResult['token'])) {
                $token = $emailResult['token'];
                echo "<div class='result info'>Reset Token: " . substr($token, 0, 20) . "...</div>";
                
                // Step 4: Test token validation
                echo "</div>";
                echo "<div class='step'>";
                echo "<h4>Step 4: Testing Token Validation</h4>";
                
                $tokenData = $passwordResetService->validateToken($token);
                if ($tokenData) {
                    echo "<div class='result success'>✓ Token validation successful</div>";
                    echo "<div class='result info'>Token User ID: " . $tokenData['user_id'] . "</div>";
                    echo "<div class='result info'>Token Role: " . $tokenData['user_role'] . "</div>";
                    echo "<div class='result info'>Token Email: " . $tokenData['email'] . "</div>";
                    
                    // Step 5: Test password reset
                    echo "</div>";
                    echo "<div class='step'>";
                    echo "<h4>Step 5: Testing Password Reset</h4>";
                    
                    $newPassword = 'NewPassword123!';
                    $resetResult = $passwordResetService->resetPassword($token, $newPassword);
                    
                    if ($resetResult['success']) {
                        echo "<div class='result success'>✓ Password reset successful</div>";
                        echo "<div class='result info'>Message: " . $resetResult['message'] . "</div>";
                        
                        // Verify FirstLogin flag was cleared
                        $query = $conn->prepare("SELECT FirstLogin FROM $table WHERE $idField = ?");
                        $query->bind_param("s", $userId);
                        $query->execute();
                        $result = $query->get_result();
                        $user = $result->fetch_assoc();
                        
                        if ($user['FirstLogin'] == 0) {
                            echo "<div class='result success'>✓ FirstLogin flag cleared successfully</div>";
                        } else {
                            echo "<div class='result warning'>⚠ FirstLogin flag not cleared</div>";
                        }
                        
                        // Test new password
                        $query = $conn->prepare("SELECT Password FROM $table WHERE $idField = ?");
                        $query->bind_param("s", $userId);
                        $query->execute();
                        $result = $query->get_result();
                        $user = $result->fetch_assoc();
                        
                        if (password_verify($newPassword, $user['Password'])) {
                            echo "<div class='result success'>✓ New password verification successful</div>";
                        } else {
                            echo "<div class='result error'>✗ New password verification failed</div>";
                        }
                        
                    } else {
                        echo "<div class='result error'>✗ Password reset failed: " . $resetResult['message'] . "</div>";
                    }
                } else {
                    echo "<div class='result error'>✗ Token validation failed</div>";
                }
            }
        } else {
            echo "<div class='result error'>✗ Password reset email failed: " . $emailResult['message'] . "</div>";
        }
        echo "</div>";
        
        // Step 6: Test file accessibility
        echo "<div class='step'>";
        echo "<h4>Step 6: Testing File Accessibility</h4>";
        
        $files = [
            'modules/password_reset_email.php' => 'Password Reset Email Page',
            'modules/password_reset_form.php' => 'Password Reset Form Page',
            'email_delivery_guide.php' => 'Email Delivery Guide'
        ];
        
        foreach ($files as $file => $description) {
            if (file_exists($file)) {
                echo "<div class='result success'>✓ $description: File exists</div>";
            } else {
                echo "<div class='result error'>✗ $description: File missing</div>";
            }
        }
        echo "</div>";
        
        // Cleanup
        echo "<div class='step'>";
        echo "<h4>Step 7: Cleanup</h4>";
        
        // Delete test user
        $deleteStmt = $conn->prepare("DELETE FROM $table WHERE $idField = ?");
        $deleteStmt->bind_param("s", $userId);
        if ($deleteStmt->execute()) {
            echo "<div class='result success'>✓ Test user cleaned up</div>";
        } else {
            echo "<div class='result warning'>⚠ Failed to cleanup test user</div>";
        }
        
        // Delete password reset tokens
        $deleteTokens = $conn->prepare("DELETE FROM password_reset_tokens WHERE user_id = ?");
        $deleteTokens->bind_param("s", $userId);
        if ($deleteTokens->execute()) {
            echo "<div class='result success'>✓ Password reset tokens cleaned up</div>";
        } else {
            echo "<div class='result warning'>⚠ Failed to cleanup tokens</div>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='result error'>✗ Exception: " . $e->getMessage() . "</div>";
    }
    
    echo "</div>";
}

// Test form
echo "<div class='form-section'>";
echo "<h3>🧪 Complete Workflow Test</h3>";
echo "<p>This will test the entire first-time login workflow from start to finish.</p>";
echo "<form method='POST'>";
echo "<label for='user_type'>User Type:</label>";
echo "<select id='user_type' name='user_type' required>";
echo "<option value='student'>Student</option>";
echo "<option value='lecturer'>Lecturer</option>";
echo "</select>";
echo "<label for='test_name'>Full Name:</label>";
echo "<input type='text' id='test_name' name='test_name' placeholder='Enter full name' required>";
echo "<label for='test_email'>Email Address:</label>";
echo "<input type='email' id='test_email' name='test_email' placeholder='Enter email address' required>";
echo "<button type='submit' name='test_workflow'>🧪 Run Complete Workflow Test</button>";
echo "</form>";
echo "</div>";

// System status
echo "<div class='test-section'>";
echo "<h3>🔧 System Status</h3>";
echo "<p><strong>Database Connection:</strong> " . ($conn ? "✓ Connected" : "✗ Failed") . "</p>";
echo "<p><strong>PasswordResetService:</strong> " . (class_exists('PasswordResetService') ? "✓ Available" : "✗ Missing") . "</p>";
echo "<p><strong>EmailService:</strong> " . (class_exists('EmailService') ? "✓ Available" : "✗ Missing") . "</p>";

// Check critical files
$criticalFiles = [
    'modules/password_reset_email.php',
    'modules/password_reset_form.php',
    'email_delivery_guide.php',
    'includes/PasswordResetService.php',
    'includes/EmailService.php'
];

foreach ($criticalFiles as $file) {
    echo "<p><strong>$file:</strong> " . (file_exists($file) ? "✓ Exists" : "✗ Missing") . "</p>";
}

echo "</div>";

echo "<p><a href='index.php'>← Back to Login</a> | <a href='test_first_time_login.php'>🔧 Test First-Time Login</a></p>";
?>
