<?php
/**
 * Forgot Password System - Multi-Step Flow
 * Step 1: Enter User ID/Email
 * Step 2: Confirmation page with send email button
 * Step 3: Email sent confirmation (redirects to forgot_password_email.php)
 */

session_start();
require_once '../config/config.php';
require_once __DIR__ . '/../includes/PasswordResetService.php';

$message = '';
$messageType = 'error';
$step = 1;
$user_data = null;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'find_user') {
        // Step 1: Find user by ID/Email
        $user_input = trim($_POST['user_id']);

        if (empty($user_input)) {
            $message = 'Please enter your Student/Staff/Admin ID or Email.';
            $messageType = 'error';
        } else {
            // Search for user in all tables
            $tables = [
                [
                    'table' => 'student',
                    'field_id' => 'UserID',
                    'field_email' => 'Email',
                    'field_name' => 'Name',
                    'role' => 'student'
                ],
                [
                    'table' => 'lecturer',
                    'field_id' => 'UserID',
                    'field_email' => 'Email',
                    'field_name' => 'Name',
                    'role' => 'lecturer'
                ],
                [
                    'table' => 'admin',
                    'field_id' => 'Username',
                    'field_email' => 'Username', // Admin uses username as email
                    'field_name' => 'Username',
                    'role' => 'admin'
                ]
            ];

            $foundUser = false;
            foreach ($tables as $info) {
                $sql = "SELECT * FROM {$info['table']} WHERE {$info['field_id']} = ? OR {$info['field_email']} = ? LIMIT 1";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("ss", $user_input, $user_input);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($result && $result->num_rows > 0) {
                    $user = $result->fetch_assoc();
                    $foundUser = true;

                    // Store user data in session for step 2
                    $_SESSION['forgot_password_user'] = [
                        'user_id' => $user_input,
                        'role' => $info['role'],
                        'table' => $info['table'],
                        'field_id' => $info['field_id'],
                        'field_email' => $info['field_email'],
                        'field_name' => $info['field_name'],
                        'name' => $user[$info['field_name']],
                        'email' => $info['role'] === 'admin' ? $user['Username'] : $user['Email']
                    ];

                    $user_data = $_SESSION['forgot_password_user'];
                    $step = 2;
                    break;
                }
                $stmt->close();
            }

            if (!$foundUser) {
                $message = 'User not found. Please check your ID/Email and try again.';
                $messageType = 'error';
            }
        }

    } elseif (isset($_POST['action']) && $_POST['action'] === 'send_reset_email') {
        // Step 2: Send password reset email
        if (!isset($_SESSION['forgot_password_user'])) {
            header("Location: forgot_password.php");
            exit();
        }

        $user_data = $_SESSION['forgot_password_user'];

        // For admin users, show error since they don't have email reset
        if ($user_data['role'] === 'admin') {
            $message = 'Password reset via email is not available for admin accounts. Please contact your system administrator.';
            $messageType = 'error';
            $step = 2;
        } else {
            // Set session data for email page (similar to first time login)
            $_SESSION['forgot_password_email_user_id'] = $user_data['user_id'];
            $_SESSION['forgot_password_email_role'] = $user_data['role'];
            $_SESSION['forgot_password_email_time'] = time();

            // Redirect to email confirmation page
            header("Location: forgot_password_email.php");
            exit();
        }
    }
}

// Check if returning from email page or if user data exists in session
if (isset($_SESSION['forgot_password_user']) && !isset($_POST['action'])) {
    $user_data = $_SESSION['forgot_password_user'];
    $step = 2;
}

// Clean up session if user navigates away or starts over
if (isset($_GET['reset']) && $_GET['reset'] === 'true') {
    unset($_SESSION['forgot_password_user']);
    unset($_SESSION['forgot_password_email_user_id']);
    unset($_SESSION['forgot_password_email_role']);
    unset($_SESSION['forgot_password_email_time']);
    header("Location: forgot_password.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - UTHM Attendance</title>

    <!-- ─── GLOBAL CSS (same as other dashboards) ─── -->
    <link rel="stylesheet" href="../dashboard/css/base-styles.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-header.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-sidebar.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-footer.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-dashboard-styles.css" />

    <!-- ─── ICONS ─── -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        /* ─────────── FORGOT PASSWORD PAGE SPECIFIC STYLES ─────────── */
        .forgot-container {
            max-width: 500px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        .forgot-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-md);
        }

        .forgot-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
        }

        .forgot-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-md);
        }

        .forgot-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .forgot-subtitle {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .form-section {
            margin-bottom: var(--spacing-lg);
        }

        .form-group {
            margin-bottom: var(--spacing-lg);
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .form-input {
            width: 100%;
            padding: var(--spacing-md);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: #ffffff;
            border: 2px solid #3b82f6;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            min-height: 48px;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
        }

        .submit-btn:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            border-color: #2563eb;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }

        .back-link {
            display: block;
            text-align: center;
            margin-top: var(--spacing-lg);
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .back-link:hover {
            color: var(--primary-color);
            text-decoration: underline;
        }

        .confirmation-section {
            text-align: center;
        }

        .user-info {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: var(--border-radius);
            padding: var(--spacing-lg);
            margin: var(--spacing-lg) 0;
        }

        .user-info h3 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .user-details {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .user-details strong {
            color: var(--primary-color);
        }

        .action-buttons {
            display: flex;
            gap: var(--spacing-md);
            justify-content: center;
            margin-top: var(--spacing-lg);
            flex-wrap: wrap;
        }

        .btn-send {
            background: #10b981;
            color: #ffffff;
            border: 2px solid #10b981;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            min-height: 48px;
            box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
        }

        .btn-send:hover {
            background: #059669;
            border-color: #059669;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
        }

        .btn-back {
            background: #6b7280;
            color: #ffffff;
            border: 2px solid #6b7280;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            min-height: 48px;
            text-decoration: none;
            box-shadow: 0 2px 4px rgba(107, 114, 128, 0.2);
        }

        .btn-back:hover {
            background: #495057;
            border-color: #495057;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(107, 114, 128, 0.4);
            color: #ffffff;
            text-decoration: none;
        }

        /* ─────────── RESPONSIVE DESIGN ─────────── */
        @media (max-width: 768px) {
            .forgot-container {
                padding: var(--spacing-lg);
            }

            .forgot-card {
                padding: var(--spacing-lg);
            }

            .forgot-icon {
                font-size: 2.5rem;
            }

            .forgot-title {
                font-size: 1.3rem;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn-send, .btn-back {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-left">
            <img src="../assets/images/logo-uthm2.png" alt="UTHM Logo" class="logo">
        </div>
        <div class="header-right">
            <span class="user-id">Password Recovery</span>
        </div>
    </div>

    <div class="container">
        <div class="forgot-container">
            <div class="forgot-card">
                <?php if ($step == 1): ?>
                    <!-- Step 1: Enter User ID/Email -->
                    <div class="forgot-header">
                        <div class="forgot-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <h1 class="forgot-title">Forgot Your Password?</h1>
                        <p class="forgot-subtitle">
                            Enter your Student ID, Staff ID, Admin ID, or Email address to begin the password recovery process.
                        </p>
                    </div>

                    <!-- Message Display -->
                    <?php if (!empty($message)): ?>
                        <div class="popup-message <?= $messageType ?>" style="margin-bottom: var(--spacing-lg);">
                            <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'times-circle' ?>"></i>
                            <?= htmlspecialchars($message) ?>
                        </div>
                    <?php endif; ?>

                    <form method="post" class="form-section">
                        <div class="form-group">
                            <label for="user_id">Student ID / Staff ID / Admin ID / Email</label>
                            <input type="text" id="user_id" name="user_id" class="form-input"
                                   placeholder="Enter your ID or email address" required
                                   value="<?= isset($_POST['user_id']) ? htmlspecialchars($_POST['user_id']) : '' ?>">
                        </div>

                        <input type="hidden" name="action" value="find_user">
                        <button type="submit" class="submit-btn">
                            <i class="fas fa-search"></i>
                            Find My Account
                        </button>
                    </form>

                    <a href="../index.php" class="back-link">
                        <i class="fas fa-arrow-left"></i>
                        Back to Login
                    </a>

                <?php elseif ($step == 2): ?>
                    <!-- Step 2: Confirmation Page -->
                    <div class="forgot-header">
                        <div class="forgot-icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <h1 class="forgot-title">Account Found</h1>
                        <p class="forgot-subtitle">
                            We found your account. Click the button below to send a secure password reset link to your email.
                        </p>
                    </div>

                    <!-- Message Display -->
                    <?php if (!empty($message)): ?>
                        <div class="popup-message <?= $messageType ?>" style="margin-bottom: var(--spacing-lg);">
                            <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'times-circle' ?>"></i>
                            <?= htmlspecialchars($message) ?>
                        </div>
                    <?php endif; ?>

                    <!-- User Information Display -->
                    <?php if ($user_data): ?>
                        <div class="user-info">
                            <h3>Account Details</h3>
                            <div class="user-details">
                                <p><strong>Name:</strong> <?= htmlspecialchars($user_data['name']) ?></p>
                                <p><strong>Account Type:</strong> <?= ucfirst($user_data['role']) ?></p>
                                <?php if ($user_data['role'] !== 'admin'): ?>
                                    <p><strong>Email:</strong> <?= htmlspecialchars($user_data['email']) ?></p>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="confirmation-section">
                            <?php if ($user_data['role'] === 'admin'): ?>
                                <p style="color: var(--text-secondary); margin: var(--spacing-lg) 0;">
                                    Admin accounts cannot reset passwords via email. Please contact your system administrator for assistance.
                                </p>
                                <div class="action-buttons">
                                    <a href="forgot_password.php?reset=true" class="btn-back">
                                        <i class="fas fa-arrow-left"></i>
                                        Try Different Account
                                    </a>
                                    <a href="../index.php" class="btn-back">
                                        <i class="fas fa-home"></i>
                                        Back to Login
                                    </a>
                                </div>
                            <?php else: ?>
                                <form method="post">
                                    <input type="hidden" name="action" value="send_reset_email">
                                    <div class="action-buttons">
                                        <a href="forgot_password.php?reset=true" class="btn-back">
                                            <i class="fas fa-arrow-left"></i>
                                            Try Different Account
                                        </a>
                                        <button type="submit" class="btn-send">
                                            <i class="fas fa-envelope"></i>
                                            Send Reset Link to Email
                                        </button>
                                    </div>
                                </form>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <p>UNIVERSITI TUN HUSSEIN ONN MALAYSIA</p>
    </footer>

    <script>
        // Auto-hide success/error messages after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const messages = document.querySelectorAll('.popup-message');
            messages.forEach(function(message) {
                setTimeout(function() {
                    message.style.opacity = '0';
                    setTimeout(function() {
                        message.style.display = 'none';
                    }, 300);
                }, 5000);
            });
        });

        // Focus on input field when page loads
        document.addEventListener('DOMContentLoaded', function() {
            const userIdInput = document.getElementById('user_id');
            if (userIdInput) {
                userIdInput.focus();
            }
        });
    </script>
</body>
</html>
