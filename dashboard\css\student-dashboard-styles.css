/* ===================================================================
   ENHANCED STUDENT DASHBOARD STYLES (student-dashboard-enhanced.css)
   Professional, Creative, Minimalist & User-Friendly Design
   ================================================================= */

/* ─────────── MODERN COLOR PALETTE ─────────── */
:root {
  /* Primary Colors */
  --primary: #6366F1;
  --primary-light: #818CF8;
  --primary-dark: #4F46E5;
  --primary-gradient: linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%);
  
  /* Semantic Colors */
  --success: #10B981;
  --success-light: #34D399;
  --warning: #F59E0B;
  --warning-light: #FBBF24;
  --danger: #EF4444;
  --danger-light: #F87171;
  --info: #3B82F6;
  --info-light: #60A5FA;
  
  /* Neutral Colors */
  --white: #FFFFFF;
  --gray-50: #F9FAFB;
  --gray-100: #F3F4F6;
  --gray-200: #E5E7EB;
  --gray-300: #D1D5DB;
  --gray-400: #9CA3AF;
  --gray-500: #6B7280;
  --gray-600: #4B5563;
  --gray-700: #374151;
  --gray-800: #1F2937;
  --gray-900: #111827;
  
  /* Shadows */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Border Radius */
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 24px;
  --radius-full: 9999px;
  
  /* Spacing */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --space-2xl: 48px;
  
  /* Typography */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  
  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* ─────────── BASE STYLES ─────────── */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-sans);
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
  color: var(--gray-800);
  line-height: 1.6;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

/* ─────────── MAIN CONTENT AREA ─────────── */
.main-content {
  flex: 1;
  padding: var(--space-xl);
  background: transparent;
  min-height: calc(100vh - 160px);
}

/* ─────────── ENHANCED PAGE HEADER ─────────── */
.page-header {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  margin-bottom: var(--space-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--space-lg);
}

.header-text h1 {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--gray-900);
  margin: 0 0 var(--space-sm) 0;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-text p {
  font-size: var(--text-base);
  color: var(--gray-600);
  margin: 0;
  font-weight: 400;
}

.header-date {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  background: var(--gray-50);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-full);
  color: var(--gray-700);
  font-size: var(--text-sm);
  font-weight: 500;
  border: 1px solid var(--gray-200);
}

.header-date i {
  color: var(--primary);
}

/* ─────────── ENHANCED STATS CARDS ─────────── */
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-2xl);
}

.stats-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: var(--space-lg);
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-gradient);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform var(--transition-normal);
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-light);
}

.stats-card:hover::before {
  transform: scaleX(1);
}

.card-icon {
  width: 64px;
  height: 64px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  position: relative;
  flex-shrink: 0;
}

/* Card-specific colors */
.courses-card .card-icon {
  background: linear-gradient(135deg, #3B82F6, #1D4ED8);
  color: var(--white);
}

.attendance-card .card-icon {
  background: linear-gradient(135deg, #10B981, #047857);
  color: var(--white);
}

.sessions-card .card-icon {
  background: linear-gradient(135deg, #F59E0B, #D97706);
  color: var(--white);
}

.present-card .card-icon {
  background: linear-gradient(135deg, #8B5CF6, #7C3AED);
  color: var(--white);
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.card-number {
  font-size: var(--text-3xl);
  font-weight: 800;
  color: var(--gray-900);
  line-height: 1;
}

.card-label {
  font-size: var(--text-base);
  color: var(--gray-600);
  font-weight: 500;
  margin: var(--space-xs) 0;
}

.card-trend {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: var(--text-sm);
  font-weight: 600;
}

.card-trend.positive {
  color: var(--success);
}

.card-trend.warning {
  color: var(--warning);
}

.card-trend.negative {
  color: var(--danger);
}

/* ─────────── CHARTS SECTION ─────────── */
.charts-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-lg);
  margin-bottom: var(--space-2xl);
}

.chart-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
}

.chart-card:hover {
  box-shadow: var(--shadow-md);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-xl);
  padding-bottom: var(--space-md);
  border-bottom: 1px solid var(--gray-200);
}

.chart-header h2 {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--gray-900);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.chart-header h2 i {
  color: var(--primary);
}

.chart-controls {
  display: flex;
  gap: var(--space-xs);
  background: var(--gray-50);
  padding: var(--space-xs);
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
}

.chart-btn {
  background: transparent;
  border: none;
  padding: var(--space-sm);
  border-radius: var(--radius-sm);
  cursor: pointer;
  color: var(--gray-500);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.chart-btn:hover {
  background: var(--gray-200);
  color: var(--gray-700);
}

.chart-btn.active {
  background: var(--primary);
  color: var(--white);
  box-shadow: var(--shadow-sm);
}

.chart-wrapper {
  position: relative;
  height: 350px;
  margin-bottom: var(--space-md);
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: var(--space-lg);
  margin-top: var(--space-lg);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--gray-700);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: var(--radius-sm);
}

.legend-color.present {
  background: var(--success);
}

.legend-color.absent {
  background: var(--danger);
}

/* ─────────── EMPTY STATE ─────────── */
.empty-state {
  text-align: center;
  padding: var(--space-2xl);
  color: var(--gray-500);
}

.empty-content {
  max-width: 400px;
  margin: 0 auto;
}

.empty-content i {
  font-size: 4rem;
  color: var(--gray-300);
  margin-bottom: var(--space-lg);
}

.empty-content h3 {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--gray-700);
  margin: 0 0 var(--space-sm) 0;
}

.empty-content p {
  font-size: var(--text-base);
  color: var(--gray-500);
  margin: 0 0 var(--space-xl) 0;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  background: var(--primary-gradient);
  color: var(--white);
  text-decoration: none;
  padding: var(--space-md) var(--space-xl);
  border-radius: var(--radius-full);
  font-weight: 600;
  font-size: var(--text-sm);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  text-decoration: none;
  color: var(--white);
}

/* ─────────── ENHANCED TABLE ─────────── */
.table-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-xl);
  padding-bottom: var(--space-lg);
  border-bottom: 1px solid var(--gray-200);
}

.table-header h2 {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--gray-900);
  margin: 0;
}

.table-actions {
  display: flex;
  gap: var(--space-md);
  align-items: center;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box i {
  position: absolute;
  left: var(--space-md);
  color: var(--gray-400);
  z-index: 1;
}

.search-box input {
  padding: var(--space-sm) var(--space-md) var(--space-sm) var(--space-2xl);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  background: var(--gray-50);
  color: var(--gray-700);
  transition: all var(--transition-fast);
  width: 250px;
}

.search-box input:focus {
  outline: none;
  border-color: var(--primary);
  background: var(--white);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.table-wrapper {
  overflow-x: auto;
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-200);
}

.courses-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--text-sm);
}

.courses-table th {
  background: var(--gray-50);
  padding: var(--space-md) var(--space-lg);
  text-align: left;
  font-weight: 600;
  color: var(--gray-700);
  border-bottom: 1px solid var(--gray-200);
  white-space: nowrap;
}

.courses-table td {
  padding: var(--space-lg);
  border-bottom: 1px solid var(--gray-100);
  vertical-align: middle;
}

.courses-table tr:last-child td {
  border-bottom: none;
}

.courses-table tr:hover td {
  background: var(--gray-50);
}

.course-id {
  font-family: 'Monaco', 'Consolas', monospace;
  font-weight: 600;
  color: var(--gray-800);
  background: var(--gray-100);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  display: inline-block;
}

.course-name {
  font-weight: 500;
  color: var(--gray-800);
  line-height: 1.4;
}

.sessions-info {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-weight: 500;
}

.sessions-info .present {
  color: var(--success);
  font-weight: 600;
}

.sessions-info .total {
  color: var(--gray-600);
}

.attendance-rate {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  min-width: 120px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--success), var(--success-light));
  border-radius: var(--radius-full);
  transition: width var(--transition-slow);
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.percentage {
  font-size: var(--text-xs);
  font-weight: 600;
  color: var(--gray-700);
  text-align: center;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.excellent {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-badge.good {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.status-badge.warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.empty-state-table {
  text-align: center;
  padding: var(--space-2xl);
  color: var(--gray-500);
}

.empty-state-table i {
  font-size: 3rem;
  color: var(--gray-300);
  margin-bottom: var(--space-lg);
}

.empty-state-table h3 {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--gray-700);
  margin: 0 0 var(--space-sm) 0;
}

.empty-state-table p {
  color: var(--gray-500);
  margin: 0;
}

/* ─────────── RESPONSIVE DESIGN ─────────── */
@media (max-width: 1200px) {
  .charts-container {
    grid-template-columns: 1fr;
  }
  
  .chart-wrapper {
    height: 300px;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: var(--space-lg);
  }
  
  .page-header {
    padding: var(--space-lg);
  }
  
  .header-content {
    flex-direction: column;
    gap: var(--space-md);
  }
  
  .header-text h1 {
    font-size: var(--text-2xl);
  }
  
  .card-container {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }
  
  .stats-card {
    padding: var(--space-lg);
    flex-direction: column;
    text-align: center;
    gap: var(--space-md);
  }
  
  .card-icon {
    width: 56px;
    height: 56px;
  }
  
  .chart-wrapper {
    height: 250px;
  }
  
  .table-header {
    flex-direction: column;
    gap: var(--space-md);
    align-items: stretch;
  }
  
  .search-box input {
    width: 100%;
  }
  
  .courses-table {
    font-size: var(--text-xs);
  }
  
  .courses-table th,
  .courses-table td {
    padding: var(--space-sm);
  }
  
  /* Mobile table layout */
  .courses-table thead {
    display: none;
  }
  
  .courses-table,
  .courses-table tbody,
  .courses-table tr,
  .courses-table td {
    display: block;
  }
  
  .courses-table tr {
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-md);
    padding: var(--space-md);
    background: var(--white);
  }
  
  .courses-table td {
    border: none;
    padding: var(--space-sm) 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .courses-table td::before {
    content: attr(data-label) ": ";
    font-weight: 600;
    color: var(--gray-600);
  }
  
  .attendance-rate {
    min-width: auto;
    width: 100px;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: var(--space-md);
  }
  
  .page-header {
    padding: var(--space-md);
  }
  
  .header-text h1 {
    font-size: var(--text-xl);
  }
  
  .stats-card {
    padding: var(--space-md);
  }
  
  .card-number {
    font-size: var(--text-2xl);
  }
  
  .chart-card,
  .table-card {
    padding: var(--space-lg);
  }
}

/* ─────────── ACCESSIBILITY IMPROVEMENTS ─────────── */
@media (prefers-reduced-motion: reduce) {
  *,
  ::before,
  ::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus states for better accessibility */
.chart-btn:focus,
.search-box input:focus,
.cta-button:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --gray-100: #F0F0F0;
    --gray-200: #E0E0E0;
    --gray-300: #C0C0C0;
    --border-color: #808080;
  }
}

/* Dark mode preparation (optional) */
@media (prefers-color-scheme: dark) {
  :root {
    --white: #1F2937;
    --gray-50: #111827;
    --gray-100: #1F2937;
    --gray-200: #374151;
    --gray-800: #F9FAFB;
    --gray-900: #FFFFFF;
  }
}