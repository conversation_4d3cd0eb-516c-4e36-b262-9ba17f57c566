{"packages": [{"name": "phpmailer/phpmailer", "version": "v6.10.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/PHPMailer/PHPMailer.git", "reference": "bf74d75a1fde6beaa34a0ddae2ec5fce0f72a144"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPMailer/PHPMailer/zipball/bf74d75a1fde6beaa34a0ddae2ec5fce0f72a144", "reference": "bf74d75a1fde6beaa34a0ddae2ec5fce0f72a144", "shasum": ""}, "require": {"ext-ctype": "*", "ext-filter": "*", "ext-hash": "*", "php": ">=5.5.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0", "doctrine/annotations": "^1.2.6 || ^1.13.3", "php-parallel-lint/php-console-highlighter": "^1.0.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcompatibility/php-compatibility": "^9.3.5", "roave/security-advisories": "dev-latest", "squizlabs/php_codesniffer": "^3.7.2", "yoast/phpunit-polyfills": "^1.0.4"}, "suggest": {"decomplexity/SendOauth2": "Adapter for using XOAUTH2 authentication", "ext-mbstring": "Needed to send email in multibyte encoding charset or decode encoded addresses", "ext-openssl": "Needed for secure SMTP sending and DKIM signing", "greew/oauth2-azure-provider": "Needed for Microsoft Azure XOAUTH2 authentication", "hayageek/oauth2-yahoo": "Needed for Yahoo XOAUTH2 authentication", "league/oauth2-google": "Needed for Google XOAUTH2 authentication", "psr/log": "For optional PSR-3 debug logging", "symfony/polyfill-mbstring": "To support UTF-8 if the Mbstring PHP extension is not enabled (^1.2)", "thenetworg/oauth2-azure": "Needed for Microsoft XOAUTH2 authentication"}, "time": "2025-04-24T15:19:31+00:00", "type": "library", "installation-source": "source", "autoload": {"psr-4": {"PHPMailer\\PHPMailer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-only"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "description": "PHPMailer is a full-featured email creation and transfer class for PHP", "support": {"issues": "https://github.com/PHPMailer/PHPMailer/issues", "source": "https://github.com/PHPMailer/PHPMailer/tree/v6.10.0"}, "funding": [{"url": "https://github.com/Synchro", "type": "github"}], "install-path": "../phpmailer/phpmailer"}, {"name": "rob<PERSON><PERSON>/twofactor<PERSON>h", "version": "v3.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/RobThree/TwoFactorAuth.git", "reference": "6d70f9ca8e25568f163a7b3b3ff77bd8ea743978"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/RobThree/TwoFactorAuth/zipball/6d70f9ca8e25568f163a7b3b3ff77bd8ea743978", "reference": "6d70f9ca8e25568f163a7b3b3ff77bd8ea743978", "shasum": ""}, "require": {"php": ">=8.2.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.13", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^9"}, "suggest": {"bacon/bacon-qr-code": "Needed for BaconQrCodeProvider provider", "endroid/qr-code": "Needed for EndroidQrCodeProvider"}, "time": "2024-10-24T15:14:25+00:00", "type": "library", "installation-source": "source", "autoload": {"psr-4": {"RobThree\\Auth\\": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://robiii.me", "role": "Developer"}, {"name": "<PERSON>", "homepage": "https://github.com/NicolasCARPi", "role": "Developer"}, {"name": "Will Power", "homepage": "https://github.com/willpower232", "role": "Developer"}], "description": "Two Factor Authentication", "homepage": "https://github.com/RobThree/TwoFactorAuth", "keywords": ["Authentication", "MFA", "Multi Factor Authentication", "Two Factor Authentication", "authenticator", "authy", "php", "tfa"], "support": {"issues": "https://github.com/RobThree/TwoFactorAuth/issues", "source": "https://github.com/RobThree/TwoFactorAuth"}, "funding": [{"url": "https://paypal.me/robiii", "type": "custom"}, {"url": "https://github.com/RobThree", "type": "github"}], "install-path": "../robthree/twofactorauth"}], "dev": true, "dev-package-names": []}