<?php
/**
 * generate_qr_helper.php
 *
 * Contains a function to generate (or regenerate) a dynamic QR code PNG file
 * based on $_SESSION['active_attendance'] and $_SESSION['qr_token'].
 *
 * Returns the relative path (from this PHP file) to the newly generated PNG.
 */

if (session_status() !== PHP_SESSION_ACTIVE) {
    session_start();
}

require_once __DIR__ . '/../phpqrcode/qrlib.php';

function generateQRCodeFile() {
    global $conn;

    if (!isset($_SESSION['active_attendance']) || !isset($_SESSION['qr_token'])) {
        return '';
    }

    $attendance = $_SESSION['active_attendance'];
    $token      = $_SESSION['qr_token'];
    $timestamp  = time();
    $time_segment = floor($timestamp / 10);

    // Generate dynamic 8‐character code
    $dynamic_hash = hash('sha256', $token . $time_segment);
    $dynamic_code = substr($dynamic_hash, 0, 8);

    // Build QR payload as URL‐encoded query string
    $qr_payload = http_build_query([
        'course_id'    => $attendance['course_id'],
        'lecturer_id'  => $attendance['lecturer_id'],
        'class_type'   => $attendance['class_type'],
        'class_section'=> $attendance['class_section'],
        'class_date'   => $attendance['class_date'],
        'dynamic_code' => $dynamic_code,
        'timestamp'    => $timestamp
    ]);

    // Define file path (relative to project root) and URL path for <img>
    // We store PNGs under /assets/images/qr_temp_<courseID>_<timestamp>.png
    $uploadDir   = __DIR__ . '/../assets/images';
    $filename    = "qr_temp_{$attendance['course_id']}_{$timestamp}.png";
    $absFilePath = $uploadDir . '/' . $filename;
    $relFilePath = "../assets/images/" . $filename;

    // Clean up old QR files for this course
    $pattern = $uploadDir . "/qr_temp_{$attendance['course_id']}_*.png";
    foreach (glob($pattern) as $oldFile) {
        if (file_exists($oldFile) && realpath($oldFile) !== realpath($absFilePath)) {
            unlink($oldFile);
        }
    }

    // Finally, generate new QR PNG (level L, size 10)
    QRcode::png($qr_payload, $absFilePath, QR_ECLEVEL_L, 10);

    return $relFilePath;
}
