<?php
/**
 * ===================================================================
 * SECURE LOGIN IMPLEMENTATION (TEST VERSION)
 * Enhanced authentication with proper session management
 * ===================================================================
 */

session_start();
require 'config/config.php';

$error = '';

// Clear any existing session data on login page access (security measure)
if (!isset($_POST['username'])) {
    session_unset();
    session_regenerate_id(true);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username']);
    $password = $_POST['password'];

    // Check if fields are empty
    if (empty($username) || empty($password)) {
        $error = "Please fill in both fields.";
    } else {
        // Admin Login
        $queryAdmin = $conn->prepare("SELECT * FROM admin WHERE Username = ?");
        $queryAdmin->bind_param("s", $username);
        $queryAdmin->execute();
        $resultAdmin = $queryAdmin->get_result();

        // Lecturer Login
        $queryLect = $conn->prepare("SELECT * FROM lecturer WHERE UserID = ?");
        $queryLect->bind_param("s", $username);
        $queryLect->execute();
        $resultLect = $queryLect->get_result();

        // Student Login
        $queryStudent = $conn->prepare("SELECT * FROM student WHERE UserID = ?");
        $queryStudent->bind_param("s", $username);
        $queryStudent->execute();
        $resultStudent = $queryStudent->get_result();

        // Admin Authentication
        if ($resultAdmin->num_rows === 1) {
            $user = $resultAdmin->fetch_assoc();
            $isAuthenticated = false;

            // Check if password is already hashed
            if (preg_match('/^\$2[ayb]\$[0-9]{2}\$/', $user['Password']) && strlen($user['Password']) >= 60) {
                // Secure password verification for hashed passwords
                $isAuthenticated = password_verify($password, $user['Password']);
            } else {
                // Legacy plain text comparison with auto-upgrade
                if ($password === $user['Password']) {
                    $isAuthenticated = true;

                    // Auto-upgrade to hashed password
                    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                    $updateStmt = $conn->prepare("UPDATE admin SET Password = ? WHERE AdminID = ?");
                    $updateStmt->bind_param("si", $hashedPassword, $user['AdminID']);
                    $updateStmt->execute();
                    $updateStmt->close();
                }
            }

            if ($isAuthenticated) {
                // Regenerate session ID to prevent session fixation
                session_regenerate_id(true);

                $_SESSION['user_id'] = $user['AdminID'];
                $_SESSION['role'] = 'admin';
                $_SESSION['login_time'] = time();
                $_SESSION['last_activity'] = time();

                // Log successful login
                error_log("LOGIN SUCCESS: Admin ID {$user['AdminID']} logged in at " . date('Y-m-d H:i:s'));

                header("Location: dashboard/admin.php");
                exit();
            } else {
                $error = "Incorrect password for Admin.";
            }
        }
        // Lecturer Authentication
        elseif ($resultLect->num_rows === 1) {
            $user = $resultLect->fetch_assoc();

            // Log lecturer login attempt for debugging
            error_log("LECTURER LOGIN ATTEMPT: UserID {$username}, LectID {$user['LectID']}, FirstLogin: {$user['FirstLogin']}");

            // Check if this is a first-time login (password might be default/unhashed)
            if ($user['FirstLogin']) {
                // For first-time login, check if password is hashed or plain text
                $isPasswordValid = false;

                if (preg_match('/^\$2[ayb]\$[0-9]{2}\$/', $user['Password']) && strlen($user['Password']) >= 60) {
                    // Password is already hashed, verify normally
                    $isPasswordValid = password_verify($password, $user['Password']);
                } else {
                    // Password might be plain text (default password), compare directly
                    $isPasswordValid = ($password === $user['Password']);
                }

                if ($isPasswordValid) {
                    // Regenerate session ID to prevent session fixation
                    session_regenerate_id(true);

                    $_SESSION['first_login_user_id'] = $user['LectID'];
                    $_SESSION['first_login_role'] = 'lecturer';
                    $_SESSION['login_time'] = time();

                    // Log first-time login
                    error_log("FIRST LOGIN: Lecturer ID {$user['LectID']} first login at " . date('Y-m-d H:i:s'));

                    header("Location: modules/password_reset_email.php");
                    exit();
                } else {
                    error_log("LECTURER LOGIN FAILED: Invalid password for LectID {$user['LectID']} (FirstLogin)");
                    $error = "Incorrect password for Lecturer. If this is your first login, please contact your administrator.";
                }
            } else {
                // Regular login for existing users
                if (password_verify($password, $user['Password'])) {
                    // Regenerate session ID to prevent session fixation
                    session_regenerate_id(true);

                    $_SESSION['user_id'] = $user['LectID'];
                    $_SESSION['role'] = 'lecturer';
                    $_SESSION['login_time'] = time();
                    $_SESSION['last_activity'] = time();

                    // Log successful login
                    error_log("LOGIN SUCCESS: Lecturer ID {$user['LectID']} logged in at " . date('Y-m-d H:i:s'));

                    header("Location: dashboard/lecturer.php");
                    exit();
                } else {
                    error_log("LECTURER LOGIN FAILED: Invalid password for LectID {$user['LectID']} (Regular login)");
                    $error = "Incorrect password for Lecturer.";
                }
            }
        }
        // Student Authentication
        elseif ($resultStudent->num_rows === 1) {
            $user = $resultStudent->fetch_assoc();

            // Log student login attempt for debugging
            error_log("STUDENT LOGIN ATTEMPT: UserID {$username}, StudentID {$user['StudentID']}, FirstLogin: {$user['FirstLogin']}");

            // Check if this is a first-time login (password might be default/unhashed)
            if ($user['FirstLogin']) {
                // For first-time login, check if password is hashed or plain text
                $isPasswordValid = false;

                if (preg_match('/^\$2[ayb]\$[0-9]{2}\$/', $user['Password']) && strlen($user['Password']) >= 60) {
                    // Password is already hashed, verify normally
                    $isPasswordValid = password_verify($password, $user['Password']);
                } else {
                    // Password might be plain text (default password), compare directly
                    $isPasswordValid = ($password === $user['Password']);
                }

                if ($isPasswordValid) {
                    // Regenerate session ID to prevent session fixation
                    session_regenerate_id(true);

                    $_SESSION['first_login_user_id'] = $user['StudentID'];
                    $_SESSION['first_login_role'] = 'student';
                    $_SESSION['login_time'] = time();

                    // Log first-time login
                    error_log("FIRST LOGIN: Student ID {$user['StudentID']} first login at " . date('Y-m-d H:i:s'));

                    header("Location: modules/password_reset_email.php");
                    exit();
                } else {
                    error_log("STUDENT LOGIN FAILED: Invalid password for StudentID {$user['StudentID']} (FirstLogin)");
                    $error = "Incorrect password for Student. If this is your first login, please contact your administrator.";
                }
            } else {
                // Regular login for existing users
                if (password_verify($password, $user['Password'])) {
                    // Regenerate session ID to prevent session fixation
                    session_regenerate_id(true);

                    $_SESSION['user_id'] = $user['StudentID'];
                    $_SESSION['role'] = 'student';
                    $_SESSION['login_time'] = time();
                    $_SESSION['last_activity'] = time();

                    // Log successful login
                    error_log("LOGIN SUCCESS: Student ID {$user['StudentID']} logged in at " . date('Y-m-d H:i:s'));

                    header("Location: dashboard/student.php");
                    exit();
                } else {
                    error_log("STUDENT LOGIN FAILED: Invalid password for StudentID {$user['StudentID']} (Regular login)");
                    $error = "Incorrect password for Student.";
                }
            }
        } else {
            $error = "Invalid username or password!";
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>UTHM Attendance – Sign In</title>

  <!-- Google Fonts (Inter) -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">

  <!-- FontAwesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Our new login CSS -->
  <link rel="stylesheet" href="dashboard/css/login.css">
</head>
<body>
  <div class="login-page">
    <div class="login-container">
      <div class="logo-section">
        <img src="assets/images/logo-uthm2.png" alt="UTHM Logo" class="logo-img">
      </div>
      <h2 class="login-title">Sign in to your account</h2>
      <?php if ($error): ?>
        <div class="alert-message">
          <i class="fas fa-exclamation-circle"></i>
          <span><?= htmlspecialchars($error) ?></span>
        </div>
      <?php endif; ?>
      <form method="POST" action="" class="login-form" novalidate>
        <label for="username" class="input-label">StudentID / StaffID / Admin</label>
        <div class="input-wrapper">
          <input
            type="text"
            id="username"
            name="username"
            class="text-input"
            placeholder="Enter your ID"
            required
          />
          <i class="fas fa-user icon-input"></i>
        </div>

        <label for="password" class="input-label">Password</label>
        <div class="input-wrapper">
          <input
            type="password"
            id="password"
            name="password"
            class="text-input"
            placeholder="Enter your password"
            required
          />
          <i class="fas fa-lock icon-input"></i>
          <button type="button" class="password-toggle" onclick="togglePassword()">
            <i id="toggleIcon" class="fas fa-eye-slash"></i>
          </button>
        </div>

        <button type="submit" class="btn-submit">Sign In</button>
      </form>
      <div class="help-links">
        <a href="modules/forgot_password.php" class="help-link">Forgot Password?</a>
        <span class="separator">|</span>
        <a href="modules/get_help.php" class="help-link">Get Help</a>
      </div>
    </div>
  </div>

  <script>
    function togglePassword() {
      const pwdField = document.getElementById('password');
      const toggleIcon = document.getElementById('toggleIcon');
      if (pwdField.type === 'password') {
        pwdField.type = 'text';
        toggleIcon.classList.replace('fa-eye-slash', 'fa-eye');
      } else {
        pwdField.type = 'password';
        toggleIcon.classList.replace('fa-eye', 'fa-eye-slash');
      }
    }
  </script>
</body>
</html>
