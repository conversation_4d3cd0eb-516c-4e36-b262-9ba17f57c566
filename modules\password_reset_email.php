<?php
/**
 * Email Verification Page for Password Reset
 * Shows confirmation that password reset email has been sent
 * Provides options to resend email or update email address
 */

session_start();
require '../config/config.php';
require_once __DIR__ . '/../includes/PasswordResetService.php';

// Security check: only allow access during first-time login process
if (!isset($_SESSION['first_login_user_id']) || !isset($_SESSION['first_login_role'])) {
    header("Location: ../index.php");
    exit();
}

$user_id = $_SESSION['first_login_user_id'];
$role = $_SESSION['first_login_role'];
$message = '';
$messageType = 'info';

// Log password reset email page access
error_log("PASSWORD RESET EMAIL PAGE: UserID $user_id, Role $role");

// Get user information
$table = ($role === 'student') ? 'student' : 'lecturer';
$id_field = ($role === 'student') ? 'StudentID' : 'LectID';

$stmt = $conn->prepare("SELECT Name, Email FROM $table WHERE $id_field = ?");
$stmt->bind_param("s", $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows !== 1) {
    error_log("PASSWORD RESET EMAIL ERROR: User not found - UserID $user_id, Role $role, Table $table, Field $id_field");
    header("Location: ../index.php");
    exit();
}

$user = $result->fetch_assoc();
$userName = $user['Name'];
$userEmail = $user['Email'];

$passwordResetService = new PasswordResetService($conn);

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['resend_email'])) {
        // Resend password reset email
        $result = $passwordResetService->resendPasswordResetEmail($user_id, $role);
        
        if ($result['success']) {
            $message = "Password reset email has been resent to your registered email address.";
            $messageType = 'success';
        } else {
            $message = "Failed to resend email: " . $result['message'];
            $messageType = 'error';
        }
        
    } elseif (isset($_POST['update_email'])) {
        // Update email address
        $newEmail = trim($_POST['new_email']);
        
        // Validate email
        if (!filter_var($newEmail, FILTER_VALIDATE_EMAIL)) {
            $message = "Please enter a valid email address.";
            $messageType = 'error';
        } else {
            // Update email in database
            $updateStmt = $conn->prepare("UPDATE $table SET Email = ? WHERE $id_field = ?");
            $updateStmt->bind_param("ss", $newEmail, $user_id);
            
            if ($updateStmt->execute()) {
                $userEmail = $newEmail;
                
                // Send password reset email to new address
                $result = $passwordResetService->initiatePasswordReset($user_id, $role, $newEmail, $userName);
                
                if ($result['success']) {
                    $message = "Email address updated successfully. Password reset email has been sent to your new email address.";
                    $messageType = 'success';
                } else {
                    $message = "Email address updated, but failed to send password reset email: " . $result['message'];
                    $messageType = 'warning';
                }
            } else {
                $message = "Failed to update email address. Please try again.";
                $messageType = 'error';
            }
        }
    }
}

// If no email has been sent yet, send initial password reset email
if (!isset($_POST['resend_email']) && !isset($_POST['update_email']) && empty($message)) {
    $result = $passwordResetService->initiatePasswordReset($user_id, $role, $userEmail, $userName);
    
    if ($result['success']) {
        $message = "A secure password reset link has been sent to your registered email address.";
        $messageType = 'success';
    } else {
        $message = "Failed to send password reset email: " . $result['message'];
        $messageType = 'error';
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset - UTHM Attendance System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 1.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
        }
        
        .form-control {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem 1.5rem;
        }
        
        .email-display {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            border-left: 4px solid #667eea;
        }
        
        .icon-large {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            font-weight: bold;
        }
        
        .step.active {
            background: #28a745;
        }
        
        .step-line {
            width: 50px;
            height: 2px;
            background: #dee2e6;
            margin-top: 19px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .card {
                margin: 1rem 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-md-8 col-lg-6">
                <div class="card">
                    <div class="card-header text-center">
                        <h4 class="mb-0">
                            <i class="fas fa-envelope me-2"></i>
                            Password Reset Email Sent
                        </h4>
                    </div>
                    <div class="card-body p-4">
                        <!-- Step Indicator -->
                        <div class="step-indicator">
                            <div class="step active">1</div>
                            <div class="step-line"></div>
                            <div class="step">2</div>
                            <div class="step-line"></div>
                            <div class="step">3</div>
                        </div>
                        
                        <div class="text-center mb-4">
                            <i class="fas fa-paper-plane icon-large"></i>
                            <h5>Check Your Email</h5>
                            <p class="text-muted">We've sent a secure password reset link to your email address.</p>
                        </div>

                        <?php if (!empty($message)): ?>
                            <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : ($messageType === 'warning' ? 'warning' : 'danger'); ?> mb-4">
                                <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : ($messageType === 'warning' ? 'exclamation-triangle' : 'exclamation-circle'); ?> me-2"></i>
                                <?php echo htmlspecialchars($message); ?>
                            </div>
                        <?php endif; ?>

                        <!-- Current Email Display -->
                        <div class="email-display mb-4">
                            <h6 class="mb-2">
                                <i class="fas fa-envelope me-2"></i>
                                Email Address:
                            </h6>
                            <strong><?php echo htmlspecialchars($userEmail); ?></strong>
                        </div>

                        <!-- Instructions -->
                        <div class="mb-4">
                            <h6>Next Steps:</h6>
                            <ol class="ps-3">
                                <li>Check your email inbox (and spam folder)</li>
                                <li>Click the password reset link in the email</li>
                                <li>Create your new secure password</li>
                                <li>Log in with your new password</li>
                            </ol>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row g-3">
                            <div class="col-md-6">
                                <form method="POST" class="d-grid">
                                    <button type="submit" name="resend_email" class="btn btn-primary">
                                        <i class="fas fa-redo me-2"></i>
                                        Resend Email
                                    </button>
                                </form>
                            </div>
                            <div class="col-md-6">
                                <button type="button" class="btn btn-secondary w-100" data-bs-toggle="modal" data-bs-target="#updateEmailModal">
                                    <i class="fas fa-edit me-2"></i>
                                    Update Email
                                </button>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <a href="../index.php" class="text-decoration-none">
                                <i class="fas fa-arrow-left me-2"></i>
                                Back to Login
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Update Email Modal -->
    <div class="modal fade" id="updateEmailModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>
                        Update Email Address
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="new_email" class="form-label">New Email Address:</label>
                            <input type="email" class="form-control" id="new_email" name="new_email" 
                                   value="<?php echo htmlspecialchars($userEmail); ?>" required>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                A new password reset email will be sent to this address.
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="update_email" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Update & Send Email
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
