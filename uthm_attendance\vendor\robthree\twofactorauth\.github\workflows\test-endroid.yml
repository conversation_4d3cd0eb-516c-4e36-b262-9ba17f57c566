name: Test Endroid QR Code Provider

on:
  push:
  pull_request:

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-version: ['8.2', '8.3']
        endroid-version: ["^3","^4","^5","^6"]

    steps:
    - uses: actions/checkout@v4

    - uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php-version }}
        tools: composer
        coverage: xdebug
        ini-values: error_reporting=E_ALL

    - uses: ramsey/composer-install@v3

    - run: composer require endroid/qrcode:${{ matrix.endroid-version }} -W

    - run: composer lint-ci
    - run: composer test testsDependency/EndroidQRCodeTest.php
