<?php
/**
 * Secure Password Reset Form
 * Handles token-based password reset from email links
 */

session_start();
require '../config/config.php';
require_once __DIR__ . '/../includes/PasswordResetService.php';

// Get token from URL and clean it
$rawToken = $_GET['token'] ?? '';
$token = trim(urldecode($rawToken)); // Properly decode URL-encoded token
$message = '';
$messageType = 'error';
$tokenValid = false;
$tokenData = null;



try {
    $passwordResetService = new PasswordResetService($conn);

    // Log token validation attempt
    error_log("PASSWORD RESET FORM: Token validation attempt - Token: " . substr($token, 0, 10) . "...");

    // Validate token
    if (!empty($token)) {

        // Check if table exists first
        $tableCheck = $conn->query("SHOW TABLES LIKE 'password_reset_tokens'");
        if ($tableCheck->num_rows === 0) {
            $message = "Password reset system is not properly configured. Please contact your administrator.";
            error_log("PASSWORD RESET FORM ERROR: password_reset_tokens table does not exist");
        } else {

            $tokenData = $passwordResetService->validateToken($token);
            if ($tokenData) {
                $tokenValid = true;
                error_log("PASSWORD RESET FORM: Token valid for user {$tokenData['user_id']} role {$tokenData['user_role']}");
            } else {
                $message = "Invalid or expired password reset link. Please request a new password reset.";
                error_log("PASSWORD RESET FORM ERROR: Token validation failed for token: " . substr($token, 0, 10) . "...");
            }
        }
    } else {
        $message = "No password reset token provided.";
        error_log("PASSWORD RESET FORM ERROR: No token provided in URL");
    }

} catch (Exception $e) {
    $message = "System error: " . $e->getMessage();
    error_log("Password reset form error: " . $e->getMessage());
}

// Handle password reset form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $tokenValid) {
    $newPassword = $_POST['new_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    if (empty($newPassword) || empty($confirmPassword)) {
        $message = "Please fill in both password fields.";
        $messageType = 'error';
    } elseif ($newPassword !== $confirmPassword) {
        $message = "Passwords do not match. Please try again.";
        $messageType = 'error';
    } else {
        // Reset password using the service
        $resetResult = $passwordResetService->resetPassword($token, $newPassword);
        
        if ($resetResult['success']) {
            $message = "Password reset successfully! You can now log in with your new password.";
            $messageType = 'success';
            $tokenValid = false; // Hide form after successful reset
            
            // Clear any first-login session data
            unset($_SESSION['first_login_user_id'], $_SESSION['first_login_role']);
            
        } else {
            $message = $resetResult['message'];
            $messageType = 'error';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - UTHM Attendance</title>
    
    <!-- ─── GLOBAL CSS (same as other dashboards) ─── -->
    <link rel="stylesheet" href="../dashboard/css/base-styles.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-header.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-sidebar.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-footer.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-dashboard-styles.css" />
    
    <!-- ─── BOOTSTRAP & ICONS ─── -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .reset-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }
        
        .reset-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
        }
        
        .reset-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .reset-body {
            padding: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-reset {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-reset:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
        }
        
        .password-requirements {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 0.5rem;
            font-size: 0.875rem;
        }
        
        .requirement {
            display: flex;
            align-items: center;
            margin-bottom: 0.25rem;
        }
        
        .requirement i {
            margin-right: 0.5rem;
            width: 16px;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 1.5rem;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #dee2e6;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            font-weight: bold;
        }
        
        .step.active {
            background: #28a745;
            color: white;
        }
        
        .step.completed {
            background: #667eea;
            color: white;
        }
        
        .step-line {
            width: 50px;
            height: 2px;
            background: #dee2e6;
            margin-top: 19px;
        }
        
        .step-line.completed {
            background: #667eea;
        }
        
        @media (max-width: 768px) {
            .reset-container {
                padding: 1rem;
            }
            
            .reset-header, .reset-body {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-card">
            <div class="reset-header">
                <i class="fas fa-key fa-2x mb-3"></i>
                <h3 class="mb-0">Reset Your Password</h3>
                <p class="mb-0 mt-2 opacity-75">Create a new secure password for your account</p>
            </div>
            
            <div class="reset-body">
                <!-- Step Indicator -->
                <div class="step-indicator">
                    <div class="step completed">1</div>
                    <div class="step-line completed"></div>
                    <div class="step completed">2</div>
                    <div class="step-line completed"></div>
                    <div class="step active">3</div>
                </div>
                
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?>">
                        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($tokenValid): ?>
                    <form method="POST" id="resetForm">
                        <div class="form-group">
                            <label for="new_password" class="form-label">
                                <i class="fas fa-lock me-2"></i>New Password
                            </label>
                            <input type="password" class="form-control" id="new_password" name="new_password" 
                                   required minlength="8" autocomplete="new-password">
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm_password" class="form-label">
                                <i class="fas fa-lock me-2"></i>Confirm New Password
                            </label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                   required minlength="8" autocomplete="new-password">
                        </div>
                        
                        <div class="password-requirements">
                            <h6 class="mb-2">
                                <i class="fas fa-shield-alt me-2"></i>Password Requirements:
                            </h6>
                            <div class="requirement">
                                <i class="fas fa-check text-success"></i>
                                At least 8 characters long
                            </div>
                            <div class="requirement">
                                <i class="fas fa-check text-success"></i>
                                Mix of uppercase and lowercase letters
                            </div>
                            <div class="requirement">
                                <i class="fas fa-check text-success"></i>
                                At least one number
                            </div>
                            <div class="requirement">
                                <i class="fas fa-check text-success"></i>
                                At least one special character
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-reset mt-3">
                            <i class="fas fa-save me-2"></i>Reset Password
                        </button>
                    </form>
                <?php else: ?>
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h5>Unable to Reset Password</h5>
                        <p class="text-muted mb-4">The password reset link is invalid or has expired.</p>
                        <a href="../index.php" class="btn btn-reset">
                            <i class="fas fa-arrow-left me-2"></i>Back to Login
                        </a>
                    </div>
                <?php endif; ?>
                
                <?php if ($messageType === 'success'): ?>
                    <div class="text-center mt-4">
                        <a href="../index.php" class="btn btn-reset">
                            <i class="fas fa-sign-in-alt me-2"></i>Go to Login
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function togglePassword(inputId, icon) {
            const input = document.getElementById(inputId);
            const isVisible = input.type === 'text';

            input.type = isVisible ? 'password' : 'text';
            icon.classList.toggle('fa-eye');
            icon.classList.toggle('fa-eye-slash');
        }

        // Password strength validation
        const passwordInput = document.getElementById('new_password');
        const confirmInput = document.getElementById('confirm_password');
        const submitBtn = document.getElementById('submitBtn');

        if (passwordInput) {
            passwordInput.addEventListener('input', validatePassword);
            confirmInput.addEventListener('input', validateConfirmPassword);
        }

        function validatePassword() {
            const password = passwordInput.value;
            const requirements = {
                length: password.length >= 8,
                upper: /[A-Z]/.test(password),
                lower: /[a-z]/.test(password),
                number: /[0-9]/.test(password),
                special: /[^a-zA-Z0-9]/.test(password)
            };

            // Update requirement indicators
            Object.keys(requirements).forEach(req => {
                const element = document.getElementById(`req-${req}`);
                if (element) {
                    if (requirements[req]) {
                        element.className = 'fas fa-check text-success';
                    } else {
                        element.className = 'fas fa-times text-danger';
                    }
                }
            });

            validateConfirmPassword();
            updateSubmitButton();
        }

        function validateConfirmPassword() {
            const password = passwordInput.value;
            const confirm = confirmInput.value;

            if (confirm.length === 0) {
                confirmInput.classList.remove('is-invalid', 'is-valid');
                return;
            }

            if (password === confirm) {
                confirmInput.classList.remove('is-invalid');
                confirmInput.classList.add('is-valid');
            } else {
                confirmInput.classList.remove('is-valid');
                confirmInput.classList.add('is-invalid');
            }

            updateSubmitButton();
        }

        function updateSubmitButton() {
            const password = passwordInput.value;
            const confirm = confirmInput.value;

            const requirements = {
                length: password.length >= 8,
                upper: /[A-Z]/.test(password),
                lower: /[a-z]/.test(password),
                number: /[0-9]/.test(password),
                special: /[^a-zA-Z0-9]/.test(password)
            };

            const allRequirementsMet = Object.values(requirements).every(Boolean);
            const passwordsMatch = password === confirm && confirm.length > 0;

            if (submitBtn) {
                submitBtn.disabled = !(allRequirementsMet && passwordsMatch);
            }
        }

        // Form submission validation
        document.getElementById('resetForm')?.addEventListener('submit', function(e) {
            const password = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;

            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Passwords do not match. Please try again.');
                return false;
            }

            if (password.length < 8) {
                e.preventDefault();
                alert('Password must be at least 8 characters long.');
                return false;
            }
        });
    </script>
</body>
</html>
