<?php
// Simple test QR generation for debugging
session_start();

// Generate test QR data
$course_id = 1;
$lecturer_id = 1;
$assignment_id = 1;
$section = 'A';
$token = bin2hex(random_bytes(8));
$expires_at = time() + 30; // 30 seconds from now (realistic test)

$qr_data = http_build_query([
    'course_id' => $course_id,
    'lecturer_id' => $lecturer_id,
    'assignment_id' => $assignment_id,
    'section' => $section,
    'token' => $token,
    'expires_at' => $expires_at
]);

echo "<h2>Test QR Data</h2>";
echo "<p><strong>QR Data String:</strong></p>";
echo "<textarea style='width: 100%; height: 100px;'>" . htmlspecialchars($qr_data) . "</textarea>";

echo "<p><strong>Parsed Data:</strong></p>";
echo "<ul>";
echo "<li>Course ID: " . $course_id . "</li>";
echo "<li>Lecturer ID: " . $lecturer_id . "</li>";
echo "<li>Assignment ID: " . $assignment_id . "</li>";
echo "<li>Section: " . $section . "</li>";
echo "<li>Token: " . $token . "</li>";
echo "<li>Expires At: " . date('Y-m-d H:i:s', $expires_at) . " (Current: " . date('Y-m-d H:i:s') . ")</li>";
echo "</ul>";

echo "<p><strong>Copy this QR data and paste it into the manual QR input on the scan page:</strong></p>";
echo "<input type='text' value='" . htmlspecialchars($qr_data) . "' style='width: 100%;' onclick='this.select();'>";

echo "<p><a href='modules/qr_scan.php'>Go to QR Scan Page</a></p>";
?>
