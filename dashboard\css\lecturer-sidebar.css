/* ===================================================================
   LECTURER SIDEBAR STYLES
   Professional sidebar navigation for lecturer dashboard pages
   ================================================================= */

/* CSS Custom Properties for Sidebar */
:root {
  --sidebar-bg: #ffffff;
  --sidebar-border: #e2e8f0;
  --sidebar-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --sidebar-width: 20%;
  --sidebar-min-width: 280px;
  
  --profile-bg: #ffffff;
  --profile-border: #e2e8f0;
  --profile-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --profile-padding: 1.5rem;
  
  --menu-link-color: #64748b;
  --menu-link-hover-bg: #f8fafc;
  --menu-link-hover-color: #1e293b;
  --menu-link-active-bg: #6366f1;
  --menu-link-active-color: #ffffff;
  
  --profile-pic-border: #6366f1;
  --profile-name-color: #1e293b;
  --profile-id-color: #64748b;
  
  --transition-smooth: all 0.3s ease;
  --border-radius: 0.75rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
}

/* Sidebar Container */
.sidebar {
  width: var(--sidebar-width);
  min-width: var(--sidebar-min-width);
  background: var(--sidebar-bg);
  border-right: 1px solid var(--sidebar-border);
  box-shadow: var(--sidebar-shadow);
  padding: var(--spacing-lg);
  overflow-y: auto;
  height: calc(100vh - 70px);
  position: relative;
}

/* Profile Section */
.profile {
  background: var(--profile-bg);
  border-radius: var(--border-radius);
  padding: var(--profile-padding);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--profile-border);
  text-align: center;
  box-shadow: var(--profile-shadow);
}

/* Profile Picture */
.profile-pic {
  border-radius: 50%;
  width: 100px;
  height: 100px;
  margin-bottom: var(--spacing-sm);
  border: 3px solid var(--profile-pic-border);
  box-shadow: var(--profile-shadow);
  transition: var(--transition-smooth);
  object-fit: cover;
}

.profile-pic:hover {
  transform: scale(1.05);
}

/* Profile Name */
.profile-name {
  color: var(--profile-name-color);
  font-size: 1rem;
  font-weight: 600;
  margin: var(--spacing-sm) 0 0.25rem 0;
  line-height: 1.2;
}

/* Profile ID */
.profile-id {
  color: var(--profile-id-color);
  font-size: 0.875rem;
  margin: 0;
  font-weight: 400;
}

/* Navigation Menu */
.menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.menu li {
  margin-bottom: 0.5rem;
}

/* Menu Links */
.menu li a {
  color: var(--menu-link-color);
  background: transparent;
  border: none;
  border-radius: var(--border-radius);
  transition: var(--transition-smooth);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  font-weight: 500;
  text-decoration: none;
  font-size: 0.875rem;
}

.menu li a:hover {
  background: var(--menu-link-hover-bg);
  color: var(--menu-link-hover-color);
  transform: translateX(2px);
}

.menu li a.active {
  background: var(--menu-link-active-bg);
  color: var(--menu-link-active-color);
  box-shadow: var(--profile-shadow);
}

/* Menu Icons */
.menu li a i {
  width: 18px;
  text-align: center;
  font-size: 1rem;
  flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar {
    width: 25%;
    min-width: 250px;
    padding: var(--spacing-md);
  }
  
  .profile {
    padding: var(--spacing-sm);
  }
  
  .profile-pic {
    width: 80px;
    height: 80px;
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 280px;
    position: fixed;
    left: -280px;
    top: 70px;
    height: calc(100vh - 70px);
    z-index: 1000;
    transition: left 0.3s ease;
    border-right: none;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  }
  
  .sidebar.open {
    left: 0;
  }
  
  .profile {
    padding: var(--spacing-sm);
  }
  
  .profile-pic {
    width: 70px;
    height: 70px;
  }
  
  .profile-name {
    font-size: 0.9rem;
  }
  
  .profile-id {
    font-size: 0.8rem;
  }
  
  .menu li a {
    padding: 0.75rem var(--spacing-sm);
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .sidebar {
    width: 260px;
    left: -260px;
  }
  
  .profile-pic {
    width: 60px;
    height: 60px;
  }
  
  .menu li a {
    padding: 0.5rem 0.75rem;
  }
}

/* Mobile Menu Toggle (if needed) */
.sidebar-toggle {
  display: none;
  background: var(--menu-link-active-bg);
  color: white;
  border: none;
  padding: 0.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 1rem;
}

@media (max-width: 768px) {
  .sidebar-toggle {
    display: block;
  }
}
