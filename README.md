# 🎓 UTHM Attendance System
## Complete Deployment and Setup Guide

A comprehensive web-based attendance management system for Universiti Tun Hussein <PERSON> Malaysia (UTHM) featuring QR code scanning, blockchain integration, and multi-role user management.

---

## 📋 Table of Contents

1. [System Requirements & Prerequisites](#-system-requirements--prerequisites)
2. [Installation Steps](#-installation-steps)
3. [Database Configuration](#-database-configuration)
4. [Application Configuration](#-application-configuration)
5. [Initial System Setup](#-initial-system-setup)
6. [Feature-Specific Setup](#-feature-specific-setup)
7. [Testing & Verification](#-testing--verification)
8. [Production Deployment](#-production-deployment)
9. [User Guide Overview](#-user-guide-overview)
10. [Support & Troubleshooting](#-support--troubleshooting)

---

## 🔧 System Requirements & Prerequisites

### Required Software
- **PHP**: Version 7.4 or higher (8.0+ recommended)
- **MySQL**: Version 5.7 or higher (8.0+ recommended)
- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **Composer**: Latest version for PHP dependency management

### Operating System Compatibility
- ✅ Windows 10/11 (XAMPP/WAMP)
- ✅ macOS (MAMP/Homebrew)
- ✅ Linux (Ubuntu 18.04+, CentOS 7+)

### Hardware Requirements
- **RAM**: Minimum 2GB (4GB+ recommended)
- **Storage**: 500MB free space
- **Network**: Internet connection for email functionality

### Browser Compatibility
- Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- Mobile browsers supported for QR code scanning

### PHP Extensions Required
```bash
php-mysqli, php-gd, php-curl, php-json, php-mbstring, php-openssl
```

---

## 🚀 Installation Steps

### Step 1: Download/Clone the Project

#### Option A: Direct Download
1. Download the project ZIP file
2. Extract to your web server directory:
   - **XAMPP**: `C:\xampp\htdocs\uthm_attendance`
   - **WAMP**: `C:\wamp64\www\uthm_attendance`
   - **Linux**: `/var/www/html/uthm_attendance`

#### Option B: Git Clone
```bash
cd /path/to/webserver/root
git clone [repository-url] uthm_attendance
cd uthm_attendance
```

### Step 2: Set Up Web Server Environment

#### For XAMPP (Windows/Mac/Linux)
1. Download and install [XAMPP](https://www.apachefriends.org/)
2. Start Apache and MySQL services
3. Verify PHP version: `http://localhost/dashboard/`

#### For WAMP (Windows)
1. Download and install [WAMP](https://www.wampserver.com/)
2. Start all services
3. Ensure PHP extensions are enabled

#### For Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install apache2 mysql-server php php-mysqli php-gd php-curl
sudo systemctl start apache2 mysql
```

### Step 3: Install PHP Dependencies
```bash
cd uthm_attendance
composer install
```

If Composer is not installed:
```bash
# Download and install Composer
curl -sS https://getcomposer.org/installer | php
php composer.phar install
```

### Step 4: Directory Structure Overview
```
uthm_attendance/
├── config/              # Configuration files
├── dashboard/           # Main dashboard pages
├── modules/             # Feature modules
├── database/            # SQL schema files
├── blockchain/          # Blockchain integration
├── vendor/              # Composer dependencies
├── uploads/             # File uploads directory
├── assets/              # Static assets (CSS, JS, images)
└── includes/            # Shared PHP includes
```

---

## 🗄️ Database Configuration

### Step 1: Create MySQL Database

#### Using phpMyAdmin
1. Open `http://localhost/phpmyadmin`
2. Click "New" to create database
3. Database name: `uthm_attendance` (or your preferred name)
4. Collation: `utf8mb4_general_ci`

#### Using MySQL Command Line
```sql
CREATE DATABASE uthm_attendance CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
CREATE USER 'uthm_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON uthm_attendance.* TO 'uthm_user'@'localhost';
FLUSH PRIVILEGES;
```

### Step 2: Import Database Schema
1. In phpMyAdmin, select your database
2. Go to "Import" tab
3. Choose file: `database/uthm_attendance (6).sql`
4. Click "Go" to import

### Step 3: Configure Database Connection
Edit `config/config.php`:
```php
$servername = "localhost";
$username = "your_db_username";    // Change this
$password = "your_db_password";    // Change this
$dbname = "uthm_attendance";       // Change if different
```

### Step 4: Set Up System Settings
Import additional configuration:
```sql
-- Import system settings
SOURCE database/system_settings.sql;
```

---

## ⚙️ Application Configuration

### Step 1: Email/SMTP Configuration
Edit `config/config.php` for email functionality:

#### Gmail Configuration (Recommended)
```php
define('SMTP_HOST',       'smtp.gmail.com');
define('SMTP_PORT',        587);
define('SMTP_USER',       '<EMAIL>');        // Your Gmail
define('SMTP_PASS',       'your-app-password');           // Gmail App Password
define('SMTP_ENCRYPTION', 'tls');
```

**Gmail Setup Steps:**
1. Enable 2-Factor Authentication
2. Generate App Password: Google Account → Security → App passwords
3. Use the 16-character app password (not your regular password)

#### Alternative Email Providers
```php
// Outlook
define('SMTP_HOST', 'smtp-mail.outlook.com');

// Yahoo
define('SMTP_HOST', 'smtp.mail.yahoo.com');
```

### Step 2: File Upload Permissions
```bash
# Linux/Mac
chmod 755 uploads/
chmod 644 uploads/*

# Windows (via Properties → Security)
# Ensure IIS_IUSRS has write permissions to uploads folder
```

### Step 3: Timezone Configuration
Already configured in `config/config.php`:
```php
date_default_timezone_set('Asia/Kuala_Lumpur');
$conn->query("SET time_zone = '+08:00'");
```

### Step 4: Security Settings
Verify session configuration in `config/config.php`:
- Session timeout: 30 minutes
- Secure session handling enabled
- CSRF protection implemented

---

## 🎯 Initial System Setup

### Step 1: Default Admin Account
**Default Credentials:**
- Username: `admin1`
- Password: `Admin123!`
- Email: `<EMAIL>`

⚠️ **Important**: Change the default password immediately after first login!

### Step 2: Access the System
1. Open browser: `http://localhost/uthm_attendance`
2. Login with admin credentials
3. You'll be redirected to admin dashboard

### Step 3: Create First Lecturer Account
1. Go to Admin Dashboard → Manage Users
2. Click "Add New Lecturer"
3. Fill required information:
   - Staff ID: Auto-generated (00001, 00002, etc.)
   - Name, Email, Faculty
   - IC Number (Malaysian format: YYMMDD-PB-GGGG)
   - Phone Number (Malaysian format)

### Step 4: Create First Student Account
1. Go to Admin Dashboard → Manage Users
2. Click "Add New Student"
3. Fill required information:
   - Matric Number: Auto-generated (AI250001, AI250002, etc.)
   - Name, Email, Faculty
   - IC Number and Phone Number

### Step 5: Set Up Courses
1. Go to Admin Dashboard → Manage Courses
2. Click "Add New Course"
3. Create course with sections:
   - Course Code (e.g., BITC3013)
   - Course Name
   - Sections with assigned lecturers

---

## 🔧 Feature-Specific Setup

### Two-Factor Authentication (2FA)
1. Install Google Authenticator app on mobile device
2. Login as lecturer/student
3. Visit: `http://localhost/uthm_attendance/setup_2fa.php`
4. Scan QR code with authenticator app

### QR Code Generation
**Dependencies already included:**
- PHP QR Code library in `phpqrcode/`
- GD extension for image generation

**Test QR functionality:**
```php
// Visit: modules/qr_generate.php
// Verify QR codes generate properly
```

### Email Notification System
**Test email configuration:**
1. Go to: `modules/test_email_config.php` (if available)
2. Send test email to verify SMTP settings
3. Check email delivery and formatting

### Blockchain Integration (Optional)
**Requirements:**
- Node.js 16+
- Ganache CLI or Ganache GUI
- Truffle framework

**Setup Steps:**
```bash
cd blockchain/
npm install
npm install -g truffle ganache-cli

# Start Ganache
ganache-cli -p 7545

# Deploy contracts
truffle migrate --network development
```

---

## ✅ Testing & Verification

### Step 1: Basic Functionality Test
1. **Admin Login**: Verify admin dashboard access
2. **User Management**: Create test lecturer and student
3. **Course Management**: Create test course with sections
4. **Course Registration**: Test student course registration

### Step 2: Attendance System Test
1. **QR Generation**: Lecturer generates QR code
2. **QR Scanning**: Student scans QR code
3. **Attendance Recording**: Verify attendance is recorded
4. **Reports**: Check attendance reports generation

### Step 3: Email System Test
1. **Warning Letters**: Generate warning letter for absent student
2. **Email Delivery**: Verify email is sent and received
3. **PDF Generation**: Check PDF attachment quality

### Step 4: Security Test
1. **Session Management**: Test session timeout
2. **Password Security**: Verify password hashing
3. **Access Control**: Test role-based permissions

### Common Test Accounts
```
Admin:
- Username: admin1
- Password: Admin123!

Test Lecturer:
- Staff ID: 00001
- Password: (set during creation)

Test Student:
- Matric No: AI250001
- Password: (set during creation)
```

---

## 🌐 Production Deployment Considerations

### Security Hardening
1. **Change Default Passwords**: All default accounts
2. **Update config.php**: Remove debug settings
3. **File Permissions**: Restrict sensitive files
4. **SSL Certificate**: Enable HTTPS
5. **Database Security**: Use strong passwords

### Performance Optimization
```php
// Enable PHP OPcache
opcache.enable=1
opcache.memory_consumption=128
opcache.max_accelerated_files=4000
```

### Backup Procedures
```bash
# Database backup
mysqldump -u username -p uthm_attendance > backup_$(date +%Y%m%d).sql

# File backup
tar -czf uthm_backup_$(date +%Y%m%d).tar.gz /path/to/uthm_attendance
```

### Maintenance Tasks
- **Daily**: Monitor error logs
- **Weekly**: Database optimization
- **Monthly**: Security updates
- **Quarterly**: Full system backup

---

## 👥 User Guide Overview

### Admin Role
- **User Management**: Create/edit lecturers and students
- **Course Management**: Set up courses and sections
- **System Settings**: Configure global settings
- **Reports**: View system-wide attendance reports

### Lecturer Role
- **QR Generation**: Create attendance QR codes
- **Attendance Reports**: View class attendance
- **Warning Letters**: Send absence notifications
- **Profile Management**: Update personal information

### Student Role
- **QR Scanning**: Mark attendance via QR codes
- **Course Registration**: Enroll in available courses
- **Attendance History**: View personal attendance records
- **Profile Management**: Update personal information

### Key Features
- **Dynamic QR Codes**: 20-second auto-refresh with unique tokens
- **Blockchain Integration**: Immutable attendance records
- **Email Notifications**: Automated warning letters
- **Multi-Section Support**: Course sections with different lecturers
- **Mobile Responsive**: Works on all devices

---

## 🆘 Support & Troubleshooting

### Common Issues & Solutions

#### Database Connection Failed
**Symptoms**: "Connection failed" error on login
**Solutions**:
1. Verify MySQL service is running
2. Check database credentials in `config/config.php`
3. Ensure database exists and is accessible

#### Email Sending Failed
**Symptoms**: "Invalid address" or "Authentication failed"
**Solutions**:
1. Verify SMTP credentials in `config/config.php`
2. Enable 2FA and use App Password for Gmail
3. Check firewall settings for SMTP ports

#### QR Code Not Generating
**Symptoms**: Blank QR code or error message
**Solutions**:
1. Verify GD extension is installed: `php -m | grep -i gd`
2. Check file permissions on `uploads/` directory
3. Ensure PHP QR Code library is present

#### Session Timeout Issues
**Symptoms**: Frequent logouts or "Session expired"
**Solutions**:
1. Check PHP session configuration
2. Verify session file permissions
3. Increase session timeout in `php.ini`

### Log File Locations
- **PHP Errors**: `/var/log/apache2/error.log` (Linux) or XAMPP logs
- **Application Logs**: Check PHP error_log() outputs
- **Email Logs**: Configured in `config/config.php`

### FAQ

**Q: Can I change the database name?**
A: Yes, update `config/config.php` and import schema to new database.

**Q: How do I reset admin password?**
A: Update directly in database or use password reset functionality.

**Q: Is blockchain integration required?**
A: No, the system works without blockchain. It's an optional feature.

**Q: Can I customize the email templates?**
A: Yes, edit templates in `includes/EmailService.php`.

### Contact & Support
- **System Documentation**: Check `/database/` folder for detailed guides
- **Email Setup**: See `EMAIL_SETUP_GUIDE.md`
- **Security**: Review `ADMIN_PASSWORD_SECURITY_README.md`

---

## 📄 License & Credits

**UTHM Attendance System**
- Developed for Universiti Tun Hussein Onn Malaysia
- Built with PHP, MySQL, and modern web technologies
- Includes blockchain integration capabilities

**Third-Party Libraries:**
- PHPMailer for email functionality
- PHP QR Code for QR generation
- TwoFactorAuth for 2FA implementation
- Web3.js for blockchain integration

---

---

## 🔍 Detailed Setup Instructions

### Environment Detection & Configuration

The system automatically detects the environment and adjusts settings accordingly. For production deployment:

#### Production Database Configuration
```php
// config/config.php - Production Settings
$servername = "your-production-host";
$username = "ai220185";           // Production username
$password = "ai220185";           // Production password
$dbname = "ai220185";             // Production database name

// Disable debug mode for production
define('EMAIL_DEBUG_MODE', false);
```

#### File Structure Verification
Ensure all required directories exist with proper permissions:
```bash
# Required directories
uploads/                 # File uploads (755 permissions)
phpqrcode/cache/        # QR code cache (755 permissions)
vendor/                 # Composer dependencies
blockchain/node_modules/ # Node.js dependencies (if using blockchain)
```

### Advanced Configuration Options

#### Session Security Settings
```php
// Automatic session timeout: 30 minutes
// Session regeneration on login
// Secure cookie settings for HTTPS
```

#### Password Security
- Admin passwords: Automatic bcrypt hashing
- User passwords: Secure hash with salt
- First-time login: Forced password reset

#### Email Configuration Templates
```php
// Professional email templates included
// Automatic retry mechanism (3 attempts)
// HTML and plain text versions
// Attachment support for warning letters
```

### Blockchain Integration Details

#### Prerequisites for Blockchain Features
```bash
# Install Node.js and npm
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Truffle globally
npm install -g truffle

# Install Ganache CLI
npm install -g ganache-cli
```

#### Smart Contract Deployment
```bash
cd blockchain/
npm install

# Start local blockchain
ganache-cli -p 7545 -h 0.0.0.0

# Deploy contracts
truffle compile
truffle migrate --network development
```

#### Blockchain Configuration
```javascript
// blockchain/truffle-config.js
networks: {
  development: {
    host: "127.0.0.1",
    port: 7545,
    network_id: "*"
  }
}
```

### System Features Deep Dive

#### QR Code System
- **Dynamic Generation**: 20-second refresh intervals
- **Unique Tokens**: Cryptographically secure
- **Expiration**: Automatic cleanup of expired tokens
- **Security**: Server-side validation prevents replay attacks

#### Multi-Role Authentication
- **Admin**: Full system control and user management
- **Lecturer**: Course management and attendance tracking
- **Student**: Course registration and attendance marking

#### Course Management System
- **Multi-Section Support**: Courses can have multiple sections
- **Lecturer Assignment**: Flexible lecturer-section relationships
- **Registration Control**: Global enable/disable functionality
- **Enrollment Limits**: Section-based capacity management

#### Attendance Tracking
- **Real-time Recording**: Immediate attendance confirmation
- **Duplicate Prevention**: UserID-based duplicate detection
- **Blockchain Integration**: Optional immutable record keeping
- **Report Generation**: Comprehensive attendance analytics

### Production Deployment Checklist

#### Pre-Deployment
- [ ] Database credentials updated
- [ ] Email SMTP configured and tested
- [ ] File permissions set correctly
- [ ] SSL certificate installed
- [ ] Default passwords changed
- [ ] Debug mode disabled

#### Security Hardening
- [ ] Remove development files
- [ ] Restrict database access
- [ ] Enable firewall rules
- [ ] Configure backup procedures
- [ ] Set up monitoring

#### Performance Optimization
- [ ] Enable PHP OPcache
- [ ] Configure MySQL optimization
- [ ] Set up CDN for static assets
- [ ] Implement caching strategies

### Maintenance & Monitoring

#### Regular Maintenance Tasks
```bash
# Weekly database optimization
mysql -u username -p -e "OPTIMIZE TABLE attendance_report, student, lecturer, course;"

# Monthly log rotation
logrotate /etc/logrotate.d/uthm-attendance

# Quarterly security updates
composer update
npm audit fix
```

#### Monitoring Setup
- **Error Logging**: PHP error logs and application logs
- **Performance Monitoring**: Database query optimization
- **Security Monitoring**: Failed login attempts tracking
- **Backup Verification**: Regular backup integrity checks

#### Troubleshooting Commands
```bash
# Check PHP configuration
php -i | grep -E "(session|mysqli|gd)"

# Test database connection
mysql -u username -p -e "SELECT 1;"

# Verify file permissions
ls -la uploads/ phpqrcode/cache/

# Check Apache/Nginx logs
tail -f /var/log/apache2/error.log
```

### Integration with External Systems

#### LDAP Integration (Optional)
The system can be extended to integrate with university LDAP systems for user authentication.

#### API Endpoints
RESTful API endpoints available for:
- Student details retrieval
- Attendance data export
- Course information access

#### Mobile App Integration
QR scanning functionality designed for mobile web browsers and can be integrated with native mobile applications.

---

## 📊 System Architecture

### Database Schema Overview
- **Users**: Admin, Lecturer, Student tables with role-based access
- **Courses**: Course, Section, Registration relationship management
- **Attendance**: Real-time attendance tracking with blockchain integration
- **System**: Configuration settings and security tokens

### Security Architecture
- **Authentication**: Multi-factor authentication with 2FA support
- **Authorization**: Role-based access control (RBAC)
- **Data Protection**: Encrypted passwords and secure session management
- **Audit Trail**: Comprehensive logging for security monitoring

### Technology Stack
- **Backend**: PHP 7.4+ with MySQL 8.0+
- **Frontend**: Responsive HTML5/CSS3/JavaScript
- **Email**: PHPMailer with SMTP support
- **QR Codes**: PHP QR Code library with GD extension
- **Blockchain**: Solidity smart contracts with Web3.js
- **Security**: bcrypt password hashing, CSRF protection

---

*For additional support or customization requests, please refer to the documentation files in the `/database/` directory or contact your system administrator.*
