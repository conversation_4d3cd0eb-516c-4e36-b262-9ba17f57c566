/* auth-pages.css */
body {
  background: #f7f8fa;
  font-family: 'Inter', Arial, sans-serif;
  min-height: 100vh;
  margin: 0;
  color: #222;
}
.centered-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}
.auth-card {
  background: #fff;
  padding: 2.5rem 2.5rem 2rem 2.5rem;
  border-radius: 18px;
  box-shadow: 0 4px 32px 0 rgba(27, 46, 108, 0.07);
  width: 100%;
  max-width: 410px;
  text-align: center;
  margin: 1.5rem 0;
}
.auth-card .uthm-logo {
  max-width: 120px;
  margin-bottom: 1.1rem;
}
.auth-card h2 {
  margin-bottom: 0.3em;
  font-weight: 700;
  font-size: 1.4rem;
  color: #222c4e;
}
.auth-card p {
  margin-bottom: 1.5rem;
  color: #3a3a3a;
  font-size: 1rem;
}
.input-group {
  margin-bottom: 1.3rem;
  text-align: left;
}
.input-label {
  display: block;
  font-size: 0.97rem;
  margin-bottom: 0.3em;
  color: #253056;
  font-weight: 500;
}
.input-wrapper {
  position: relative;
}
input[type="text"], input[type="email"], input[type="password"] {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #dbe1ef;
  border-radius: 7px;
  background: #f5f8ff;
  font-size: 1rem;
  transition: border .2s;
}
input:focus {
  border: 1.5px solid #5c65e5;
  outline: none;
}
.input-wrapper i {
  position: absolute;
  top: 50%;
  left: 12px;
  transform: translateY(-50%);
  color: #9aa3b7;
  font-size: 1rem;
}
button[type="submit"] {
  width: 100%;
  background: #6469e6;
  color: #fff;
  border: none;
  border-radius: 7px;
  padding: 0.9rem 0;
  font-size: 1.05rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 0.7rem;
  box-shadow: 0 1px 6px 0 rgba(53,79,255,0.04);
  transition: background .2s;
}
button[type="submit"]:hover {
  background: #4250d3;
}
.strong-password-bar {
  margin-top: 6px;
  height: 8px;
  width: 100%;
  background: #eaeaea;
  border-radius: 5px;
  overflow: hidden;
}
.strong-password-bar span {
  display: block;
  height: 100%;
  transition: width 0.25s;
}
.strength-weak { background: #e24c4b; }
.strength-medium { background: #ffd900; }
.strength-strong { background: #51c26e; }

.helper-link, .helper-link:visited {
  color: #6469e6;
  text-decoration: none;
  font-size: 0.96rem;
  margin-top: 1.1rem;
  display: inline-block;
}
.helper-link:hover { text-decoration: underline; }
.message {
  margin-bottom: 1.2em;
  font-size: 0.97rem;
  border-radius: 6px;
  padding: 0.6em 1em;
}
.message.success {
  background: #e8f8f1;
  color: #1a8843;
  border: 1px solid #71e4a2;
}
.message.error {
  background: #fde2e0;
  color: #b90c19;
  border: 1px solid #f5b0b0;
}
@media (max-width: 500px) {
  .auth-card { padding: 1.4rem 0.9rem 1.4rem 0.9rem; }
}
