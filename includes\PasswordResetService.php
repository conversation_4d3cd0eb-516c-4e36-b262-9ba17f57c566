<?php
/**
 * Password Reset Service for UTHM Attendance System
 * Handles secure email-based password reset functionality with token management
 */

require_once __DIR__ . '/EmailService.php';
require_once __DIR__ . '/../config/config.php';

class PasswordResetService {
    private $conn;
    private $emailService;
    
    public function __construct($connection) {
        $this->conn = $connection;
        try {
            $this->emailService = new EmailService();
            error_log("PasswordResetService: EmailService initialized successfully");
        } catch (Exception $e) {
            error_log("PasswordResetService: Failed to initialize EmailService - " . $e->getMessage());
            throw new Exception("Failed to initialize email service: " . $e->getMessage());
        }
    }
    
    /**
     * Generate and send password reset email for first-time login
     */
    public function initiatePasswordReset($userId, $userRole, $email, $userName) {
        try {
            // Clean up any existing tokens for this user
            $this->cleanupUserTokens($userId, $userRole);
            
            // Generate secure token
            $token = $this->generateSecureToken();

            // Use database timezone for consistency
            $expiresResult = $this->conn->query("SELECT DATE_ADD(NOW(), INTERVAL 1 HOUR) as expires_at");
            $expiresAt = $expiresResult->fetch_assoc()['expires_at'];
            
            // Store token in database
            $stmt = $this->conn->prepare("
                INSERT INTO password_reset_tokens 
                (user_id, user_role, token, email, expires_at, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
            
            $stmt->bind_param("sssssss", $userId, $userRole, $token, $email, $expiresAt, $ipAddress, $userAgent);
            
            if (!$stmt->execute()) {
                throw new Exception("Failed to store password reset token");
            }
            
            // Send password reset email
            $resetLink = $this->generateResetLink($token);
            $emailResult = $this->sendPasswordResetEmail($email, $userName, $resetLink, $userRole);
            
            if ($emailResult['success']) {
                return [
                    'success' => true,
                    'message' => 'Password reset email sent successfully',
                    'token' => $token
                ];
            } else {
                // Clean up token if email failed
                $this->invalidateToken($token);
                return [
                    'success' => false,
                    'message' => 'Failed to send password reset email: ' . $emailResult['message']
                ];
            }
            
        } catch (Exception $e) {
            error_log("Password reset initiation failed: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to initiate password reset: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Validate password reset token
     */
    public function validateToken($token) {
        try {
            // Check if table exists
            $tableCheck = $this->conn->query("SHOW TABLES LIKE 'password_reset_tokens'");
            if ($tableCheck->num_rows === 0) {
                error_log("Password reset tokens table does not exist");
                return false;
            }

            // Clean the token - remove any URL encoding artifacts
            $cleanToken = trim($token);

            // First check if token exists at all (without time/used constraints)
            $checkStmt = $this->conn->prepare("
                SELECT user_id, user_role, email, expires_at, used, created_at,
                       NOW() as server_time,
                       TIMESTAMPDIFF(MINUTE, NOW(), expires_at) as minutes_until_expiry,
                       CASE WHEN expires_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN 'valid' ELSE 'expired' END as time_status,
                       CASE WHEN used = 0 THEN 'unused' ELSE 'used' END as usage_status
                FROM password_reset_tokens
                WHERE token = ?
            ");

            if (!$checkStmt) {
                error_log("Failed to prepare token check statement: " . $this->conn->error);
                return false;
            }

            $checkStmt->bind_param("s", $cleanToken);
            $checkStmt->execute();
            $checkResult = $checkStmt->get_result();

            if ($checkResult->num_rows === 0) {
                return false;
            }

            $tokenInfo = $checkResult->fetch_assoc();

            // Now check if token is valid (not expired and not used)
            if ($tokenInfo['time_status'] === 'expired') {
                return false;
            }

            if ($tokenInfo['usage_status'] === 'used') {
                return false;
            }

            // Token is valid
            return [
                'user_id' => $tokenInfo['user_id'],
                'user_role' => $tokenInfo['user_role'],
                'email' => $tokenInfo['email'],
                'expires_at' => $tokenInfo['expires_at']
            ];

        } catch (Exception $e) {
            error_log("Token validation error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Reset password using valid token
     */
    public function resetPassword($token, $newPassword) {
        try {
            // Validate token
            $tokenData = $this->validateToken($token);
            if (!$tokenData) {
                return [
                    'success' => false,
                    'message' => 'Invalid or expired password reset token'
                ];
            }

            // Validate password strength
            $passwordValidation = $this->validatePassword($newPassword);
            if (!$passwordValidation['valid']) {
                return [
                    'success' => false,
                    'message' => $passwordValidation['message']
                ];
            }

            // Hash the new password
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

            // Determine table and field mappings based on user role
            $updateResult = $this->updateUserPassword($tokenData, $hashedPassword, $newPassword);

            if (!$updateResult['success']) {
                return $updateResult;
            }

            // Mark token as used
            $this->markTokenAsUsed($token);

            // Log successful password reset
            error_log("Password reset successful for user: {$tokenData['user_id']} ({$tokenData['user_role']}) - Updated {$updateResult['affected_rows']} row(s)");

            // Additional security logging
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
            error_log("Password reset completed from IP: $ipAddress, User-Agent: $userAgent");

            return [
                'success' => true,
                'message' => 'Password reset successfully',
                'user_role' => $tokenData['user_role']
            ];

        } catch (Exception $e) {
            error_log("Password reset failed: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to reset password: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Update user password in the correct table with proper field mapping
     */
    private function updateUserPassword($tokenData, $hashedPassword, $plainPassword = null) {
        try {
            $userId = $tokenData['user_id'];
            $userRole = $tokenData['user_role'];

            // Define table and field mappings for each user type
            $userMappings = [
                'student' => [
                    'table' => 'student',
                    'id_field' => 'StudentID',
                    'email_field' => 'Email'
                ],
                'lecturer' => [
                    'table' => 'lecturer',
                    'id_field' => 'LectID',
                    'email_field' => 'Email'
                ],
                'admin' => [
                    'table' => 'admin',
                    'id_field' => 'Username',
                    'email_field' => 'Username'
                ]
            ];

            if (!isset($userMappings[$userRole])) {
                throw new Exception("Invalid user role: $userRole");
            }

            $mapping = $userMappings[$userRole];
            $table = $mapping['table'];
            $idField = $mapping['id_field'];
            $emailField = $mapping['email_field'];

            // First, find the actual user record to get the correct ID
            // The token user_id might be email or UserID, so we need to search both
            // Handle different data types for ID fields (integer vs string)
            if ($userRole === 'student' || $userRole === 'lecturer') {
                // For students and lecturers, ID fields are integers, but we might have string values
                $findUserStmt = $this->conn->prepare("
                    SELECT * FROM $table
                    WHERE $idField = ? OR $emailField = ? OR CAST($idField AS CHAR) = ?
                    LIMIT 1
                ");
                $findUserStmt->bind_param("sss", $userId, $userId, $userId);
            } else {
                // For admin, use original logic
                $findUserStmt = $this->conn->prepare("
                    SELECT * FROM $table
                    WHERE $idField = ? OR $emailField = ?
                    LIMIT 1
                ");
                $findUserStmt->bind_param("ss", $userId, $userId);
            }

            $findUserStmt->execute();
            $userResult = $findUserStmt->get_result();

            if ($userResult->num_rows === 0) {
                error_log("User not found for password reset: $userId in table $table (role: $userRole)");
                error_log("Searched in fields: $idField, $emailField");
                return [
                    'success' => false,
                    'message' => 'User not found for password reset'
                ];
            }

            $userRecord = $userResult->fetch_assoc();
            $actualUserId = $userRecord[$idField];

            // Log the user we found
            error_log("Found user for password reset: $actualUserId in table $table (searched for: $userId)");

            // Update password using the actual user ID
            if ($userRole === 'admin') {
                // Admin users don't have FirstLogin field
                $updateStmt = $this->conn->prepare("
                    UPDATE $table
                    SET Password = ?
                    WHERE $idField = ?
                ");
                $updateStmt->bind_param("ss", $hashedPassword, $actualUserId);
            } else {
                // Student and lecturer have FirstLogin field
                $updateStmt = $this->conn->prepare("
                    UPDATE $table
                    SET Password = ?, FirstLogin = 0
                    WHERE $idField = ?
                ");
                $updateStmt->bind_param("ss", $hashedPassword, $actualUserId);
            }



            if (!$updateStmt->execute()) {
                throw new Exception("Failed to execute password update query");
            }

            $affectedRows = $updateStmt->affected_rows;

            if ($affectedRows === 0) {
                error_log("No rows affected during password update for user: $actualUserId in table $table");
                return [
                    'success' => false,
                    'message' => 'Failed to update password - no rows affected'
                ];
            }

            // Verify the password was actually updated by checking the hash
            if ($plainPassword) {
                $verifyStmt = $this->conn->prepare("SELECT Password FROM $table WHERE $idField = ?");
                $verifyStmt->bind_param("s", $actualUserId);
                $verifyStmt->execute();
                $verifyResult = $verifyStmt->get_result();

                if ($verifyResult->num_rows > 0) {
                    $verifyRecord = $verifyResult->fetch_assoc();
                    $storedHash = $verifyRecord['Password'];

                    if (password_verify($plainPassword, $storedHash)) {
                        error_log("Password update verified successfully for user: $actualUserId");
                    } else {
                        error_log("Password verification failed after update for user: $actualUserId");
                        return [
                            'success' => false,
                            'message' => 'Password update verification failed'
                        ];
                    }
                }
            }

            return [
                'success' => true,
                'affected_rows' => $affectedRows,
                'user_id' => $actualUserId
            ];

        } catch (Exception $e) {
            error_log("Error updating user password: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Database error during password update: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Resend password reset email
     */
    public function resendPasswordResetEmail($userId, $userRole) {
        try {
            // Get user information
            $table = ($userRole === 'student') ? 'student' : 'lecturer';
            $idField = ($userRole === 'student') ? 'StudentID' : 'LectID';
            
            $stmt = $this->conn->prepare("SELECT Name, Email FROM $table WHERE $idField = ?");
            $stmt->bind_param("s", $userId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows !== 1) {
                return [
                    'success' => false,
                    'message' => 'User not found'
                ];
            }
            
            $user = $result->fetch_assoc();
            return $this->initiatePasswordReset($userId, $userRole, $user['Email'], $user['Name']);
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to resend email: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Generate secure random token
     */
    private function generateSecureToken() {
        return bin2hex(random_bytes(32));
    }
    
    /**
     * Generate password reset link
     */
    private function generateResetLink($token) {
        $httpHost = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') .
                   '://' . $httpHost;

        // Get the application root path by removing the modules part if present
        $scriptPath = dirname($_SERVER['SCRIPT_NAME'] ?? '/');

        // If we're currently in modules directory, go up one level to get app root
        if (basename($scriptPath) === 'modules') {
            $appRoot = dirname($scriptPath);
        } else {
            $appRoot = $scriptPath;
        }

        return $baseUrl . $appRoot . '/modules/password_reset_form.php?token=' . urlencode($token);
    }
    
    /**
     * Send password reset email (for first-time login)
     */
    private function sendPasswordResetEmail($email, $userName, $resetLink, $userRole) {
        try {
            error_log("PasswordResetService: Attempting to send password reset email to $email for user $userName ($userRole)");

            $subject = "UTHM Attendance System - Password Reset Required";

            // HTML email body
            $htmlBody = $this->generatePasswordResetEmailHTML($userName, $resetLink, $userRole);

            // Plain text alternative
            $textBody = $this->generatePasswordResetEmailText($userName, $resetLink, $userRole);

            error_log("PasswordResetService: Email content generated, calling EmailService");

            // Use EmailService to send
            $result = $this->emailService->sendPasswordResetEmail($email, $userName, $subject, $htmlBody, $textBody);

            error_log("PasswordResetService: EmailService returned - Success: " . ($result['success'] ? 'true' : 'false') . ", Message: " . $result['message']);

            return $result;

        } catch (Exception $e) {
            error_log("PasswordResetService: Exception in sendPasswordResetEmail - " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to send email: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send forgot password email (for user-initiated password reset)
     */
    private function sendForgotPasswordEmail($email, $userName, $resetLink, $userRole) {
        try {
            $subject = "UTHM Attendance System - Password Reset Request";

            // HTML email body
            $htmlBody = $this->generateForgotPasswordEmailHTML($userName, $resetLink, $userRole);

            // Plain text alternative
            $textBody = $this->generateForgotPasswordEmailText($userName, $resetLink, $userRole);

            // Use EmailService to send
            return $this->emailService->sendPasswordResetEmail($email, $userName, $subject, $htmlBody, $textBody);

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to send email: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Initiate forgot password reset (different from first-time login)
     */
    public function initiateForgotPasswordReset($userId, $userRole, $email, $userName) {
        try {
            // Clean up any existing tokens for this user
            $this->cleanupUserTokens($userId, $userRole);

            // Generate secure token
            $token = $this->generateSecureToken();

            // Use database timezone for consistency
            $expiresResult = $this->conn->query("SELECT DATE_ADD(NOW(), INTERVAL 1 HOUR) as expires_at");
            $expiresAt = $expiresResult->fetch_assoc()['expires_at'];

            // Store token in database
            $stmt = $this->conn->prepare("
                INSERT INTO password_reset_tokens
                (user_id, user_role, token, email, expires_at, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");

            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

            $stmt->bind_param("sssssss", $userId, $userRole, $token, $email, $expiresAt, $ipAddress, $userAgent);
            $stmt->execute();

            // Send forgot password email
            $resetLink = $this->generateResetLink($token);
            $emailResult = $this->sendForgotPasswordEmail($email, $userName, $resetLink, $userRole);

            if ($emailResult['success']) {
                return [
                    'success' => true,
                    'message' => 'Password reset email sent successfully',
                    'token' => $token
                ];
            } else {
                // Clean up token if email failed
                $this->invalidateToken($token);
                return [
                    'success' => false,
                    'message' => 'Failed to send password reset email: ' . $emailResult['message']
                ];
            }

        } catch (Exception $e) {
            error_log("Forgot password reset initiation failed: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to initiate password reset: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Generate HTML email template
     */
    public function generatePasswordResetEmailHTML($userName, $resetLink, $userRole) {
        $roleTitle = ucfirst($userRole);
        
        return "
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .header { background-color: #0078d4; padding: 20px; text-align: center; color: white; }
                .content { padding: 30px; background-color: #f8f9fa; }
                .reset-box { background-color: white; padding: 25px; border-radius: 8px; border-left: 4px solid #0078d4; margin: 20px 0; }
                .button { display: inline-block; padding: 12px 30px; background-color: #0078d4; color: white; text-decoration: none; border-radius: 5px; font-weight: bold; }
                .footer { background-color: #f1f1f1; padding: 15px; text-align: center; font-size: 12px; color: #666; }
                .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h2>UTHM Attendance System</h2>
                <h3>Password Reset Required</h3>
            </div>
            <div class='content'>
                <p>Dear <strong>{$userName}</strong>,</p>
                
                <div class='reset-box'>
                    <h3>🔐 First-Time Login Detected</h3>
                    <p>You are receiving this email because you need to set a new password for your UTHM Attendance System account.</p>
                    <p><strong>Account Type:</strong> {$roleTitle}</p>
                </div>
                
                <p>To set your new password, please click the button below:</p>
                
                <p style='text-align: center; margin: 30px 0;'>
                    <a href='{$resetLink}' class='button'>Set New Password</a>
                </p>
                
                <div class='warning'>
                    <p><strong>⚠️ Important Security Information:</strong></p>
                    <ul>
                        <li>This link will expire in <strong>1 hour</strong> for security reasons</li>
                        <li>If you didn't request this password reset, please contact your system administrator</li>
                        <li>Never share this link with anyone else</li>
                    </ul>
                </div>
                
                <p>If the button above doesn't work, you can copy and paste this link into your browser:</p>
                <p style='word-break: break-all; background: #f1f1f1; padding: 10px; border-radius: 4px;'>{$resetLink}</p>
                
                <p>If you have any questions or need assistance, please contact your system administrator.</p>
                
                <p>Best regards,<br>
                <strong>UTHM Attendance System</strong></p>
            </div>
            <div class='footer'>
                <p>This is an automated message from the UTHM Attendance System.<br>
                Generated on " . date('Y-m-d H:i:s') . "</p>
            </div>
        </body>
        </html>";
    }
    
    /**
     * Generate plain text email (for first-time login)
     */
    public function generatePasswordResetEmailText($userName, $resetLink, $userRole) {
        $roleTitle = ucfirst($userRole);

        return "UTHM ATTENDANCE SYSTEM - PASSWORD RESET REQUIRED\n\n" .
               "Dear {$userName},\n\n" .
               "You are receiving this email because you need to set a new password for your UTHM Attendance System account.\n\n" .
               "Account Type: {$roleTitle}\n\n" .
               "To set your new password, please visit the following link:\n" .
               "{$resetLink}\n\n" .
               "IMPORTANT SECURITY INFORMATION:\n" .
               "- This link will expire in 1 hour for security reasons\n" .
               "- If you didn't request this password reset, please contact your system administrator\n" .
               "- Never share this link with anyone else\n\n" .
               "If you have any questions or need assistance, please contact your system administrator.\n\n" .
               "Best regards,\n" .
               "UTHM Attendance System\n\n" .
               "---\n" .
               "This is an automated message from the UTHM Attendance System.\n" .
               "Generated on " . date('Y-m-d H:i:s');
    }

    /**
     * Generate HTML email template for forgot password
     */
    private function generateForgotPasswordEmailHTML($userName, $resetLink, $userRole) {
        $roleTitle = ucfirst($userRole);

        return "
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .header { background-color: #0078d4; padding: 20px; text-align: center; color: white; }
                .content { padding: 30px; background-color: #f8f9fa; }
                .reset-box { background-color: white; padding: 25px; border-radius: 8px; border-left: 4px solid #dc3545; margin: 20px 0; }
                .button { display: inline-block; padding: 12px 30px; background-color: #0078d4; color: white; text-decoration: none; border-radius: 5px; font-weight: bold; }
                .footer { background-color: #f1f1f1; padding: 15px; text-align: center; font-size: 12px; color: #666; }
                .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h2>UTHM Attendance System</h2>
                <h3>Password Reset Request</h3>
            </div>
            <div class='content'>
                <p>Dear <strong>{$userName}</strong>,</p>

                <div class='reset-box'>
                    <h3>🔑 Password Reset Requested</h3>
                    <p>You are receiving this email because you requested a password reset for your UTHM Attendance System account.</p>
                    <p><strong>Account Type:</strong> {$roleTitle}</p>
                </div>

                <p>To reset your password, please click the button below:</p>

                <p style='text-align: center; margin: 30px 0;'>
                    <a href='{$resetLink}' class='button'>Reset My Password</a>
                </p>

                <div class='warning'>
                    <p><strong>⚠️ Important Security Information:</strong></p>
                    <ul>
                        <li>This link will expire in <strong>1 hour</strong> for security reasons</li>
                        <li>If you didn't request this password reset, please ignore this email or contact your system administrator</li>
                        <li>Never share this link with anyone else</li>
                    </ul>
                </div>

                <p>If the button above doesn't work, you can copy and paste this link into your browser:</p>
                <p style='word-break: break-all; background: #f1f1f1; padding: 10px; border-radius: 4px;'>{$resetLink}</p>

                <p>If you have any questions or need assistance, please contact your system administrator.</p>

                <p>Best regards,<br>
                <strong>UTHM Attendance System</strong></p>
            </div>
            <div class='footer'>
                <p>This is an automated message from the UTHM Attendance System.<br>
                Generated on " . date('Y-m-d H:i:s') . "</p>
            </div>
        </body>
        </html>";
    }

    /**
     * Generate plain text email for forgot password
     */
    private function generateForgotPasswordEmailText($userName, $resetLink, $userRole) {
        $roleTitle = ucfirst($userRole);

        return "UTHM ATTENDANCE SYSTEM - PASSWORD RESET REQUEST\n\n" .
               "Dear {$userName},\n\n" .
               "You are receiving this email because you requested a password reset for your UTHM Attendance System account.\n\n" .
               "Account Type: {$roleTitle}\n\n" .
               "To reset your password, please visit the following link:\n" .
               "{$resetLink}\n\n" .
               "IMPORTANT SECURITY INFORMATION:\n" .
               "- This link will expire in 1 hour for security reasons\n" .
               "- If you didn't request this password reset, please ignore this email or contact your system administrator\n" .
               "- Never share this link with anyone else\n\n" .
               "If you have any questions or need assistance, please contact your system administrator.\n\n" .
               "Best regards,\n" .
               "UTHM Attendance System\n\n" .
               "---\n" .
               "This is an automated message from the UTHM Attendance System.\n" .
               "Generated on " . date('Y-m-d H:i:s');
    }
    
    /**
     * Validate password strength
     */
    private function validatePassword($password) {
        if (strlen($password) < 8) {
            return ['valid' => false, 'message' => 'Password must be at least 8 characters long'];
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            return ['valid' => false, 'message' => 'Password must contain at least one uppercase letter'];
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            return ['valid' => false, 'message' => 'Password must contain at least one lowercase letter'];
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            return ['valid' => false, 'message' => 'Password must contain at least one number'];
        }
        
        if (!preg_match('/[^a-zA-Z0-9]/', $password)) {
            return ['valid' => false, 'message' => 'Password must contain at least one special character'];
        }
        
        return ['valid' => true, 'message' => 'Password is strong'];
    }
    
    /**
     * Clean up existing tokens for user
     */
    private function cleanupUserTokens($userId, $userRole) {
        $stmt = $this->conn->prepare("
            DELETE FROM password_reset_tokens 
            WHERE user_id = ? AND user_role = ?
        ");
        $stmt->bind_param("ss", $userId, $userRole);
        $stmt->execute();
    }
    
    /**
     * Mark token as used
     */
    private function markTokenAsUsed($token) {
        $stmt = $this->conn->prepare("
            UPDATE password_reset_tokens 
            SET used = 1, used_at = NOW() 
            WHERE token = ?
        ");
        $stmt->bind_param("s", $token);
        $stmt->execute();
    }
    
    /**
     * Invalidate token
     */
    private function invalidateToken($token) {
        $stmt = $this->conn->prepare("
            DELETE FROM password_reset_tokens 
            WHERE token = ?
        ");
        $stmt->bind_param("s", $token);
        $stmt->execute();
    }
    
    /**
     * Clean up expired tokens (maintenance function)
     */
    public function cleanupExpiredTokens() {
        $stmt = $this->conn->prepare("
            DELETE FROM password_reset_tokens 
            WHERE expires_at < NOW() OR used = 1
        ");
        return $stmt->execute();
    }
}
?>
