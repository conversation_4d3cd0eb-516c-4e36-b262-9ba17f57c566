/* ═══════════════════════════════════════════════════════════════
   MANAGE USERS PAGE - SPECIFIC STYLING
   Enhanced user management interface with professional design
   ═══════════════════════════════════════════════════════════════ */

/* ─────────── FORM SECTIONS ENHANCEMENT ─────────── */
.form-section {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

.form-section:hover {
  box-shadow: var(--shadow-md);
}

.form-section-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-lg) 0;
  padding-bottom: var(--spacing-sm);
  border-bottom: 3px solid var(--primary-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.form-section-title i {
  color: var(--primary-color);
  font-size: 1.1rem;
}

/* ─────────── ENHANCED ROLE SELECTION ─────────── */
.role-selection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.role-card {
  position: relative;
  padding: var(--spacing-md);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--card-bg);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.role-card:hover {
  border-color: var(--primary-color);
  background: rgba(59, 130, 246, 0.05);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.role-card.selected {
  border-color: var(--primary-color);
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.role-card input[type="radio"] {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.role-card-icon {
  font-size: 1.8rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
  transition: color 0.3s ease;
}

.role-card.selected .role-card-icon {
  color: var(--primary-color);
}

.role-card-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.role-card.selected .role-card-title {
  color: var(--primary-color);
}

.role-card-description {
  font-size: 0.8rem;
  color: var(--text-secondary);
  line-height: 1.3;
}

/* ─────────── ENHANCED FORM VALIDATION ─────────── */
.form-group {
  position: relative;
  margin-bottom: var(--spacing-lg);
}

.form-input {
  transition: all 0.3s ease;
  border: 2px solid var(--border-color);
}

.form-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.form-input.error {
  border-color: #dc2626;
  background-color: rgba(239, 68, 68, 0.05);
  animation: shake 0.5s ease-in-out;
}

.form-input.success {
  border-color: #059669;
  background-color: rgba(16, 185, 129, 0.05);
}

.form-input.success::after {
  content: '✓';
  position: absolute;
  right: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: #059669;
  font-weight: bold;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.error-message {
  display: block;
  font-size: 0.75rem;
  color: #dc2626;
  margin-top: var(--spacing-xs);
  font-weight: 500;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(239, 68, 68, 0.1);
  border-radius: var(--border-radius-sm);
  border-left: 3px solid #dc2626;
}

.success-message {
  display: block;
  font-size: 0.75rem;
  color: #059669;
  margin-top: var(--spacing-xs);
  font-weight: 500;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(16, 185, 129, 0.1);
  border-radius: var(--border-radius-sm);
  border-left: 3px solid #059669;
}

.field-hint {
  display: block;
  font-size: 0.7rem;
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
  font-style: italic;
  padding: var(--spacing-xs) 0;
}

/* ─────────── ENHANCED PASSWORD FIELD ─────────── */
.password-container {
  position: relative;
}

.password-container .toggle-password {
  position: absolute;
  right: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
}

.password-container .toggle-password:hover {
  color: var(--primary-color);
  background: rgba(59, 130, 246, 0.1);
}

/* ─────────── ENHANCED FORM ACTIONS ─────────── */
.form-actions {
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 2px solid var(--border-color);
  text-align: center;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
  border-radius: var(--border-radius);
  padding: var(--spacing-xl);
}

.action-btn.primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, #2563eb 100%);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  min-width: 200px;
  position: relative;
  overflow: hidden;
}

.action-btn.primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.action-btn.primary:hover::before {
  left: 100%;
}

.action-btn.primary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.action-btn.primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.action-btn.primary:disabled {
  background: var(--text-secondary);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* ─────────── USER FILTERING INTERFACE ─────────── */
.user-filter-container {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
}

.filter-tabs {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  border-bottom: 2px solid var(--border-color);
  padding-bottom: var(--spacing-md);
}

.filter-tab {
  padding: var(--spacing-sm) var(--spacing-lg);
  border: 2px solid transparent;
  border-radius: var(--border-radius);
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.filter-tab:hover {
  background: rgba(59, 130, 246, 0.05);
  color: var(--primary-color);
}

.filter-tab.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.user-count-badge {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 0.8rem;
  font-weight: 600;
  margin-left: var(--spacing-xs);
}

.filter-tab.active .user-count-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* ─────────── ENHANCED USER TABLES ─────────── */
.user-table-container {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.user-table-container.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Enhanced table styling for better layout */
.user-table-container .courses-table {
  table-layout: fixed;
  width: 100%;
}

.user-table-container .courses-table th,
.user-table-container .courses-table td {
  padding: var(--spacing-md);
  vertical-align: top;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Column width distribution */
.user-table-container .courses-table th:nth-child(1),
.user-table-container .courses-table td:nth-child(1) {
  width: 15%; /* Student/Staff ID */
}

.user-table-container .courses-table th:nth-child(2),
.user-table-container .courses-table td:nth-child(2) {
  width: 25%; /* Name */
}

.user-table-container .courses-table th:nth-child(3),
.user-table-container .courses-table td:nth-child(3) {
  width: 30%; /* Email */
}

.user-table-container .courses-table th:nth-child(4),
.user-table-container .courses-table td:nth-child(4) {
  width: 30%; /* Faculty */
}

.user-id-badge {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.user-id-badge.student {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.2) 100%);
  color: var(--primary-color);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.user-id-badge.lecturer {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.2) 100%);
  color: var(--success);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.user-id-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.faculty-text {
  color: var(--text-secondary);
  font-size: 0.85rem;
  line-height: 1.4;
  display: block;
  word-break: break-word;
}

.user-email {
  color: var(--text-primary);
  font-size: 0.85rem;
  line-height: 1.4;
  word-break: break-all;
  display: block;
}

.user-name {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.9rem;
  line-height: 1.3;
}

/* ─────────── RESPONSIVE DESIGN ─────────── */
@media (max-width: 768px) {
  .role-selection {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    max-width: 400px;
  }

  .role-card {
    padding: var(--spacing-md);
  }

  .role-card-icon {
    font-size: 1.6rem;
  }

  .filter-tabs {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .filter-tab {
    justify-content: center;
  }

  .form-section {
    padding: var(--spacing-lg);
  }

  .form-section-title {
    font-size: 1rem;
  }

  .action-btn.primary {
    width: 100%;
    min-width: auto;
  }

  /* Mobile table improvements */
  .user-table-container .courses-table th:nth-child(1),
  .user-table-container .courses-table td:nth-child(1) {
    width: 20%;
  }

  .user-table-container .courses-table th:nth-child(2),
  .user-table-container .courses-table td:nth-child(2) {
    width: 25%;
  }

  .user-table-container .courses-table th:nth-child(3),
  .user-table-container .courses-table td:nth-child(3) {
    width: 25%;
  }

  .user-table-container .courses-table th:nth-child(4),
  .user-table-container .courses-table td:nth-child(4) {
    width: 30%;
  }

  .faculty-text {
    font-size: 0.8rem;
  }

  .user-email {
    font-size: 0.8rem;
  }

  .user-name {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .form-section {
    padding: var(--spacing-md);
  }

  .role-selection {
    max-width: 300px;
  }

  .role-card {
    padding: var(--spacing-sm);
  }

  .role-card-icon {
    font-size: 1.4rem;
    margin-bottom: var(--spacing-sm);
  }

  .role-card-title {
    font-size: 0.9rem;
  }

  .role-card-description {
    font-size: 0.75rem;
  }

  /* Very small screen table adjustments */
  .user-table-container .courses-table th,
  .user-table-container .courses-table td {
    padding: var(--spacing-sm);
    font-size: 0.8rem;
  }

  .user-id-badge {
    font-size: 0.7rem;
    padding: 2px 6px;
  }

  .faculty-text {
    font-size: 0.75rem;
    line-height: 1.3;
  }

  .user-email {
    font-size: 0.75rem;
    line-height: 1.3;
  }

  .user-name {
    font-size: 0.8rem;
    line-height: 1.2;
  }
}

/* ─────────── STATUS BADGES FOR COURSE MANAGEMENT ─────────── */
.status-badge {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.status-badge.active {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.2) 100%);
  color: var(--success);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-badge.inactive {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.2) 100%);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
