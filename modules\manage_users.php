<?php
session_start();
require '../config/config.php';

// ─────────────────────────────────────────────────────────────
// Ensure only admin can access
// ─────────────────────────────────────────────────────────────
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// ─────────────────────────────────────────────────────────────
// CSRF Protection
// ─────────────────────────────────────────────────────────────
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// ─────────────────────────────────────────────────────────────
// Rate Limiting (Simple implementation)
// ─────────────────────────────────────────────────────────────
$rate_limit_key = 'user_form_' . $_SESSION['user_id'];
if (!isset($_SESSION[$rate_limit_key])) {
    $_SESSION[$rate_limit_key] = ['count' => 0, 'time' => time()];
}

// Reset counter if more than 5 minutes have passed
if (time() - $_SESSION[$rate_limit_key]['time'] > 300) {
    $_SESSION[$rate_limit_key] = ['count' => 0, 'time' => time()];
}

// ─────────────────────────────────────────────────────────────
// Enhanced Validation Functions
// ─────────────────────────────────────────────────────────────
function validateFullName($name) {
    // Sanitize input
    $name = trim($name);
    $name = filter_var($name, FILTER_SANITIZE_STRING, FILTER_FLAG_NO_ENCODE_QUOTES);
    // Check length
    if (strlen($name) < 2 || strlen($name) > 100) {
        return "Full name must be between 2 and 100 characters.";
    }
    // Check for valid characters (letters, spaces, apostrophes, hyphens)
    if (!preg_match("/^[a-zA-Z\s'\-]+$/u", $name)) {
        return "Full name can only contain letters, spaces, apostrophes, and hyphens.";
    }
    // Check for consecutive spaces
    if (preg_match("/\s{2,}/", $name)) {
        return "Full name cannot contain consecutive spaces.";
    }
    // Check for special characters at beginning or end
    if (preg_match("/^[\s'\-]|[\s'\-]$/", $name)) {
        return "Full name cannot start or end with spaces, apostrophes, or hyphens.";
    }
    // Additional security check for potential XSS
    if ($name !== strip_tags($name)) {
        return "Full name contains invalid characters.";
    }

    return true;
}

function validateEmail($email) {
    $email = trim($email);
    $email = filter_var($email, FILTER_SANITIZE_EMAIL);

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return "Invalid email format.";
    }

    // Optional: Check if domain exists
    $domain = substr(strrchr($email, "@"), 1);
    if (!checkdnsrr($domain, "MX") && !checkdnsrr($domain, "A")) {
        return "Email domain does not exist.";
    }

    return true;
}

function validatePhone($phone) {
    $phone = trim($phone);

    // Remove all non-numeric characters except + and -
    $cleaned_phone = preg_replace('/[^0-9+\-]/', '', $phone);

    // Malaysian phone number patterns
    $patterns = [
        '/^01[0-9]\-[0-9]{7,8}$/',     // 012-3456789
        '/^\+601[0-9][0-9]{7,8}$/',   // +60123456789
        '/^01[0-9][0-9]{7,8}$/'       // 0123456789
    ];

    $valid = false;
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $cleaned_phone)) {
            $valid = true;
            break;
        }
    }

    if (!$valid) {
        return "Invalid Malaysian phone number format. Use formats like 012-3456789, +60123456789, or 0123456789.";
    }

    return true;
}

function validateIdNumber($id_number, $id_type) {
    $id_number = trim($id_number);
    $id_number = preg_replace('/[^a-zA-Z0-9\-]/', '', $id_number);

    if ($id_type === 'IC') {
        // Malaysian IC format: YYMMDD-PB-GGGG
        if (!preg_match('/^[0-9]{6}\-[0-9]{2}\-[0-9]{4}$/', $id_number)) {
            return "Invalid IC format. Use format: YYMMDD-PB-GGGG (e.g., 990101-01-1234).";
        }

        // Additional IC validation
        $parts = explode('-', $id_number);
        $date_part = $parts[0];
        $year = substr($date_part, 0, 2);
        $month = substr($date_part, 2, 2);
        $day = substr($date_part, 4, 2);

        if ($month < 1 || $month > 12) {
            return "Invalid month in IC number.";
        }

        if ($day < 1 || $day > 31) {
            return "Invalid day in IC number.";
        }

    } elseif ($id_type === 'Passport') {
        // Passport: Alphanumeric, 6-12 characters
        if (!preg_match('/^[A-Za-z0-9]{6,12}$/', $id_number)) {
            return "Invalid passport format. Must be 6-12 alphanumeric characters.";
        }
    }

    return true;
}

function formatPhone($phone) {
    $phone = preg_replace('/[^0-9+]/', '', $phone);

    // Convert to standard format (012-3456789)
    if (preg_match('/^\+601([0-9])([0-9]{7,8})$/', $phone, $matches)) {
        return '01' . $matches[1] . '-' . $matches[2];
    } elseif (preg_match('/^01([0-9])([0-9]{7,8})$/', $phone, $matches)) {
        return '01' . $matches[1] . '-' . $matches[2];
    }

    return $phone;
}

function checkDuplicateId($conn, $id_number, $role) {
    $table = ($role === 'lecturer') ? 'lecturer' : 'student';
    $stmt = $conn->prepare("SELECT COUNT(*) FROM $table WHERE ID_Number = ?");
    $stmt->bind_param("s", $id_number);
    $stmt->execute();
    $stmt->bind_result($count);
    $stmt->fetch();
    $stmt->close();

    return $count > 0;
}

// ─────────────────────────────────────────────────────────────
// 1) Compute Next Student MatricNo (AI250001, AI250002, etc.) by querying the DB
// ─────────────────────────────────────────────────────────────
function getNextStudentMatric($conn) {
    // Query to find the highest student ID with new format (AI250001+) or old format (AI25*)
    $matricQuery = "
        SELECT UserID
        FROM student
        WHERE UserID LIKE 'AI25%'
        ORDER BY
            CASE
                WHEN LENGTH(UserID) >= 8 AND UserID REGEXP '^AI25[0-9]+$' THEN CAST(SUBSTRING(UserID, 5) AS UNSIGNED)
                ELSE 0
            END DESC
        LIMIT 1
    ";

    $nextStudentMatric = 'AI250001'; // Default starting ID with new format (8 digits total)

    if ($res = $conn->query($matricQuery)) {
        if ($res->num_rows > 0) {
            $row = $res->fetch_assoc();
            $lastMatric = $row['UserID'];                    // e.g., "AI250001" or "AI258"

            if (preg_match('/^AI25(\d+)$/', $lastMatric, $matches)) {
                // Extract numeric part from any valid AI25* format
                $numericPart = intval($matches[1]);          // Get numeric part: 0001, 0010, 0100, 1000, etc.
                $nextNumeric = $numericPart + 1;             // Increment: 2, 11, 101, 1001, etc.

                // Use 4-digit minimum padding, but allow natural growth
                if ($nextNumeric < 10000) {
                    // For numbers 1-9999, use 4-digit padding: 0001, 0010, 0100, 1000
                    $nextStudentMatric = 'AI25' . str_pad($nextNumeric, 4, '0', STR_PAD_LEFT);
                } else {
                    // For numbers 10000+, no padding needed: 10000, 10001, etc.
                    $nextStudentMatric = 'AI25' . $nextNumeric;
                }
            }
        }
        $res->free();
    }

    return $nextStudentMatric;
}

// ─────────────────────────────────────────────────────────────
// 2) Compute Next Lecturer Staff ID (00001, 00002, etc.) by querying the DB
// ─────────────────────────────────────────────────────────────
function getNextStaffId($conn) {
    // Query to find the highest numeric Staff ID
    $staffQuery = "
        SELECT UserID
        FROM lecturer
        WHERE UserID REGEXP '^[0-9]{5}$'
        ORDER BY CAST(UserID AS UNSIGNED) DESC
        LIMIT 1
    ";

    $nextStaffId = '00001'; // Default starting ID

    if ($res = $conn->query($staffQuery)) {
        if ($res->num_rows > 0) {
            $row = $res->fetch_assoc();
            $lastStaffId = $row['UserID'];                    // e.g., "00005"
            $numericPart = intval($lastStaffId);              // Convert to integer: 5
            $nextNumeric = $numericPart + 1;                  // Increment: 6
            $nextStaffId = str_pad($nextNumeric, 5, '0', STR_PAD_LEFT); // Zero-pad: "00006"
        }
        $res->free();
    }

    return $nextStaffId;
}

// Initialize next IDs
$nextStudentMatric = getNextStudentMatric($conn);
$nextStaffId = getNextStaffId($conn);

// ─────────────────────────────────────────────────────────────
// 2) Handle “Add User” form submission (UNCHANGED logic)
// ─────────────────────────────────────────────────────────────
$message = '';
$messageType = 'error';
$errors = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_user'])) {
    // CSRF Protection
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $message = "Invalid security token. Please refresh the page and try again.";
    } else {
        // Rate Limiting Check
        if ($_SESSION[$rate_limit_key]['count'] >= 5) {
            $message = "Too many attempts. Please wait 5 minutes before trying again.";
        } else {
            $_SESSION[$rate_limit_key]['count']++;

            // Get and sanitize input data
            $full_name = trim($_POST['full_name'] ?? '');
            $email     = trim($_POST['email'] ?? '');
            $phone     = trim($_POST['phone'] ?? '');
            $id_type   = $_POST['id_type'] ?? '';
            $id_number = trim($_POST['id_number'] ?? '');
            $gender    = $_POST['gender'] ?? '';
            $user_id   = trim($_POST['user_id'] ?? '');
            $password  = $_POST['password'] ?? '';
            $faculty   = $_POST['faculty'] ?? '';
            $role      = $_POST['role'] ?? '';

            // Validate all fields
            $nameValidation = validateFullName($full_name);
            if ($nameValidation !== true) {
                $errors['full_name'] = $nameValidation;
            }

            $emailValidation = validateEmail($email);
            if ($emailValidation !== true) {
                $errors['email'] = $emailValidation;
            } else {
                $email = filter_var($email, FILTER_SANITIZE_EMAIL);
            }

            $phoneValidation = validatePhone($phone);
            if ($phoneValidation !== true) {
                $errors['phone'] = $phoneValidation;
            } else {
                $phone = formatPhone($phone);
            }

            $idValidation = validateIdNumber($id_number, $id_type);
            if ($idValidation !== true) {
                $errors['id_number'] = $idValidation;
            }

            // Check for duplicate ID number
            if (empty($errors['id_number']) && checkDuplicateId($conn, $id_number, $role)) {
                $errors['id_number'] = "This ID number is already registered in the system.";
            }

            // Validate required fields
            if (empty($full_name)) $errors['full_name'] = "Full name is required.";
            if (empty($email)) $errors['email'] = "Email is required.";
            if (empty($phone)) $errors['phone'] = "Phone number is required.";
            if (empty($id_number)) $errors['id_number'] = "ID number is required.";
            if (empty($user_id)) $errors['user_id'] = "User ID is required.";
            if (empty($password)) $errors['password'] = "Password is required.";
            if (empty($faculty)) $errors['faculty'] = "Faculty selection is required.";
            if (!in_array($role, ['student', 'lecturer'])) $errors['role'] = "Invalid role selected.";
            if (!in_array($id_type, ['IC', 'Passport'])) $errors['id_type'] = "Invalid ID type selected.";
            if (!in_array($gender, ['Male', 'Female'])) $errors['gender'] = "Invalid gender selected.";

            // If no errors, proceed with insertion
            if (empty($errors)) {
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                $table = ($role === 'lecturer') ? 'lecturer' : 'student';

                // Auto-generate appropriate ID based on role
                if ($role === 'lecturer') {
                    // Use database transaction to prevent race conditions for Staff ID
                    $conn->begin_transaction();
                    try {
                        // Get the latest Staff ID to prevent duplicates
                        $latestStaffId = getNextStaffId($conn);
                        $user_id = $latestStaffId;

                        $insertSql = "
                          INSERT INTO $table
                            (Name, Email, PhoneNumber, ID_Type, ID_Number, Gender, UserID, Password, Faculty, FirstLogin)
                          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
                        ";

                        if ($stmt = $conn->prepare($insertSql)) {
                            $stmt->bind_param(
                              "sssssssss",
                              $full_name,
                              $email,
                              $phone,
                              $id_type,
                              $id_number,
                              $gender,
                              $user_id,
                              $hashed_password,
                              $faculty
                            );

                            if ($stmt->execute()) {
                                $conn->commit();
                                $message = "Lecturer added successfully! Staff ID: " . htmlspecialchars($user_id);
                                $messageType = 'success';

                                // Update next Staff ID for display
                                $nextStaffId = getNextStaffId($conn);
                            } else {
                                $conn->rollback();
                                $message = "Error: Unable to add lecturer. Please try again.";
                            }
                            $stmt->close();
                        } else {
                            $conn->rollback();
                            $message = "Database error occurred. Please try again.";
                        }
                    } catch (Exception $e) {
                        $conn->rollback();
                        $message = "Error: " . $e->getMessage();
                    }
                } else {
                    // Student creation with new format
                    $conn->begin_transaction();
                    try {
                        // Get the latest Student MatricNo to prevent duplicates
                        $latestStudentMatric = getNextStudentMatric($conn);
                        $user_id = $latestStudentMatric;

                        $insertSql = "
                          INSERT INTO $table
                            (Name, Email, PhoneNumber, ID_Type, ID_Number, Gender, UserID, Password, Faculty, FirstLogin)
                          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
                        ";

                        if ($stmt = $conn->prepare($insertSql)) {
                            $stmt->bind_param(
                              "sssssssss",
                              $full_name,
                              $email,
                              $phone,
                              $id_type,
                              $id_number,
                              $gender,
                              $user_id,
                              $hashed_password,
                              $faculty
                            );

                            if ($stmt->execute()) {
                                $conn->commit();
                                $message = "Student added successfully! Matric No: " . htmlspecialchars($user_id);
                                $messageType = 'success';

                                // Update next Student MatricNo for display
                                $nextStudentMatric = getNextStudentMatric($conn);
                            } else {
                                $conn->rollback();
                                $message = "Error: Unable to add student. Please try again.";
                            }
                            $stmt->close();
                        } else {
                            $conn->rollback();
                            $message = "Database error occurred. Please try again.";
                        }
                    } catch (Exception $e) {
                        $conn->rollback();
                        $message = "Error: " . $e->getMessage();
                    }
                }

                if ($messageType === 'success') {
                    // Reset rate limiting on success
                    $_SESSION[$rate_limit_key]['count'] = 0;
                    // Clear form data on success
                    $_POST = [];
                }
            } else {
                $message = "Please correct the errors below and try again.";
            }
        }
    }
}

// ─────────────────────────────────────────────────────────────
// Fetch User Counts and Lists for Enhanced Interface
// ─────────────────────────────────────────────────────────────
// Get student count
$student_count_query = "SELECT COUNT(*) as count FROM student";
$student_count_result = $conn->query($student_count_query);
$student_count = $student_count_result->fetch_assoc()['count'];

// Get lecturer count
$lecturer_count_query = "SELECT COUNT(*) as count FROM lecturer";
$lecturer_count_result = $conn->query($lecturer_count_query);
$lecturer_count = $lecturer_count_result->fetch_assoc()['count'];

// Get all students
$students_query = "SELECT UserID, Name, Email, Faculty FROM student ORDER BY UserID DESC";
$students_result = $conn->query($students_query);

// Get all lecturers
$lecturers_query = "SELECT UserID, Name, Email, Faculty FROM lecturer ORDER BY UserID DESC";
$lecturers_result = $conn->query($lecturers_query);
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Manage Users – UTHM Attendance</title>

  <!-- FontAwesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <!-- ─── GLOBAL CSS (same as other dashboards) ─── -->
  <link rel="stylesheet" href="../dashboard/css/base-styles.css" />
  <link rel="stylesheet" href="../dashboard/css/lecturer-header.css" />
  <link rel="stylesheet" href="../dashboard/css/lecturer-sidebar.css" />
  <link rel="stylesheet" href="../dashboard/css/lecturer-footer.css" />
  <link rel="stylesheet" href="../dashboard/css/lecturer-dashboard-styles.css" />

  <!-- ─── PAGE-SPECIFIC CSS ─── -->
  <link rel="stylesheet" href="css/manage-users1.css" />

</head>
<body>
  <!-- ───────── HEADER ───────── -->
  <div class="header">
    <div class="header-left">
      <img src="../assets/images/logo-uthm2.png" alt="UTHM Logo" class="logo">
    </div>
    <div class="header-right">
      <span class="user-id">ADMIN</span>
    </div>
  </div>

  <div class="container">
    <!-- ───────── SIDEBAR ───────── -->
    <div class="sidebar">
      <div class="profile">
        <img src="../assets/images/user1.png" alt="Admin Profile" class="profile-pic">
        <p class="profile-name">ADMIN</p>
      </div>
      <ul class="menu">
        <li><a href="../dashboard/admin.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
        <li><a href="../modules/manage_users.php" class="active"><i class="fas fa-user-plus"></i> Manage Users</a></li>
        <li><a href="../modules/manage_courses.php"><i class="fas fa-book"></i> Manage Courses</a></li>
        <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
      </ul>
    </div>

    <!-- ───────── MAIN CONTENT ───────── -->
    <div class="main-content">
      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">Manage Users</h1>
        <p class="page-subtitle">Add new students and lecturers to the system.</p>
      </div>

      <!-- ─────────────────── ADD USER SECTION ─────────────────── -->
      <div class="section-header">
        <h2 class="section-title"><i class="fas fa-user-plus"></i> Add New User</h2>
        <p class="section-subtitle">Register a new student or lecturer in the system.</p>
      </div>

      <!-- Popup Message (success or error) -->
      <?php if (!empty($message)): ?>
        <div class="popup-message <?= $messageType === 'success' ? 'success' : 'error' ?>" id="popup-message">
          <?php echo htmlspecialchars($message); ?>
          <button class="close-btn" onclick="closePopup()">&times;</button>
        </div>
      <?php endif; ?>

      <!-- Display individual field errors -->
      <?php if (!empty($errors)): ?>
        <div class="validation-errors" id="validation-errors">
          <h4><i class="fas fa-exclamation-triangle"></i> Please correct the following errors:</h4>
          <ul>
            <?php foreach ($errors as $field => $error): ?>
              <li><?= htmlspecialchars($error) ?></li>
            <?php endforeach; ?>
          </ul>
        </div>
      <?php endif; ?>

      <div class="courses-container">
        <form method="POST" action="" class="add-user-form" id="addUserForm" novalidate>
          <!-- CSRF Token -->
          <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token']) ?>">

          <!-- User Type Selection -->
          <div class="form-section">
            <h3 class="form-section-title"><i class="fas fa-user-tag"></i> User Type</h3>
            <div class="role-selection">
              <label class="role-card <?= ($_POST['role'] ?? 'student') === 'student' ? 'selected' : '' ?>">
                <input type="radio" name="role" value="student" id="role-student" <?= ($_POST['role'] ?? 'student') === 'student' ? 'checked' : '' ?>>
                <div class="role-card-icon">
                  <i class="fas fa-user-graduate"></i>
                </div>
                <div class="role-card-title">Student</div>
                <div class="role-card-description">Register a new student in the system</div>
              </label>
              <label class="role-card <?= ($_POST['role'] ?? '') === 'lecturer' ? 'selected' : '' ?>">
                <input type="radio" name="role" value="lecturer" id="role-lecturer" <?= ($_POST['role'] ?? '') === 'lecturer' ? 'checked' : '' ?>>
                <div class="role-card-icon">
                  <i class="fas fa-chalkboard-teacher"></i>
                </div>
                <div class="role-card-title">Lecturer</div>
                <div class="role-card-description">Register a new lecturer in the system</div>
              </label>
            </div>
          </div>

          <!-- Personal Information Section -->
          <div class="form-section">
            <h3 class="form-section-title"><i class="fas fa-user"></i> Personal Information</h3>
            <div class="form-grid">
              <!-- Full Name -->
              <div class="form-group">
                <label for="full_name">Full Name <span class="required">*</span></label>
                <input type="text"
                       name="full_name"
                       id="full_name"
                       placeholder="E.g. Ahmad Danish Adly"
                       required
                       minlength="2"
                       maxlength="100"
                       pattern="^[a-zA-Z\s'\-]+$"
                       title="Only letters, spaces, apostrophes, and hyphens are allowed"
                       class="form-input <?= isset($errors['full_name']) ? 'error' : '' ?>"
                       value="<?= htmlspecialchars($_POST['full_name'] ?? '') ?>">
                <?php if (isset($errors['full_name'])): ?>
                  <span class="error-message"><?= htmlspecialchars($errors['full_name']) ?></span>
                <?php endif; ?>
              </div>

              <!-- Email -->
              <div class="form-group">
                <label for="email">Email <span class="required">*</span></label>
                <input type="email"
                       name="email"
                       id="email"
                       placeholder="E.g. <EMAIL>"
                       required
                       pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
                       title="Please enter a valid email address"
                       class="form-input <?= isset($errors['email']) ? 'error' : '' ?>"
                       value="<?= htmlspecialchars($_POST['email'] ?? '') ?>">
                <?php if (isset($errors['email'])): ?>
                  <span class="error-message"><?= htmlspecialchars($errors['email']) ?></span>
                <?php endif; ?>
              </div>

              <!-- Phone Number -->
              <div class="form-group">
                <label for="phone">Phone Number <span class="required">*</span></label>
                <input type="tel"
                       name="phone"
                       id="phone"
                       placeholder="E.g. 012-3456789"
                       required
                       pattern="^(01[0-9]\-[0-9]{7,8}|\+601[0-9][0-9]{7,8}|01[0-9][0-9]{7,8})$"
                       title="Malaysian phone number format: 012-3456789, +60123456789, or 0123456789"
                       class="form-input <?= isset($errors['phone']) ? 'error' : '' ?>"
                       value="<?= htmlspecialchars($_POST['phone'] ?? '') ?>">
                <?php if (isset($errors['phone'])): ?>
                  <span class="error-message"><?= htmlspecialchars($errors['phone']) ?></span>
                <?php endif; ?>
              </div>
            </div>
          </div>

          <!-- Identification Section -->
          <div class="form-section">
            <h3 class="form-section-title"><i class="fas fa-id-card"></i> Identification</h3>
            <div class="form-grid">
              <!-- ID Type -->
              <div class="form-group">
                <label for="id_type">ID Type <span class="required">*</span></label>
                <select name="id_type" id="id_type" required class="form-input <?= isset($errors['id_type']) ? 'error' : '' ?>" onchange="updateIdValidation()">
                  <option value="IC" <?= ($_POST['id_type'] ?? '') === 'IC' ? 'selected' : '' ?>>Malaysian IC</option>
                  <option value="Passport" <?= ($_POST['id_type'] ?? '') === 'Passport' ? 'selected' : '' ?>>Passport</option>
                </select>
                <?php if (isset($errors['id_type'])): ?>
                  <span class="error-message"><?= htmlspecialchars($errors['id_type']) ?></span>
                <?php endif; ?>
              </div>

              <!-- ID Number -->
              <div class="form-group">
                <label for="id_number">ID Number <span class="required">*</span></label>
                <input type="text"
                       name="id_number"
                       id="id_number"
                       placeholder="E.g. 990101-01-1234"
                       oninput="syncPassword(); validateIdNumber()"
                       required
                       class="form-input <?= isset($errors['id_number']) ? 'error' : '' ?>"
                       value="<?= htmlspecialchars($_POST['id_number'] ?? '') ?>">
                <small class="field-hint" id="id_hint">For IC: YYMMDD-PB-GGGG format. For Passport: 6-12 alphanumeric characters.</small>
                <?php if (isset($errors['id_number'])): ?>
                  <span class="error-message"><?= htmlspecialchars($errors['id_number']) ?></span>
                <?php endif; ?>
              </div>

              <!-- Gender -->
              <div class="form-group">
                <label for="gender">Gender <span class="required">*</span></label>
                <select name="gender" id="gender" required class="form-input <?= isset($errors['gender']) ? 'error' : '' ?>">
                  <option value="Male" <?= ($_POST['gender'] ?? '') === 'Male' ? 'selected' : '' ?>>Male</option>
                  <option value="Female" <?= ($_POST['gender'] ?? '') === 'Female' ? 'selected' : '' ?>>Female</option>
                </select>
                <?php if (isset($errors['gender'])): ?>
                  <span class="error-message"><?= htmlspecialchars($errors['gender']) ?></span>
                <?php endif; ?>
              </div>
            </div>
          </div>

          <!-- Account Information Section -->
          <div class="form-section">
            <h3 class="form-section-title"><i class="fas fa-key"></i> Account Information</h3>
            <div class="form-grid">
              <!-- Password (Auto‐filled as ID Number) -->
              <div class="form-group">
                <label for="password">Password <span class="required">*</span></label>
                <div class="password-container">
                  <input type="password"
                         name="password"
                         id="password"
                         placeholder="Will match ID Number"
                         required
                         readonly
                         class="form-input <?= isset($errors['password']) ? 'error' : '' ?>"
                         value="<?= htmlspecialchars($_POST['password'] ?? '') ?>">
                  <i class="fas fa-eye toggle-password" onclick="togglePasswordVisibility()"></i>
                </div>
                <?php if (isset($errors['password'])): ?>
                  <span class="error-message"><?= htmlspecialchars($errors['password']) ?></span>
                <?php endif; ?>
              </div>

              <!-- MatricNo / StaffNo -->
              <div class="form-group">
                <label for="user_id">MatricNo / StaffNo <span class="required">*</span></label>
                <input type="text"
                       name="user_id"
                       id="user_id"
                       value="<?php echo htmlspecialchars($nextStudentMatric); ?>"
                       placeholder="AI250001"
                       required
                       readonly
                       class="form-input <?= isset($errors['user_id']) ? 'error' : '' ?>">
                <small class="field-hint">For Students, MatricNo auto‐increments (AI250001, AI250002, etc.). For Lecturers, Staff ID auto‐increments (00001, 00002, etc.).</small>
                <?php if (isset($errors['user_id'])): ?>
                  <span class="error-message"><?= htmlspecialchars($errors['user_id']) ?></span>
                <?php endif; ?>
              </div>

              <!-- Faculty Dropdown -->
              <div class="form-group">
                <label for="faculty">Faculty <span class="required">*</span></label>
                <select name="faculty" id="faculty" required class="form-input <?= isset($errors['faculty']) ? 'error' : '' ?>">
                  <option value="" disabled <?= empty($_POST['faculty']) ? 'selected' : '' ?>>Select Faculty</option>
                  <option value="Fakulti Sains Komputer dan Teknologi Maklumat" <?= ($_POST['faculty'] ?? '') === 'Fakulti Sains Komputer dan Teknologi Maklumat' ? 'selected' : '' ?>>Fakulti Sains Komputer dan Teknologi Maklumat</option>
                  <option value="Fakulti Kejuruteraan Mekanikal dan Pembuatan" <?= ($_POST['faculty'] ?? '') === 'Fakulti Kejuruteraan Mekanikal dan Pembuatan' ? 'selected' : '' ?>>Fakulti Kejuruteraan Mekanikal dan Pembuatan</option>
                  <option value="Fakulti Kejuruteraan Elektrik dan Elektronik" <?= ($_POST['faculty'] ?? '') === 'Fakulti Kejuruteraan Elektrik dan Elektronik' ? 'selected' : '' ?>>Fakulti Kejuruteraan Elektrik dan Elektronik</option>
                  <option value="Fakulti Kejuruteraan Awam dan Alam Bina" <?= ($_POST['faculty'] ?? '') === 'Fakulti Kejuruteraan Awam dan Alam Bina' ? 'selected' : '' ?>>Fakulti Kejuruteraan Awam dan Alam Bina</option>
                </select>
                <?php if (isset($errors['faculty'])): ?>
                  <span class="error-message"><?= htmlspecialchars($errors['faculty']) ?></span>
                <?php endif; ?>
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="form-actions">
            <button type="submit"
                    name="add_user"
                    class="action-btn primary"
                    id="submit-btn">
              <i class="fas fa-user-plus"></i> Add User
            </button>
          </div>
        </form>
      </div>

      <!-- ─────────────────── ENHANCED USER MANAGEMENT INTERFACE ─────────────────── -->
      <div class="section-header">
        <h2 class="section-title"><i class="fas fa-users-cog"></i> User Management</h2>
        <p class="section-subtitle">View and manage all users in the system with filtering options.</p>
      </div>

      <!-- User Filter Interface -->
      <div class="user-filter-container">
        <div class="filter-tabs">
          <button class="filter-tab active" data-filter="students" id="students-tab">
            <i class="fas fa-user-graduate"></i>
            Students
            <span class="user-count-badge"><?= $student_count ?></span>
          </button>
          <button class="filter-tab" data-filter="lecturers" id="lecturers-tab">
            <i class="fas fa-chalkboard-teacher"></i>
            Lecturers
            <span class="user-count-badge"><?= $lecturer_count ?></span>
          </button>
        </div>

        <!-- Students Table -->
        <div class="user-table-container active" id="students-container">
          <?php if ($students_result && $students_result->num_rows > 0): ?>
            <div class="courses-container">
              <table class="courses-table">
                <thead>
                  <tr>
                    <th>Student ID</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Faculty</th>
                  </tr>
                </thead>
                <tbody>
                  <?php while ($student = $students_result->fetch_assoc()): ?>
                    <tr>
                      <td data-label="Student ID">
                        <span class="user-id-badge student">
                          <?= htmlspecialchars($student['UserID']) ?>
                        </span>
                      </td>
                      <td data-label="Name">
                        <span class="user-name">
                          <?= htmlspecialchars($student['Name']) ?>
                        </span>
                      </td>
                      <td data-label="Email">
                        <span class="user-email">
                          <?= htmlspecialchars($student['Email']) ?>
                        </span>
                      </td>
                      <td data-label="Faculty">
                        <span class="faculty-text">
                          <?= htmlspecialchars($student['Faculty']) ?>
                        </span>
                      </td>
                    </tr>
                  <?php endwhile; ?>
                </tbody>
              </table>
            </div>
          <?php else: ?>
            <div class="no-data-msg">
              <i class="fas fa-user-graduate"></i>
              <strong>No students found.</strong><br>
              Students you add will appear here.
            </div>
          <?php endif; ?>
        </div>

        <!-- Lecturers Table -->
        <div class="user-table-container" id="lecturers-container">
          <?php if ($lecturers_result && $lecturers_result->num_rows > 0): ?>
            <div class="courses-container">
              <table class="courses-table">
                <thead>
                  <tr>
                    <th>Staff ID</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Faculty</th>
                  </tr>
                </thead>
                <tbody>
                  <?php while ($lecturer = $lecturers_result->fetch_assoc()): ?>
                    <tr>
                      <td data-label="Staff ID">
                        <span class="user-id-badge lecturer">
                          <?= htmlspecialchars($lecturer['UserID']) ?>
                        </span>
                      </td>
                      <td data-label="Name">
                        <span class="user-name">
                          <?= htmlspecialchars($lecturer['Name']) ?>
                        </span>
                      </td>
                      <td data-label="Email">
                        <span class="user-email">
                          <?= htmlspecialchars($lecturer['Email']) ?>
                        </span>
                      </td>
                      <td data-label="Faculty">
                        <span class="faculty-text">
                          <?= htmlspecialchars($lecturer['Faculty']) ?>
                        </span>
                      </td>
                    </tr>
                  <?php endwhile; ?>
                </tbody>
              </table>
            </div>
          <?php else: ?>
            <div class="no-data-msg">
              <i class="fas fa-chalkboard-teacher"></i>
              <strong>No lecturers found.</strong><br>
              Lecturers you add will appear here.
            </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>

  <!-- ───────── FOOTER ───────── -->
  <footer>
    <p>UNIVERSITI TUN HUSSEIN ONN MALAYSIA</p>
  </footer>

  <!-- ───────── JAVASCRIPT ───────── -->
  <script>
    // 1) Close popup message after a few seconds (or on × click)
    function closePopup() {
      let popup = document.getElementById("popup-message");
      if (popup) popup.style.display = "none";
    }
    document.addEventListener("DOMContentLoaded", function() {
      let popup = document.getElementById("popup-message");
      if (popup) {
        setTimeout(closePopup, 6000);
      }

      let errors = document.getElementById("validation-errors");
      if (errors) {
        setTimeout(function() { errors.style.display = "none"; }, 10000);
      }

      // Add event listeners for real-time validation
      document.getElementById("full_name").addEventListener("blur", validateFullName);
      document.getElementById("email").addEventListener("blur", validateEmail);
      document.getElementById("phone").addEventListener("blur", validatePhone);
      document.getElementById("id_number").addEventListener("blur", validateIdNumber);

      // Initialize ID validation based on current selection
      updateIdValidation();

      // Initialize role card selection
      initializeRoleCards();

      // Initialize user filtering
      initializeUserFiltering();
    });

    // 2) Sync “Password” field whenever ID Number changes
    function syncPassword() {
      let idNum = document.getElementById("id_number").value;
      document.getElementById("password").value = idNum;
    }

    // 3) Toggle password visibility
    function togglePasswordVisibility() {
      let pwdInput = document.getElementById("password");
      let toggle   = document.querySelector(".toggle-password");
      if (pwdInput.type === "password") {
        pwdInput.type = "text";
        toggle.classList.remove("fa-eye");
        toggle.classList.add("fa-eye-slash");
      } else {
        pwdInput.type = "password";
        toggle.classList.remove("fa-eye-slash");
        toggle.classList.add("fa-eye");
      }
    }

    // 4) If “Lecturer” chosen → MatricNo & Password become editable
    document.getElementById("role-student").addEventListener("change", function() {
      if (this.checked) {
        handleStudentRoleSelection();
      }
    });
    document.getElementById("role-lecturer").addEventListener("change", function() {
      if (this.checked) {
        handleLecturerRoleSelection();
      }
    });

    // ─────────── ENHANCED VALIDATION FUNCTIONS ───────────
    function validateFullName() {
      const input = document.getElementById("full_name");
      const value = input.value.trim();
      clearFieldValidation(input);
      if (value.length < 2 || value.length > 100) {
        showFieldError(input, "Full name must be between 2 and 100 characters.");
        return false;
      }
      if (!/^[a-zA-Z\s'\-]+$/.test(value)) {
        showFieldError(input, "Only letters, spaces, apostrophes, and hyphens are allowed.");
        return false;
      }
      if (/\s{2,}/.test(value)) {
        showFieldError(input, "Consecutive spaces are not allowed.");
        return false;
      }
      if (/^[\s'\-]|[\s'\-]$/.test(value)) {
        showFieldError(input, "Cannot start or end with spaces, apostrophes, or hyphens.");
        return false;
      }
      showFieldSuccess(input);
      return true;
    }
    function validateEmail() {
      const input = document.getElementById("email");
      const value = input.value.trim();
      clearFieldValidation(input);
      const emailRegex = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/i;

      if (!emailRegex.test(value)) {
        showFieldError(input, "Please enter a valid email address.");
        return false;
      }

      showFieldSuccess(input);
      return true;
    }

    function validatePhone() {
      const input = document.getElementById("phone");
      const value = input.value.trim();

      clearFieldValidation(input);

      const phonePatterns = [
        /^01[0-9]\-[0-9]{7,8}$/,     // 012-3456789
        /^\+601[0-9][0-9]{7,8}$/,   // +60123456789
        /^01[0-9][0-9]{7,8}$/       // 0123456789
      ];

      const isValid = phonePatterns.some(pattern => pattern.test(value));

      if (!isValid) {
        showFieldError(input, "Invalid Malaysian phone number format.");
        return false;
      }

      showFieldSuccess(input);
      return true;
    }

    function validateIdNumber() {
      const input = document.getElementById("id_number");
      const value = input.value.trim();
      const idType = document.getElementById("id_type").value;
      clearFieldValidation(input);
      if (idType === 'IC') {
        if (!/^[0-9]{6}\-[0-9]{2}\-[0-9]{4}$/.test(value)) {
          showFieldError(input, "Invalid IC format. Use: YYMMDD-PB-GGGG");
          return false;
        }
        // Additional IC validation
        const parts = value.split('-');
        const month = parseInt(parts[0].substring(2, 4));
        const day = parseInt(parts[0].substring(4, 6));
        if (month < 1 || month > 12) {
          showFieldError(input, "Invalid month in IC number.");
          return false;
        }
        if (day < 1 || day > 31) {
          showFieldError(input, "Invalid day in IC number.");
          return false;
        }
      } else if (idType === 'Passport') {
        if (!/^[A-Za-z0-9]{6,12}$/.test(value)) {
          showFieldError(input, "Passport must be 6-12 alphanumeric characters.");
          return false;
        }
      }

      showFieldSuccess(input);
      return true;
    }

    function updateIdValidation() {
      const idType = document.getElementById("id_type").value;
      const idInput = document.getElementById("id_number");
      const hint = document.getElementById("id_hint");

      if (idType === 'IC') {
        idInput.placeholder = "E.g. 990101-01-1234";
        idInput.pattern = "^[0-9]{6}\\-[0-9]{2}\\-[0-9]{4}$";
        hint.textContent = "IC format: YYMMDD-PB-GGGG (e.g., 990101-01-1234)";
      } else {
        idInput.placeholder = "E.g. A12345678";
        idInput.pattern = "^[A-Za-z0-9]{6,12}$";
        hint.textContent = "Passport: 6-12 alphanumeric characters (e.g., A12345678)";
      }

      // Re-validate if there's already a value
      if (idInput.value.trim()) {
        validateIdNumber();
      }
    }

    function clearFieldValidation(input) {
      input.classList.remove('error', 'success');
      const existingError = input.parentNode.querySelector('.error-message');
      if (existingError) existingError.remove();
    }

    function showFieldError(input, message) {
      input.classList.add('error');
      const errorSpan = document.createElement('span');
      errorSpan.className = 'error-message';
      errorSpan.textContent = message;
      input.parentNode.appendChild(errorSpan);
    }

    function showFieldSuccess(input) {
      input.classList.add('success');
    }

    // ─────────── ROLE CARD FUNCTIONALITY ───────────
    function initializeRoleCards() {
      const roleCards = document.querySelectorAll('.role-card');
      roleCards.forEach(card => {
        card.addEventListener('click', function() {
          const radio = this.querySelector('input[type="radio"]');
          radio.checked = true;

          // Update visual state
          roleCards.forEach(c => c.classList.remove('selected'));
          this.classList.add('selected');

          // Trigger role change logic
          if (radio.id === 'role-student') {
            handleStudentRoleSelection();
          } else {
            handleLecturerRoleSelection();
          }
        });
      });
    }

    function handleStudentRoleSelection() {
      document.getElementById("user_id").value = "<?php echo htmlspecialchars($nextStudentMatric); ?>";
      document.getElementById("user_id").readOnly = true;
      document.getElementById("user_id").placeholder = "AI250001";
      document.getElementById("id_number").addEventListener("input", syncPassword);
      document.getElementById("password").readOnly = true;
      syncPassword();
    }

    function handleLecturerRoleSelection() {
      document.getElementById("user_id").value = "<?php echo htmlspecialchars($nextStaffId); ?>";
      document.getElementById("user_id").readOnly = true;
      document.getElementById("user_id").placeholder = "00001";
      document.getElementById("password").value = "";
      document.getElementById("password").readOnly = false;
      document.getElementById("id_number").removeEventListener("input", syncPassword);
    }

    // ─────────── USER FILTERING FUNCTIONALITY ───────────
    function initializeUserFiltering() {
      const filterTabs = document.querySelectorAll('.filter-tab');
      filterTabs.forEach(tab => {
        tab.addEventListener('click', function() {
          const filter = this.dataset.filter;

          // Update active tab
          filterTabs.forEach(t => t.classList.remove('active'));
          this.classList.add('active');

          // Show/hide containers
          const containers = document.querySelectorAll('.user-table-container');
          containers.forEach(container => {
            container.classList.remove('active');
          });

          const targetContainer = document.getElementById(filter + '-container');
          if (targetContainer) {
            targetContainer.classList.add('active');
          }
        });
      });
    }

    // ─────────── FORM SUBMISSION VALIDATION ───────────
    document.getElementById("addUserForm").addEventListener("submit", function(e) {
      const isValidName = validateFullName();
      const isValidEmail = validateEmail();
      const isValidPhone = validatePhone();
      const isValidId = validateIdNumber();

      if (!isValidName || !isValidEmail || !isValidPhone || !isValidId) {
        e.preventDefault();
        alert("Please correct the validation errors before submitting.");
        return false;
      }
    });
  </script>
</body>
</html>
