<?php
/**
 * Email Delivery Guide and Troubleshooting
 * Provides users with clear guidance on email delivery issues
 */

require_once 'config/config.php';

// Check if user is coming from a password reset flow
$fromPasswordReset = isset($_GET['from']) && $_GET['from'] === 'password_reset';
$userEmail = isset($_GET['email']) ? htmlspecialchars($_GET['email']) : '';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Email Sent - UTHM Attendance</title>

    <!-- ─── GLOBAL CSS (same as other dashboards) ─── -->
    <link rel="stylesheet" href="dashboard/css/base-styles.css" />
    <link rel="stylesheet" href="dashboard/css/lecturer-header.css" />
    <link rel="stylesheet" href="dashboard/css/lecturer-sidebar.css" />
    <link rel="stylesheet" href="dashboard/css/lecturer-footer.css" />
    <link rel="stylesheet" href="dashboard/css/lecturer-dashboard-styles.css" />

    <!-- ─── ICONS ─── -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        /* ─────────── EMAIL DELIVERY GUIDE SPECIFIC STYLES ─────────── */
        .email-container {
            max-width: 500px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        .email-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-md);
            text-align: center;
        }

        .email-icon {
            font-size: 4rem;
            color: #6c7ae0;
            margin-bottom: var(--spacing-lg);
        }

        .email-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
        }

        .email-message {
            color: var(--text-secondary);
            font-size: 0.95rem;
            line-height: 1.5;
            margin-bottom: var(--spacing-lg);
        }

        .email-sent-info {
            background: #e8f2ff;
            border: 1px solid #b3d9ff;
            border-radius: var(--border-radius);
            padding: var(--spacing-md);
            margin: var(--spacing-lg) 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            color: #1e40af;
            font-size: 0.9rem;
        }

        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
            margin: var(--spacing-lg) 0;
        }

        .btn-primary {
            background: #4285f4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
        }

        .btn-primary:hover {
            background: #3367d6;
            color: white;
        }

        .btn-success {
            background: #34a853;
            color: white;
        }

        .btn-success:hover {
            background: #2d8f47;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            color: white;
        }

        .help-section {
            background: #f8f9fa;
            border-radius: var(--border-radius);
            padding: var(--spacing-lg);
            margin: var(--spacing-lg) 0;
            text-align: left;
        }

        .help-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .help-steps {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .help-step {
            padding: var(--spacing-sm) 0;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: flex-start;
            gap: var(--spacing-sm);
        }

        .help-step:last-child {
            border-bottom: none;
        }

        .step-number {
            background: #6c7ae0;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: 600;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .step-content {
            flex: 1;
        }

        .step-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .step-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .security-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: var(--border-radius);
            padding: var(--spacing-md);
            margin: var(--spacing-lg) 0;
            text-align: left;
        }

        .security-title {
            font-weight: 600;
            color: #856404;
            margin-bottom: var(--spacing-sm);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .security-list {
            list-style: none;
            padding: 0;
            margin: 0;
            color: #856404;
        }

        .security-list li {
            padding: 4px 0;
            font-size: 0.9rem;
        }

        .footer-info {
            text-align: center;
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-lg);
            border-top: 1px solid var(--border-color);
            color: var(--text-secondary);
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-card">
            <div class="email-icon">
                <i class="fas fa-envelope-open"></i>
            </div>

            <h1 class="email-title">Password Reset Email Sent</h1>

            <p class="email-message">
                Hello, <strong><?php echo isset($_GET['name']) ? htmlspecialchars($_GET['name']) : 'User'; ?>!</strong> We've sent a secure password reset link to your email address. Please check your email to continue.
            </p>

            <?php if ($fromPasswordReset && $userEmail): ?>
            <div class="email-sent-info">
                <i class="fas fa-envelope"></i>
                Email sent to: <?php echo $userEmail; ?>
            </div>
            <?php endif; ?>

            <div class="action-buttons">
                <a href="modules/forgot_password.php" class="btn-primary">
                    <i class="fas fa-redo"></i>
                    Resend Email
                </a>
                <a href="modules/forgot_password.php" class="btn-success">
                    <i class="fas fa-edit"></i>
                    Update Email Address
                </a>
                <a href="index.php" class="btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Try Different Account
                </a>
            </div>

            <div class="help-section">
                <div class="help-title">
                    <i class="fas fa-question-circle"></i>
                    Can't find the email? Here's how to locate it:
                </div>

                <ul class="help-steps">
                    <li class="help-step">
                        <span class="step-number">1</span>
                        <div class="step-content">
                            <div class="step-title">Check Your Inbox</div>
                            <div class="step-description">Look for an email from <strong>UTHM Attendance System</strong> (<EMAIL>)</div>
                        </div>
                    </li>
                    <li class="help-step">
                        <span class="step-number">2</span>
                        <div class="step-content">
                            <div class="step-title">Check Spam/Junk Folder</div>
                            <div class="step-description">Automated emails are sometimes filtered. Check your spam or junk folder.</div>
                        </div>
                    </li>
                    <li class="help-step">
                        <span class="step-number">3</span>
                        <div class="step-content">
                            <div class="step-title">Wait a Few Minutes</div>
                            <div class="step-description">Email delivery can take 1-5 minutes. Please be patient.</div>
                        </div>
                    </li>
                    <li class="help-step">
                        <span class="step-number">4</span>
                        <div class="step-content">
                            <div class="step-title">Add to Safe Senders</div>
                            <div class="step-description">Add <EMAIL> to your safe senders list for future emails.</div>
                        </div>
                    </li>
                </ul>
            </div>

            <div class="security-info">
                <div class="security-title">
                    <i class="fas fa-shield-alt"></i>
                    Security Information
                </div>
                <ul class="security-list">
                    <li>• The password reset link expires in <strong>1 hour</strong> for security</li>
                    <li>• Each link can only be used <strong>once</strong></li>
                    <li>• Never share your password reset link with anyone</li>
                    <li>• If you didn't request this reset, contact your administrator</li>
                </ul>
            </div>

            <div class="footer-info">
                <i class="fas fa-clock"></i>
                Generated on <?php echo date('Y-m-d H:i:s'); ?>
            </div>
        </div>
    </div>

    <!-- Auto-refresh reminder -->
    <script>
        // Show a reminder to check email after 2 minutes
        setTimeout(function() {
            if (confirm('It\'s been 2 minutes. Would you like to check if the email has arrived? Click OK to refresh this page.')) {
                location.reload();
            }
        }, 120000); // 2 minutes
    </script>
</body>
</html>
