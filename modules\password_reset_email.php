<?php
/**
 * Email Verification Page for Password Reset
 * Shows confirmation that password reset email has been sent
 * Provides options to resend email or update email address
 */

session_start();
require '../config/config.php';
require_once __DIR__ . '/../includes/PasswordResetService.php';

// Security check: only allow access during first-time login process
if (!isset($_SESSION['first_login_user_id']) || !isset($_SESSION['first_login_role'])) {
    header("Location: ../index.php");
    exit();
}

$user_id = $_SESSION['first_login_user_id'];
$role = $_SESSION['first_login_role'];
$message = '';
$messageType = 'info';

// Get user information
$table = ($role === 'student') ? 'student' : 'lecturer';
$id_field = ($role === 'student') ? 'StudentID' : 'LectID';

$stmt = $conn->prepare("SELECT Name, Email FROM $table WHERE $id_field = ?");
$stmt->bind_param("s", $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows !== 1) {
    header("Location: ../index.php");
    exit();
}

$user = $result->fetch_assoc();
$userName = $user['Name'];
$userEmail = $user['Email'];

$passwordResetService = new PasswordResetService($conn);

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['resend_email'])) {
        // Resend password reset email
        $result = $passwordResetService->resendPasswordResetEmail($user_id, $role);
        
        if ($result['success']) {
            // Redirect to email delivery guide after successful resend
            $redirectUrl = "../email_delivery_guide.php?from=password_reset&email=" . urlencode($userEmail);
            header("Location: $redirectUrl");
            exit();
        } else {
            $message = "Failed to resend email: " . $result['message'];
            $messageType = 'error';
        }
        
    } elseif (isset($_POST['update_email'])) {
        // Update email address
        $newEmail = trim($_POST['new_email']);
        
        if (empty($newEmail)) {
            $message = "Please enter a valid email address.";
            $messageType = 'error';
        } elseif (!filter_var($newEmail, FILTER_VALIDATE_EMAIL)) {
            $message = "Please enter a valid email address format.";
            $messageType = 'error';
        } else {
            // Update email in database
            $updateStmt = $conn->prepare("UPDATE $table SET Email = ? WHERE $id_field = ?");
            $updateStmt->bind_param("ss", $newEmail, $user_id);
            
            if ($updateStmt->execute()) {
                $userEmail = $newEmail;
                
                // Send password reset email to new address
                $result = $passwordResetService->initiatePasswordReset($user_id, $role, $newEmail, $userName);
                
                if ($result['success']) {
                    // Redirect to email delivery guide after successful email update and send
                    $redirectUrl = "../email_delivery_guide.php?from=password_reset&email=" . urlencode($newEmail);
                    header("Location: $redirectUrl");
                    exit();
                } else {
                    $message = "Email address updated, but failed to send password reset email: " . $result['message'];
                    $messageType = 'warning';
                }
            } else {
                $message = "Failed to update email address. Please try again.";
                $messageType = 'error';
            }
        }
    }
}

// If no email has been sent yet, send initial password reset email
if (!isset($_POST['resend_email']) && !isset($_POST['update_email']) && empty($message)) {
    $result = $passwordResetService->initiatePasswordReset($user_id, $role, $userEmail, $userName);
    
    if ($result['success']) {
        // Redirect to email delivery guide for better user experience
        $redirectUrl = "../email_delivery_guide.php?from=password_reset&email=" . urlencode($userEmail);
        header("Location: $redirectUrl");
        exit();
    } else {
        $message = "Failed to send password reset email: " . $result['message'];
        $messageType = 'error';
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Email - UTHM Attendance</title>

    <!-- ─── GLOBAL CSS (same as other dashboards) ─── -->
    <link rel="stylesheet" href="../dashboard/css/base-styles.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-header.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-sidebar.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-footer.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-dashboard-styles.css" />

    <!-- ─── ICONS ─── -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        /* ─────────── PASSWORD RESET EMAIL PAGE SPECIFIC STYLES ─────────── */
        .reset-container {
            max-width: 500px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        .reset-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-md);
        }

        .reset-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
        }

        .reset-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-md);
        }

        .reset-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .reset-subtitle {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .alert-message {
            padding: var(--spacing-md);
            border-radius: var(--border-radius);
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .alert-success {
            background-color: #d1f2eb;
            border: 1px solid #7dcea0;
            color: #0e6b47;
        }

        .alert-error {
            background-color: #fadbd8;
            border: 1px solid #f1948a;
            color: #922b21;
        }

        .alert-warning {
            background-color: #fcf3cf;
            border: 1px solid #f7dc6f;
            color: #7d6608;
        }
        .user-info {
            background: var(--bg-light);
            padding: var(--spacing-md);
            border-radius: var(--border-radius);
            margin-bottom: var(--spacing-lg);
            text-align: center;
        }

        .user-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .user-role {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .email-highlight {
            background: var(--bg-light);
            padding: var(--spacing-sm);
            border-radius: var(--border-radius);
            font-family: monospace;
            word-break: break-all;
            border: 1px solid var(--border-color);
        }

        .form-group {
            margin-bottom: var(--spacing-lg);
        }

        .form-label {
            display: block;
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .form-input {
            width: 100%;
            padding: var(--spacing-md);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: #ffffff;
            border: 2px solid #3b82f6;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            min-height: 48px;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
            margin-bottom: var(--spacing-md);
        }

        .submit-btn:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            border-color: #2563eb;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }

        .secondary-btn {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            border-color: #6b7280;
        }

        .secondary-btn:hover {
            background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
            border-color: #4b5563;
        }

        .back-link {
            display: block;
            text-align: center;
            margin-top: var(--spacing-lg);
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .back-link:hover {
            color: var(--primary-color);
        }

        .action-section {
            background: var(--bg-light);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius);
            margin: var(--spacing-lg) 0;
            border: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .btn-group {
            display: flex;
            gap: var(--spacing-sm);
            flex-wrap: wrap;
        }

        .btn-group .submit-btn {
            flex: 1;
            min-width: 150px;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-card">
            <div class="reset-header">
                <div class="reset-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <h1 class="reset-title">Password Reset Email</h1>
                <p class="reset-subtitle">First-Time Login Setup</p>
            </div>

            <?php if ($message): ?>
                <div class="alert-message alert-<?php echo $messageType; ?>">
                    <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : ($messageType === 'error' ? 'exclamation-circle' : 'exclamation-triangle'); ?>"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <div class="user-info">
                <div class="user-name">
                    <i class="fas fa-user"></i>
                    Welcome, <?php echo htmlspecialchars($userName); ?>!
                </div>
                <div class="user-role">Account Type: <?php echo ucfirst($role); ?></div>
            </div>

            <div class="alert-message alert-success">
                <i class="fas fa-info-circle"></i>
                <div>
                    <strong>Email Sent Successfully!</strong><br>
                    A password reset email has been sent to: <div class="email-highlight"><?php echo htmlspecialchars($userEmail); ?></div>
                </div>
            </div>

            <div class="action-section">
                <div class="section-title">
                    <i class="fas fa-tools"></i>
                    Need Help?
                </div>
                <p>If you don't receive the email within a few minutes:</p>

                <div class="btn-group">
                    <form method="POST" style="flex: 1;">
                        <button type="submit" name="resend_email" class="submit-btn">
                            <i class="fas fa-redo"></i>
                            Resend Email
                        </button>
                    </form>

                    <button type="button" class="submit-btn secondary-btn" onclick="toggleEmailForm()">
                        <i class="fas fa-edit"></i>
                        Update Email
                    </button>
                </div>
            </div>

            <div id="updateEmailForm" style="display: none;">
                <div class="action-section">
                    <div class="section-title">
                        <i class="fas fa-envelope"></i>
                        Update Email Address
                    </div>
                    <form method="POST">
                        <div class="form-group">
                            <label for="new_email" class="form-label">New Email Address:</label>
                            <input type="email" class="form-input" id="new_email" name="new_email" required>
                        </div>
                        <button type="submit" name="update_email" class="submit-btn">
                            <i class="fas fa-save"></i>
                            Update & Send Email
                        </button>
                    </form>
                </div>
            </div>

            <a href="../index.php" class="back-link">
                <i class="fas fa-arrow-left"></i>
                Back to Login
            </a>

            <div style="text-align: center; margin-top: var(--spacing-lg); color: var(--text-secondary); font-size: 0.8rem;">
                <i class="fas fa-clock"></i>
                Generated on <?php echo date('Y-m-d H:i:s'); ?>
            </div>
        </div>
    </div>

    <script>
        function toggleEmailForm() {
            const form = document.getElementById('updateEmailForm');
            form.style.display = form.style.display === 'none' ? 'block' : 'none';
        }
    </script>
</body>
</html>
