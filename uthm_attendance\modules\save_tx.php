<?php
// save_tx.php

header('Content-Type: application/json');
require '../config/config.php';  // Adjust the path if needed

// Read the raw JSON input
$input = json_decode(file_get_contents('php://input'), true);
if (!$input) {
    echo json_encode([ 'success' => false, 'error' => 'Invalid JSON' ]);
    exit;
}

$attendanceID = intval($input['attendanceID'] ?? 0);
$txHash       = trim($input['txHash'] ?? '');

if (!$attendanceID || !$txHash) {
    echo json_encode([ 'success' => false, 'error' => 'Missing attendanceID or txHash' ]);
    exit;
}

// Insert into blockchain_record table
$stmt = $conn->prepare("
    INSERT INTO blockchain_record 
       (AttendanceID, Timestamp, Blockchain_Hash) 
    VALUES (?, NOW(), ?)
");
$stmt->bind_param("is", $attendanceID, $txHash);
$ok = $stmt->execute();
if ($ok) {
    echo json_encode([ 'success' => true ]);
} else {
    echo json_encode([ 'success' => false, 'error' => 'DB insert failed' ]);
}
$stmt->close();
$conn->close();
