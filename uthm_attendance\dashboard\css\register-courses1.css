/* ===================================================================
   register-courses.css
   ──────────────────────────────────────────────────────────────────
   Azia‐inspired enhancements merged with your Student Dashboard
   styling. Drop this into dashboard/css/register-courses.css
   =================================================================== */

/* ─────────── VARIABLES ─────────── */
:root {
  --card-bg:            #ffffff;
  --border-color:       #e5e7eb;
  --border-radius-lg:   8px;
  --border-radius:      8px;
  --spacing-xs:         4px;
  --spacing-sm:         8px;
  --spacing-md:         16px;
  --spacing-lg:         24px;

  --primary-color:      #6366F1;
  --primary-light:      #818CF8;
  --primary-dark:       #4F46E5;

  --success-color:      #10B981;
  --danger-color:       #EF4444;
  --warning-color:      #F59E0B;
  --info-color:         #3B82F6;

  --text-primary:       #1F2937;
  --text-secondary:     #6B7280;

  --shadow-sm:          0 1px 2px rgba(0,0,0,0.05);
  --shadow-md:          0 4px 6px rgba(0,0,0,0.1);
}

/* ─────────── RESET & BASE ─────────── */
body {
  background: var(--card-bg);
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  margin: 0;
  padding: 0;
}

/* ─────────── PAGE HEADER ─────────── */
.page-header {
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}
.page-header::before {
  content: '';
  position: absolute;
  top: 0; left: 0; right: 0;
  height: 4px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
}
.page-header h1 {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 var(--spacing-xs) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.page-header h1 i {
  color: var(--primary-color);
}
.page-header p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
  font-weight: 400;
}

/* ─────────── STATS SUMMARY ─────────── */
.stats-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}
.stat-card {
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}
.stat-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: var(--card-bg);
}
.stat-icon.registered {
  background: var(--success-color);
}
.stat-icon.available {
  background: var(--info-color);
}
.stat-icon.completion {
  background: var(--warning-color);
}
.stat-content {
  flex: 1;
}
.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}
.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* ─────────── ALERT ─────────── */
.alert {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-lg);
  font-size: 0.875rem;
  border-left: 4px solid;
  box-shadow: var(--shadow-sm);
  animation: slideIn 0.4s ease-out;
}
.alert.success {
  background: #f0fdf4;
  color: #166534;
  border-left-color: var(--success-color);
}
.alert.success::before {
  content: '\f00c';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  color: var(--success-color);
}
.alert.error {
  background: #fef2f2;
  color: #991b1b;
  border-left-color: var(--danger-color);
}
.alert.error::before {
  content: '\f071';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  color: var(--danger-color);
}

/* ─────────── TWO-COLUMN LAYOUT ─────────── */
.dual-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}
@media (max-width: 768px) {
  .dual-section {
    grid-template-columns: 1fr;
  }
}

/* ─────────── TABLE CARDS ─────────── */
.table-card {
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
  margin-bottom: var(--spacing-lg);
}
.table-title {
  position: relative;
  padding: var(--spacing-lg) var(--spacing-lg) 0;
  margin: 0 0 var(--spacing-md) 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}
.table-title::before {
  content: '\f02d';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  font-size: 1rem;
  color: var(--primary-color);
}
.table-wrapper {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  padding: 0 var(--spacing-lg) var(--spacing-lg);
}

.courses-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: var(--card-bg);
  font-size: 0.875rem;
}
.courses-table th {
  position: sticky;
  top: 0;
  background: #f8fafc;
  color: #64748b;
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 1rem 0.75rem;
  border-bottom: 2px solid var(--border-color);
  white-space: nowrap;
}
.courses-table th:first-child { padding-left: 1.5rem; }
.courses-table th:last-child  { padding-right: 1.5rem; }

.courses-table td {
  padding: 1.25rem 0.75rem;
  border-bottom: 1px solid #f1f5f9;
  vertical-align: middle;
  transition: background 0.2s ease;
}
.courses-table td:first-child { padding-left: 1.5rem; }
.courses-table td:last-child  { padding-right: 1.5rem; }

.courses-table tbody tr:nth-child(even) {
  background: #fafbfc;
}
.courses-table tbody tr:hover td {
  background: #f1f5f9;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

/* ─────────── SEARCH BOX ─────────── */
.search-box {
  position: relative;
  max-width: 300px;
  margin-left: auto;
  margin-bottom: var(--spacing-md);
}
.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 0.875rem;
  z-index: 2;
}
.search-box input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) 2.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  background: var(--card-bg);
  transition: all 0.3s ease;
  font-weight: 400;
}
.search-box input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99,102,241,0.1);
}
.search-box input::placeholder {
  color: var(--text-secondary);
}

/* ─────────── REGISTER BUTTON ─────────── */
.register-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  background: var(--primary-color);
  color: var(--card-bg);
  border: none;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: capitalize;
  box-shadow: var(--shadow-sm);
}
.register-btn::before {
  content: '\f067';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  font-size: 0.75rem;
}
.register-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}
.register-btn:active {
  transform: scale(0.98);
}

/* ─────────── REMOVE BUTTON ─────────── */
.remove-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  background: var(--danger-color);
  color: var(--card-bg);
  border: none;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
}
.remove-btn::before {
  content: '\f1f8';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
}
.remove-btn:hover {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}
.remove-btn:active {
  transform: scale(0.98);
}

/* ─────────── EMPTY STATE ─────────── */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  color: var(--text-secondary);
}
.empty-state i {
  font-size: 4rem;
  margin-bottom: var(--spacing-md);
  color: #e2e8f0;
  opacity: 0.7;
}
.empty-state h3 {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}
.empty-state p {
  font-size: 0.875rem;
  margin-bottom: var(--spacing-md);
}
.empty-state .cta-button {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: var(--card-bg);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  box-shadow: var(--shadow-sm);
  text-decoration: none;
}
.empty-state .cta-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
}

/* ─────────── RESPONSIVE ─────────── */
@media (max-width: 1024px) {
  .search-box { max-width: 250px; }
}
@media (max-width: 768px) {
  .register-btn, .remove-btn { font-size: 0.75rem; padding: 0.5rem; }
}
@media (max-width: 480px) {
  .page-header h1 { font-size: 1.5rem; }
  .stat-number { font-size: 1.25rem; }
  .stat-label { font-size: 0.75rem; }
  .courses-table th, .courses-table td { padding: 0.75rem 0.5rem; }
}

/* ─────────── ANIMATIONS ─────────── */
@keyframes slideIn {
  from { opacity: 0; transform: translateX(-20px); }
  to   { opacity: 1; transform: translateX(0); }
}
/* Add this to style the Unregister button */
.unregister-btn {
  background: #e24c4b;
  color: #fff;
  border: none;
  padding: 6px 16px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  font-size: 1rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5em;
  transition: background 0.18s;
}
.unregister-btn:hover, .unregister-btn:focus {
  background: #c03030;
  color: #fff;
  outline: none;
}
.unregister-btn i {
  margin-right: 3px;
}
