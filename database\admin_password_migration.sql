-- ===================================================================
-- ADMIN PASSWORD SECURITY MIGRATION
-- This script updates admin passwords to use secure hashing
-- ===================================================================

-- Step 1: Ensure the Password column can accommodate hashed passwords (255 characters)
-- The current schema already has VARCHAR(255), so this is just a safety check
ALTER TABLE `admin` MODIFY COLUMN `Password` VARCHAR(255) NOT NULL;

-- Step 2: Update existing admin passwords to use secure hashing
-- Note: This will be done via PHP script for proper password_hash() function usage
-- The plain text passwords will be hashed using PASSWORD_DEFAULT algorithm

-- Current admin record:
-- AdminID: 1, Username: 'admin1', Email: '<EMAIL>', Password: 'admin123'

-- After migration, the password 'admin123' will be hashed using password_hash()
-- Example of what the hashed password might look like:
-- '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'

-- Step 3: Add a migration tracking table (optional, for audit purposes)
CREATE TABLE IF NOT EXISTS `admin_password_migrations` (
    `migration_id` INT AUTO_INCREMENT PRIMARY KEY,
    `admin_id` INT NOT NULL,
    `migration_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `migration_type` VARCHAR(50) DEFAULT 'password_hash_upgrade',
    `status` ENUM('completed', 'failed') DEFAULT 'completed',
    FOREIGN KEY (`admin_id`) REFERENCES `admin`(`AdminID`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Step 4: Create backup of original admin table (recommended before migration)
-- This will be handled by the PHP migration script

-- Note: The actual password hashing will be performed by the PHP migration script
-- to ensure proper use of password_hash() function with PASSWORD_DEFAULT algorithm
