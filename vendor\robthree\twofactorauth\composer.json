{"name": "rob<PERSON><PERSON>/twofactor<PERSON>h", "description": "Two Factor Authentication", "type": "library", "keywords": ["Authentication", "Two Factor Authentication", "Multi Factor Authentication", "TFA", "MFA", "PHP", "Authenticator", "<PERSON><PERSON>"], "homepage": "https://github.com/RobThree/TwoFactorAuth", "license": "MIT", "authors": [{"name": "<PERSON>", "homepage": "http://robiii.me", "role": "Developer"}, {"name": "<PERSON>", "homepage": "https://github.com/NicolasCARPi", "role": "Developer"}, {"name": "Will Power", "homepage": "https://github.com/willpower232", "role": "Developer"}], "support": {"issues": "https://github.com/RobThree/TwoFactorAuth/issues", "source": "https://github.com/RobThree/TwoFactorAuth"}, "require": {"php": ">=8.2.0"}, "require-dev": {"phpunit/phpunit": "^9", "friendsofphp/php-cs-fixer": "^3.13", "phpstan/phpstan": "^1.9"}, "suggest": {"bacon/bacon-qr-code": "Needed for BaconQrCodeProvider provider", "endroid/qr-code": "Needed for EndroidQrCodeProvider"}, "autoload": {"psr-4": {"RobThree\\Auth\\": "lib"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"phpstan": ["phpstan analyze --xdebug lib tests testsDependency"], "lint": ["php-cs-fixer fix -v"], "lint-ci": ["PHP_CS_FIXER_IGNORE_ENV=1 php-cs-fixer fix -v --dry-run --stop-on-violation"], "test": ["XDEBUG_MODE=coverage phpunit"]}}