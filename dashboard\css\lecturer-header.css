/* ===================================================================
   LECTURER HEADER STYLES
   Professional header component for lecturer dashboard pages
   ================================================================= */

/* CSS Custom Properties for Header */
:root {
  --header-bg: #ffffff;
  --header-border: #e2e8f0;
  --header-text: #1e293b;
  --header-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --header-padding: 1rem 2rem;
  --logo-height: 36px;
  --user-badge-bg: #f8fafc;
  --user-badge-border: #e2e8f0;
  --user-badge-text: #1e293b;
  --transition-smooth: all 0.3s ease;
}

/* Header Container */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--header-bg);
  border-bottom: 1px solid var(--header-border);
  box-shadow: var(--header-shadow);
  position: relative;
  z-index: 100;
  padding: var(--header-padding);
  min-height: 70px;
}

/* Header Left Section */
.header-left {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
}

.header-left .logo {
  height: var(--logo-height);
  transition: var(--transition-smooth);
  cursor: pointer;
}

.header-left .logo:hover {
  transform: scale(1.05);
}

/* Header Right Section */
.header-right {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
}

/* User ID Badge */
.user-id {
  background: var(--user-badge-bg);
  color: var(--user-badge-text);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: 1px solid var(--user-badge-border);
  font-weight: 500;
  font-size: 0.875rem;
  transition: var(--transition-smooth);
}

.user-id:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

/* QR Button (if needed for other pages) */
.qr-button {
  background: #6366f1;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  margin-right: 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.875rem;
  transition: var(--transition-smooth);
}

.qr-button:hover {
  background: #4f46e5;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header {
    padding: 1rem;
  }
  
  .header-left .logo {
    height: 32px;
  }
  
  .user-id {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .qr-button {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
    margin-right: 0.5rem;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 0.75rem;
  }
  
  .header-left .logo {
    height: 28px;
  }
  
  .user-id {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
}
