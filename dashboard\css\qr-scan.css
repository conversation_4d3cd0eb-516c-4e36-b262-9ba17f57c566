/* ───────────── ADDITIONAL CSS VARIABLES ───────────── */
    :root {
      --primary-gradient: linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%);
      --radius-xl: 16px;
      --space-xs: 4px;
      --space-2xl: 32px;
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --transition: 200ms ease-in-out;
    }

    /* ───────────── PAGE‐SPECIFIC STYLES ───────────── */
    .page-header-qr {
      background: var(--white);
      border-radius: var(--radius-xl);
      padding: var(--space-xl);
      margin-bottom: var(--space-xl);
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--gray-200);
      position: relative;
      overflow: hidden;
    }
    .page-header-qr::before {
      content: '';
      position: absolute;
      top: 0; left: 0; right: 0;
      height: 4px;
      background: var(--primary-gradient);
    }
    .page-header-qr h1 {
      font-size: var(--text-3xl);
      font-weight: 700;
      color: var(--gray-900);
      margin: 0 0 var(--space-sm) 0;
      background: var(--primary-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      display: flex;
      align-items: center;
      gap: var(--space-sm);
    }
    .page-header-qr p {
      font-size: var(--text-base);
      color: var(--gray-600);
      margin: 0;
      font-weight: 400;
    }

    /* Card to hold QR reader */
    .qr-card {
      background: var(--white);
      border-radius: var(--radius-lg);
      padding: var(--space-xl);
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--gray-200);
      text-align: center;
    }
    #qr-reader {
      width: 100%;
      max-width: 360px;
      margin: var(--space-md) auto;
    }
    #result {
      margin-top: var(--space-md);
      color: var(--success);
      font-size: var(--text-base);
      font-weight: 500;
    }

    /* Enhanced Modal Styling (Azia Template Design) */
    .modal {
      display: none;
      position: fixed;
      top: 0; left: 0;
      width: 100%; height: 100%;
      background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6));
      backdrop-filter: blur(8px);
      justify-content: center;
      align-items: center;
      z-index: 9999;
      animation: modalFadeIn 0.3s ease-out;
    }

    @keyframes modalFadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    @keyframes modalSlideIn {
      from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
      }
      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    .modal-content {
      background: var(--white);
      border-radius: var(--radius-2xl);
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1);
      max-width: 480px;
      width: 90%;
      max-height: 90vh;
      overflow-y: auto;
      position: relative;
      animation: modalSlideIn 0.3s ease-out;
      border: 1px solid var(--gray-200);
    }

    .modal-header {
      padding: var(--space-2xl) var(--space-2xl) var(--space-lg);
      text-align: center;
      border-bottom: 1px solid var(--gray-100);
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
    }

    .modal-icon {
      width: 64px;
      height: 64px;
      margin: 0 auto var(--space-lg);
      background: linear-gradient(135deg, var(--success) 0%, #10b981 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 10px 25px rgba(34, 197, 94, 0.3);
    }

    .modal-icon.error {
      background: linear-gradient(135deg, var(--danger) 0%, #dc2626 100%);
      box-shadow: 0 10px 25px rgba(220, 38, 38, 0.3);
    }

    .modal-icon i {
      font-size: 1.75rem;
      color: var(--white);
      margin: 0;
    }

    .modal-title {
      font-size: var(--text-xl);
      font-weight: 700;
      color: var(--gray-900);
      margin: 0 0 var(--space-sm);
      line-height: 1.3;
    }

    .modal-subtitle {
      font-size: var(--text-sm);
      color: var(--gray-600);
      margin: 0;
      font-weight: 500;
    }

    .modal-body {
      padding: var(--space-xl) var(--space-2xl);
    }

    .modal-message {
      font-size: var(--text-base);
      color: var(--gray-700);
      line-height: 1.6;
      margin-bottom: var(--space-lg);
      text-align: center;
    }

    .modal-details {
      background: var(--gray-50);
      border-radius: var(--radius-lg);
      padding: var(--space-lg);
      margin: var(--space-lg) 0;
      border-left: 4px solid var(--success);
    }

    .modal-details.error {
      background: #fef2f2;
      border-left-color: var(--danger);
    }

    .modal-help {
      background: #f0f9ff;
      border-radius: var(--radius-lg);
      padding: var(--space-lg);
      margin: var(--space-lg) 0;
      border-left: 4px solid var(--info);
    }

    .modal-help p {
      margin: 0 0 var(--space-sm);
      font-weight: 600;
      color: var(--gray-700);
      font-size: var(--text-sm);
    }

    .modal-help ul {
      margin: 0;
      padding-left: var(--space-lg);
      color: var(--gray-600);
      font-size: var(--text-sm);
    }

    .modal-help li {
      margin-bottom: var(--space-xs);
      line-height: 1.5;
    }

    .modal-detail-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--space-sm) 0;
      border-bottom: 1px solid var(--gray-200);
    }

    .modal-detail-item:last-child {
      border-bottom: none;
    }

    .modal-detail-label {
      font-weight: 600;
      color: var(--gray-600);
      font-size: var(--text-sm);
    }

    .modal-detail-value {
      font-weight: 500;
      color: var(--gray-900);
      font-size: var(--text-sm);
      text-align: right;
      max-width: 60%;
      word-break: break-word;
    }

    .modal-buttons {
      display: flex;
      gap: var(--space-md);
      justify-content: center;
      padding: var(--space-lg) var(--space-2xl) var(--space-2xl);
      background: var(--gray-50);
      border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
      border-top: 1px solid var(--gray-100);
    }

    .modal-button {
      padding: var(--space-md) var(--space-xl);
      border: none;
      border-radius: var(--radius-lg);
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      font-size: var(--text-sm);
      min-width: 120px;
      position: relative;
      overflow: hidden;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .modal-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    .modal-button:hover::before {
      left: 100%;
    }

    .modal-button.primary {
      background: linear-gradient(135deg, var(--success) 0%, #10b981 100%);
      color: var(--white);
      box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
    }

    .modal-button.primary:hover {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);
    }

    .modal-button.secondary {
      background: linear-gradient(135deg, var(--warning) 0%, #f59e0b 100%);
      color: var(--white);
      box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
    }

    .modal-button.secondary:hover {
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
    }

    .modal-button:active {
      transform: translateY(0);
    }

    .modal-button:disabled {
      background: var(--gray-400) !important;
      color: var(--gray-600) !important;
      cursor: not-allowed;
      transform: none !important;
      box-shadow: none !important;
    }

    .modal-button.loading {
      position: relative;
      color: transparent !important;
    }

    .modal-button.loading::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 16px;
      height: 16px;
      margin: -8px 0 0 -8px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      color: var(--white);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .close-button {
      background: var(--gray-100);
      border: none;
      position: absolute;
      top: var(--space-lg);
      right: var(--space-lg);
      width: 32px;
      height: 32px;
      border-radius: 50%;
      font-size: var(--text-lg);
      color: var(--gray-500);
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      z-index: 10;
    }

    .close-button:hover {
      background: var(--gray-200);
      color: var(--gray-700);
      transform: scale(1.1);
    }

    .close-button:active {
      transform: scale(0.95);
    }

    /* Modal Progress Indicator */
    .modal-progress {
      margin: var(--space-lg) 0;
      padding: var(--space-md);
      background: var(--gray-50);
      border-radius: var(--radius-lg);
      border-left: 4px solid var(--info);
    }

    .modal-progress.success {
      border-left-color: var(--success);
      background: #f0fdf4;
    }

    .modal-progress.error {
      border-left-color: var(--danger);
      background: #fef2f2;
    }

    .modal-progress.warning {
      border-left-color: var(--warning);
      background: #fffbeb;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .modal-content {
        width: 95%;
        margin: var(--space-md);
        max-height: 95vh;
      }

      .modal-header {
        padding: var(--space-lg) var(--space-lg) var(--space-md);
      }

      .modal-body {
        padding: var(--space-lg);
      }

      .modal-buttons {
        padding: var(--space-md) var(--space-lg) var(--space-lg);
        flex-direction: column;
        gap: var(--space-sm);
      }

      .modal-button {
        width: 100%;
        min-width: auto;
      }

      .modal-detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-xs);
      }

      .modal-detail-value {
        max-width: 100%;
        text-align: left;
      }

      .close-button {
        top: var(--space-md);
        right: var(--space-md);
        width: 28px;
        height: 28px;
        font-size: var(--text-base);
      }
    }

    @media (max-width: 480px) {
      .modal-content {
        width: 98%;
        margin: var(--space-sm);
      }

      .modal-icon {
        width: 48px;
        height: 48px;
      }

      .modal-icon i {
        font-size: 1.5rem;
      }

      .modal-title {
        font-size: var(--text-lg);
      }

      .modal-subtitle {
        font-size: var(--text-xs);
      }
    }

    /* Status messages */
    .status-message {
      margin-top: var(--space-md);
      padding: var(--space-md);
      border-radius: var(--radius-lg);
      font-size: var(--text-sm);
      font-weight: 500;
      line-height: 1.5;
      border: 1px solid;
      display: flex;
      align-items: flex-start;
      gap: var(--space-sm);
    }
    .status-message i {
      margin-top: 2px;
      flex-shrink: 0;
    }
    .status-success {
      background-color: #F0FDF4;
      color: #166534;
      border-color: #22C55E;
    }
    .status-error {
      background-color: #FEF2F2;
      color: #DC2626;
      border-color: #EF4444;
    }
    .status-warning {
      background-color: #FFFBEB;
      color: #D97706;
      border-color: #F59E0B;
    }

    /* Blockchain status container */
    #blockchain-status {
      margin-top: var(--space-lg);
    }

    /* Debug section styling */
    .debug-section {
      margin-top: var(--space-xl);
      padding: var(--space-lg);
      border: 2px dashed var(--gray-300);
      border-radius: var(--radius-lg);
      background-color: var(--gray-50);
    }
    .debug-section h4 {
      color: var(--gray-700);
      margin-bottom: var(--space-md);
      font-size: var(--text-lg);
      display: flex;
      align-items: center;
      gap: var(--space-sm);
    }
    #debug-info {
      margin-top: var(--space-md);
      padding: var(--space-md);
      background-color: var(--white);
      border-radius: var(--radius-md);
      border: 1px solid var(--gray-200);
      font-family: 'Courier New', monospace;
      font-size: var(--text-xs);
      line-height: 1.6;
      color: var(--gray-700);
      min-height: 60px;
    }

    /* Enhanced action buttons */
    .action-button {
      background: var(--primary-gradient);
      color: var(--white);
      padding: var(--space-sm) var(--space-lg);
      border: none;
      border-radius: var(--radius-lg);
      cursor: pointer;
      font-size: var(--text-sm);
      font-weight: 600;
      margin: var(--space-xs);
      transition: all var(--transition);
      display: inline-flex;
      align-items: center;
      gap: var(--space-xs);
      box-shadow: var(--shadow-sm);
    }
    .action-button:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }
    .action-button:disabled {
      background: var(--gray-400);
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
    .action-button i {
      font-size: var(--text-sm);
    }