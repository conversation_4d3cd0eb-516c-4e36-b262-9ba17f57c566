/* ===================================================================
   BLOCKCHAIN VERIFICATION STYLES
   Specific styles for the blockchain verification page
   Matches attendance-report-styles.css design patterns exactly
   ================================================================= */

/* Page Header - Identical to attendance report */
.page-header {
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.page-title {
  color: var(--text-primary);
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 var(--spacing-xs) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.page-title::before {
  content: '\f3ed';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  color: var(--primary-color);
  font-size: 1.5rem;
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0;
  font-weight: 400;
}

/* Verification Container - Matches attendance report container */
.verification-container {
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

/* Enhanced Messages - Identical to attendance report */
.msg {
  margin: 1.5rem 2rem;
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  border-left: 4px solid;
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.msg.error {
  background: #fef2f2;
  color: #991b1b;
  border-left-color: var(--danger-color);
}

.msg.error::before {
  content: '\f071';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
}

.msg.success {
  background: #f0fdf4;
  color: #166534;
  border-left-color: var(--success-color);
}

.msg.success::before {
  content: '\f00c';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
}

.msg.info {
  background: #f0f9ff;
  color: #0c4a6e;
  border-left-color: var(--info-color);
}

.msg.info::before {
  content: '\f05a';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
}

/* Verification Form Section - Matches filter section styling */
.verification-form-section {
  background: var(--card-bg);
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  margin: 0;
}

.verification-form {
  display: flex;
  align-items: end;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.form-group label {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.form-group label[for="record_id"]::before {
  content: '\f02d';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  color: var(--primary-color);
  font-size: 1rem;
}

.form-group label[for="verification_type"]::before {
  content: '\f0b0';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  color: var(--primary-color);
  font-size: 1rem;
}

.form-group input,
.form-group select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--card-bg);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.3s ease;
  font-weight: 500;
  min-width: 220px;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.verify-button {
  background: var(--primary-color);
  color: var(--card-bg);
  border: none;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.875rem;
}

.verify-button::before {
  content: '\f3ed';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
}

.verify-button:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Verification Results Section */
.verification-results {
  padding: var(--spacing-lg);
  background: var(--card-bg);
}

.results-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.results-header h3 {
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.results-header::before {
  content: '\f0ca';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  color: var(--primary-color);
  font-size: 1.25rem;
}

/* Verification Status Cards */
.verification-status {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.status-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

.status-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.status-card.verified {
  border-left: 4px solid var(--success-color);
  background: #f0fdf4;
}

.status-card.failed {
  border-left: 4px solid var(--danger-color);
  background: #fef2f2;
}

.status-card.pending {
  border-left: 4px solid var(--warning-color);
  background: #fffbeb;
}

.status-card-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.status-card-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
  margin: 0;
}

.status-card-icon {
  font-size: 1rem;
}

.status-card.verified .status-card-icon {
  color: var(--success-color);
}

.status-card.failed .status-card-icon {
  color: var(--danger-color);
}

.status-card.pending .status-card-icon {
  color: var(--warning-color);
}

.status-card-content {
  color: var(--text-secondary);
  font-size: 0.8rem;
  line-height: 1.4;
}

/* Blockchain Details Table - Matches attendance table styling */
.blockchain-details {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  overflow: hidden;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.details-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin: 0;
  background: var(--card-bg);
  font-size: 0.875rem;
}

.details-table th {
  background: #f8fafc;
  color: #64748b;
  padding: 1rem 0.75rem;
  text-align: left;
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 2px solid var(--border-color);
}

.details-table th:first-child {
  padding-left: 1.5rem;
}

.details-table th:last-child {
  padding-right: 1.5rem;
}

.details-table td {
  padding: 1.25rem 0.75rem;
  text-align: left;
  border-bottom: 1px solid #f1f5f9;
  vertical-align: middle;
  color: var(--text-primary);
  background: var(--card-bg);
  transition: all 0.2s ease;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
}

.details-table td:first-child {
  padding-left: 1.5rem;
  font-family: inherit;
  font-weight: 500;
}

.details-table td:last-child {
  padding-right: 1.5rem;
}

.details-table tr:last-child td {
  border-bottom: none;
}

/* Hash Display */
.hash-display {
  background: #f1f5f9;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  word-break: break-all;
  border: 1px solid var(--border-color);
  line-height: 1.4;
}

/* Enhanced Responsive Design - Matches attendance report */
@media (max-width: 1200px) {
  .verification-status {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 1024px) {
  .main-content {
    padding: var(--spacing-md);
  }

  .page-header {
    padding: var(--spacing-md);
  }

  .verification-form-section {
    padding: var(--spacing-md);
  }

  .verification-results {
    padding: var(--spacing-md);
  }

  .verification-form {
    gap: var(--spacing-sm);
  }

  .form-group input,
  .form-group select {
    min-width: 180px;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: var(--spacing-sm);
  }

  .page-header {
    padding: var(--spacing-sm);
  }

  .page-title {
    font-size: 1.5rem;
  }

  .verification-form-section {
    padding: var(--spacing-sm);
  }

  .verification-results {
    padding: var(--spacing-sm);
  }

  .verification-form {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .form-group input,
  .form-group select {
    min-width: auto;
  }

  .verification-status {
    grid-template-columns: 1fr;
  }

  .details-table th,
  .details-table td {
    padding: 0.75rem 0.375rem;
    font-size: 0.75rem;
  }

  .details-table th:first-child,
  .details-table td:first-child {
    padding-left: 1rem;
  }

  .details-table th:last-child,
  .details-table td:last-child {
    padding-right: 1rem;
  }

  .hash-display {
    font-size: 0.625rem;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.25rem;
  }

  .verification-form-section,
  .verification-results {
    padding: var(--spacing-xs);
  }

  .msg {
    margin: 1rem;
    padding: 0.75rem 1rem;
  }

  .details-table th,
  .details-table td {
    padding: 0.5rem 0.25rem;
    font-size: 0.625rem;
  }

  .details-table th:first-child,
  .details-table td:first-child {
    padding-left: 0.75rem;
  }

  .details-table th:last-child,
  .details-table td:last-child {
    padding-right: 0.75rem;
  }
}

/* Loading Animation */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(99, 102, 241, 0.3);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Print Styles - Matches attendance report */
@media print {
  .header, .sidebar, .verification-form-section, footer {
    display: none !important;
  }

  .main-content {
    padding: 0;
    background: white;
  }

  .page-header {
    margin-bottom: 1rem;
  }

  .verification-container {
    box-shadow: none;
    border: 1px solid #000;
  }

  .details-table {
    font-size: 0.75rem;
  }

  .details-table th,
  .details-table td {
    padding: 0.5rem 0.25rem;
    border: 1px solid #000;
  }

  .status-card {
    border: 1px solid #000;
    margin-bottom: 1rem;
  }
}
