<?php
/**
 * First-Time Login Flow Test Script
 * Tests the complete first-time login authentication and email workflow
 */

require_once 'config/config.php';
require_once 'includes/PasswordResetService.php';
require_once 'includes/EmailService.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>First-Time Login Flow Test</h2>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
    .test-section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
    .form-section { background: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0; }
    input, select { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
    button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
    button:hover { background: #005a87; }
</style>";

// Handle test user creation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_test_user'])) {
    $userType = $_POST['user_type'];
    $testEmail = trim($_POST['test_email']);
    $testName = trim($_POST['test_name']);
    
    if (empty($testEmail) || empty($testName)) {
        echo "<div class='test-section'><p class='error'>Please fill in both email and name fields.</p></div>";
    } else {
        echo "<div class='test-section'>";
        echo "<h3>Creating Test User</h3>";
        
        try {
            if ($userType === 'student') {
                // Get next student ID
                $result = $conn->query("SELECT MAX(CAST(SUBSTRING(StudentID, 3) AS UNSIGNED)) as max_id FROM student WHERE StudentID LIKE 'AI%'");
                $row = $result->fetch_assoc();
                $nextId = $row['max_id'] ? $row['max_id'] + 1 : 250001;
                $studentId = 'AI' . str_pad($nextId, 6, '0', STR_PAD_LEFT);
                
                // Create test student with FirstLogin = 1
                $defaultPassword = password_hash('password123', PASSWORD_DEFAULT);
                $stmt = $conn->prepare("INSERT INTO student (StudentID, Name, Email, Password, FirstLogin) VALUES (?, ?, ?, ?, 1)");
                $stmt->bind_param("ssss", $studentId, $testName, $testEmail, $defaultPassword);
                
                if ($stmt->execute()) {
                    echo "<p class='success'>✓ Test student created successfully!</p>";
                    echo "<p class='info'><strong>Student ID:</strong> $studentId</p>";
                    echo "<p class='info'><strong>Name:</strong> $testName</p>";
                    echo "<p class='info'><strong>Email:</strong> $testEmail</p>";
                    echo "<p class='info'><strong>Default Password:</strong> password123</p>";
                    echo "<p class='info'><strong>FirstLogin:</strong> Yes (1)</p>";
                } else {
                    echo "<p class='error'>✗ Failed to create test student: " . $conn->error . "</p>";
                }
                
            } else {
                // Get next lecturer ID
                $result = $conn->query("SELECT MAX(CAST(LectID AS UNSIGNED)) as max_id FROM lecturer");
                $row = $result->fetch_assoc();
                $nextId = $row['max_id'] ? $row['max_id'] + 1 : 1;
                $lectId = str_pad($nextId, 5, '0', STR_PAD_LEFT);
                
                // Create test lecturer with FirstLogin = 1
                $defaultPassword = password_hash('password123', PASSWORD_DEFAULT);
                $stmt = $conn->prepare("INSERT INTO lecturer (LectID, Name, Email, Password, FirstLogin) VALUES (?, ?, ?, ?, 1)");
                $stmt->bind_param("ssss", $lectId, $testName, $testEmail, $defaultPassword);
                
                if ($stmt->execute()) {
                    echo "<p class='success'>✓ Test lecturer created successfully!</p>";
                    echo "<p class='info'><strong>Lecturer ID:</strong> $lectId</p>";
                    echo "<p class='info'><strong>Name:</strong> $testName</p>";
                    echo "<p class='info'><strong>Email:</strong> $testEmail</p>";
                    echo "<p class='info'><strong>Default Password:</strong> password123</p>";
                    echo "<p class='info'><strong>FirstLogin:</strong> Yes (1)</p>";
                } else {
                    echo "<p class='error'>✗ Failed to create test lecturer: " . $conn->error . "</p>";
                }
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>✗ Exception: " . $e->getMessage() . "</p>";
        }
        
        echo "</div>";
    }
}

// Handle first-time login simulation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_login'])) {
    $username = trim($_POST['username']);
    $password = trim($_POST['password']);
    
    echo "<div class='test-section'>";
    echo "<h3>Testing First-Time Login Flow</h3>";
    echo "<p class='info'>Testing login for: <strong>$username</strong></p>";
    
    try {
        // Simulate the login process from index.php
        $foundUser = false;
        $userRole = '';
        $userData = null;
        
        // Check lecturer table
        $queryLect = $conn->prepare("SELECT * FROM lecturer WHERE LectID = ?");
        $queryLect->bind_param("s", $username);
        $queryLect->execute();
        $resultLect = $queryLect->get_result();
        
        if ($resultLect->num_rows === 1) {
            $userData = $resultLect->fetch_assoc();
            $userRole = 'lecturer';
            $foundUser = true;
            echo "<p class='success'>✓ User found in lecturer table</p>";
        }
        
        // Check student table if not found in lecturer
        if (!$foundUser) {
            $queryStudent = $conn->prepare("SELECT * FROM student WHERE StudentID = ?");
            $queryStudent->bind_param("s", $username);
            $queryStudent->execute();
            $resultStudent = $queryStudent->get_result();
            
            if ($resultStudent->num_rows === 1) {
                $userData = $resultStudent->fetch_assoc();
                $userRole = 'student';
                $foundUser = true;
                echo "<p class='success'>✓ User found in student table</p>";
            }
        }
        
        if (!$foundUser) {
            echo "<p class='error'>✗ User not found in any table</p>";
        } else {
            echo "<p class='info'>User Role: $userRole</p>";
            echo "<p class='info'>FirstLogin Status: " . ($userData['FirstLogin'] ? 'Yes (1)' : 'No (0)') . "</p>";
            
            // Test password verification
            if (password_verify($password, $userData['Password'])) {
                echo "<p class='success'>✓ Password verification successful</p>";
                
                if ($userData['FirstLogin']) {
                    echo "<p class='success'>✓ First-time login detected - should redirect to password reset</p>";
                    
                    // Test email sending
                    echo "<h4>Testing Email Sending</h4>";
                    $passwordResetService = new PasswordResetService($conn);
                    
                    $userId = $userRole === 'student' ? $userData['StudentID'] : $userData['LectID'];
                    $userName = $userData['Name'];
                    $userEmail = $userData['Email'];
                    
                    echo "<p class='info'>Attempting to send password reset email...</p>";
                    echo "<p class='info'>User ID: $userId</p>";
                    echo "<p class='info'>User Name: $userName</p>";
                    echo "<p class='info'>User Email: $userEmail</p>";
                    
                    $result = $passwordResetService->initiatePasswordReset($userId, $userRole, $userEmail, $userName);
                    
                    if ($result['success']) {
                        echo "<p class='success'>✓ Password reset email sent successfully!</p>";
                        echo "<p class='info'>Message: " . $result['message'] . "</p>";
                        if (isset($result['token'])) {
                            echo "<p class='info'>Reset Token: " . substr($result['token'], 0, 20) . "...</p>";
                        }
                    } else {
                        echo "<p class='error'>✗ Failed to send password reset email</p>";
                        echo "<p class='error'>Error: " . $result['message'] . "</p>";
                    }
                    
                } else {
                    echo "<p class='warning'>⚠ Not a first-time login - would redirect to dashboard</p>";
                }
                
            } else {
                echo "<p class='error'>✗ Password verification failed</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>✗ Exception during login test: " . $e->getMessage() . "</p>";
    }
    
    echo "</div>";
}

// Test user creation form
echo "<div class='form-section'>";
echo "<h3>📝 Create Test User</h3>";
echo "<p>Create a test user with FirstLogin = 1 to test the first-time login flow.</p>";
echo "<form method='POST'>";
echo "<label for='user_type'>User Type:</label>";
echo "<select id='user_type' name='user_type' required>";
echo "<option value='student'>Student</option>";
echo "<option value='lecturer'>Lecturer</option>";
echo "</select>";
echo "<label for='test_name'>Full Name:</label>";
echo "<input type='text' id='test_name' name='test_name' placeholder='Enter full name' required>";
echo "<label for='test_email'>Email Address:</label>";
echo "<input type='email' id='test_email' name='test_email' placeholder='Enter email address' required>";
echo "<button type='submit' name='create_test_user'>📝 Create Test User</button>";
echo "</form>";
echo "</div>";

// Login test form
echo "<div class='form-section'>";
echo "<h3>🔐 Test First-Time Login</h3>";
echo "<p>Test the first-time login flow with a user account.</p>";
echo "<form method='POST'>";
echo "<label for='username'>Username (Student ID / Lecturer ID):</label>";
echo "<input type='text' id='username' name='username' placeholder='Enter user ID' required>";
echo "<label for='password'>Password:</label>";
echo "<input type='password' id='password' name='password' placeholder='Enter password' required>";
echo "<button type='submit' name='test_login'>🔐 Test Login Flow</button>";
echo "</form>";
echo "</div>";

// System status
echo "<div class='test-section'>";
echo "<h3>🔧 System Status</h3>";
echo "<p><strong>Database Connection:</strong> " . ($conn ? "✓ Connected" : "✗ Failed") . "</p>";
echo "<p><strong>PasswordResetService:</strong> " . (class_exists('PasswordResetService') ? "✓ Available" : "✗ Missing") . "</p>";
echo "<p><strong>EmailService:</strong> " . (class_exists('EmailService') ? "✓ Available" : "✗ Missing") . "</p>";

// Check if password reset files exist
$files = [
    'modules/password_reset_email.php',
    'modules/password_reset_form.php',
    'email_delivery_guide.php'
];

foreach ($files as $file) {
    echo "<p><strong>$file:</strong> " . (file_exists($file) ? "✓ Exists" : "✗ Missing") . "</p>";
}

echo "</div>";

echo "<p><a href='index.php'>← Back to Login</a> | <a href='test_email.php'>🔧 Test Email System</a></p>";
?>
