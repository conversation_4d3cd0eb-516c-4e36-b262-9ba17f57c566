/* ===================================================================
   BASE STYLES
   Essential global styles and layout foundation
   ================================================================= */

/* CSS Custom Properties - Global */
:root {
  /* Colors */
  --primary-color: #6366f1;
  --primary-light: #818cf8;
  --primary-dark: #4f46e5;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #06b6d4;
  
  /* Backgrounds */
  --light-bg: #f8fafc;
  --card-bg: #ffffff;
  --border-color: #e2e8f0;
  
  /* Text Colors */
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  
  /* Border Radius */
  --border-radius: 0.75rem;
  --border-radius-lg: 1rem;
  
  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
  
  /* Transitions */
  --transition-smooth: all 0.3s ease;
}

/* Global Reset */
*, *::before, *::after {
  box-sizing: border-box;
}

body, html {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--light-bg);
  height: 100%;
  font-size: 14px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Container Layout */
.container {
  display: flex;
  min-height: 100vh;
  overflow-y: auto;
  background: var(--light-bg);
}

/* Main Content Area */
.main-content {
  flex: 1;
  width: 80%;
  padding: var(--spacing-lg);
  overflow-y: auto;
  min-height: calc(100vh - 70px);
  background: var(--light-bg);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin: 0 0 var(--spacing-sm) 0;
  font-weight: 600;
  line-height: 1.2;
  color: var(--text-primary);
}

h1 {
  font-size: 1.875rem;
}

h2 {
  font-size: 1.5rem;
}

h3 {
  font-size: 1.25rem;
}

h4 {
  font-size: 1.125rem;
}

h5 {
  font-size: 1rem;
}

h6 {
  font-size: 0.875rem;
}

p {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-secondary);
}

/* Links */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition-smooth);
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Form Elements */
input, select, textarea, button {
  font-family: inherit;
  font-size: inherit;
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-muted {
  color: var(--text-muted);
}

.text-primary {
  color: var(--primary-color);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-danger {
  color: var(--danger-color);
}

/* Responsive Utilities */
@media (max-width: 1024px) {
  .main-content {
    width: 75%;
    padding: var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .container {
    flex-direction: column;
  }
  
  .main-content {
    width: 100%;
    padding: var(--spacing-sm);
    margin-left: 0;
  }
  
  body {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: var(--spacing-xs);
  }
  
  body {
    font-size: 12px;
  }
  
  h1 {
    font-size: 1.5rem;
  }
  
  h2 {
    font-size: 1.25rem;
  }
  
  h3 {
    font-size: 1.125rem;
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--light-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Focus Styles */
:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* Print Styles */
@media print {
  body {
    background: white;
    color: black;
  }
  
  .container {
    display: block;
  }
  
  .main-content {
    width: 100%;
    padding: 0;
  }
}
