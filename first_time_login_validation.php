<?php
/**
 * First-Time Login System Validation
 * Comprehensive validation of all components and integration
 */

require_once 'config/config.php';

echo "<h2>First-Time Login System Validation</h2>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .validation-section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
    .check-item { padding: 8px; margin: 5px 0; border-left: 4px solid #ddd; padding-left: 15px; }
    .check-pass { border-left-color: #28a745; background: #f8fff9; }
    .check-fail { border-left-color: #dc3545; background: #fff8f8; }
    .check-warn { border-left-color: #ffc107; background: #fffdf5; }
    .summary { background: #f0f8ff; padding: 20px; border-radius: 8px; margin: 20px 0; }
</style>";

$validationResults = [];
$totalChecks = 0;
$passedChecks = 0;

// Function to add validation result
function addValidation($category, $item, $status, $message) {
    global $validationResults, $totalChecks, $passedChecks;
    $validationResults[$category][] = [
        'item' => $item,
        'status' => $status,
        'message' => $message
    ];
    $totalChecks++;
    if ($status === 'pass') $passedChecks++;
}

// 1. File Structure Validation
echo "<div class='validation-section'>";
echo "<h3>📁 File Structure Validation</h3>";

$requiredFiles = [
    'modules/password_reset_email.php' => 'First-time login email page',
    'modules/password_reset_form.php' => 'Password reset form page',
    'email_delivery_guide.php' => 'Email delivery guide',
    'includes/PasswordResetService.php' => 'Password reset service',
    'includes/EmailService.php' => 'Email service',
    'config/config.php' => 'Configuration file'
];

foreach ($requiredFiles as $file => $description) {
    if (file_exists($file)) {
        addValidation('files', $description, 'pass', "File exists: $file");
        echo "<div class='check-item check-pass'>✓ $description</div>";
    } else {
        addValidation('files', $description, 'fail', "File missing: $file");
        echo "<div class='check-item check-fail'>✗ $description - File missing: $file</div>";
    }
}

echo "</div>";

// 2. Database Structure Validation
echo "<div class='validation-section'>";
echo "<h3>🗄️ Database Structure Validation</h3>";

$requiredTables = [
    'student' => ['StudentID', 'Name', 'Email', 'Password', 'FirstLogin'],
    'lecturer' => ['LectID', 'Name', 'Email', 'Password', 'FirstLogin'],
    'password_reset_tokens' => ['user_id', 'user_role', 'token', 'email', 'expires_at']
];

foreach ($requiredTables as $table => $columns) {
    $tableCheck = $conn->query("SHOW TABLES LIKE '$table'");
    if ($tableCheck->num_rows > 0) {
        addValidation('database', "Table: $table", 'pass', "Table exists");
        echo "<div class='check-item check-pass'>✓ Table: $table</div>";
        
        // Check columns
        foreach ($columns as $column) {
            $columnCheck = $conn->query("SHOW COLUMNS FROM $table LIKE '$column'");
            if ($columnCheck->num_rows > 0) {
                addValidation('database', "Column: $table.$column", 'pass', "Column exists");
                echo "<div class='check-item check-pass'>  ✓ Column: $column</div>";
            } else {
                addValidation('database', "Column: $table.$column", 'fail', "Column missing");
                echo "<div class='check-item check-fail'>  ✗ Column: $column - Missing</div>";
            }
        }
    } else {
        addValidation('database', "Table: $table", 'fail', "Table missing");
        echo "<div class='check-item check-fail'>✗ Table: $table - Missing</div>";
    }
}

echo "</div>";

// 3. Class and Service Validation
echo "<div class='validation-section'>";
echo "<h3>🔧 Service Validation</h3>";

try {
    if (class_exists('PasswordResetService')) {
        addValidation('services', 'PasswordResetService', 'pass', 'Class available');
        echo "<div class='check-item check-pass'>✓ PasswordResetService class available</div>";
        
        $passwordResetService = new PasswordResetService($conn);
        addValidation('services', 'PasswordResetService instantiation', 'pass', 'Can instantiate');
        echo "<div class='check-item check-pass'>✓ PasswordResetService can be instantiated</div>";
    } else {
        addValidation('services', 'PasswordResetService', 'fail', 'Class not found');
        echo "<div class='check-item check-fail'>✗ PasswordResetService class not found</div>";
    }
} catch (Exception $e) {
    addValidation('services', 'PasswordResetService instantiation', 'fail', $e->getMessage());
    echo "<div class='check-item check-fail'>✗ PasswordResetService instantiation failed: " . $e->getMessage() . "</div>";
}

try {
    if (class_exists('EmailService')) {
        addValidation('services', 'EmailService', 'pass', 'Class available');
        echo "<div class='check-item check-pass'>✓ EmailService class available</div>";
        
        $emailService = new EmailService();
        addValidation('services', 'EmailService instantiation', 'pass', 'Can instantiate');
        echo "<div class='check-item check-pass'>✓ EmailService can be instantiated</div>";
        
        // Test SMTP configuration
        $configTest = $emailService->testConfiguration();
        if ($configTest['success']) {
            addValidation('services', 'SMTP Configuration', 'pass', $configTest['message']);
            echo "<div class='check-item check-pass'>✓ SMTP Configuration: " . $configTest['message'] . "</div>";
        } else {
            addValidation('services', 'SMTP Configuration', 'fail', $configTest['message']);
            echo "<div class='check-item check-fail'>✗ SMTP Configuration: " . $configTest['message'] . "</div>";
        }
    } else {
        addValidation('services', 'EmailService', 'fail', 'Class not found');
        echo "<div class='check-item check-fail'>✗ EmailService class not found</div>";
    }
} catch (Exception $e) {
    addValidation('services', 'EmailService instantiation', 'fail', $e->getMessage());
    echo "<div class='check-item check-fail'>✗ EmailService instantiation failed: " . $e->getMessage() . "</div>";
}

echo "</div>";

// 4. Configuration Validation
echo "<div class='validation-section'>";
echo "<h3>⚙️ Configuration Validation</h3>";

$requiredConfigs = [
    'SMTP_HOST' => 'SMTP host configuration',
    'SMTP_PORT' => 'SMTP port configuration',
    'SMTP_USER' => 'SMTP user configuration',
    'SMTP_PASS' => 'SMTP password configuration',
    'SMTP_ENCRYPTION' => 'SMTP encryption configuration'
];

foreach ($requiredConfigs as $config => $description) {
    if (defined($config) && constant($config)) {
        addValidation('config', $description, 'pass', 'Configured');
        echo "<div class='check-item check-pass'>✓ $description</div>";
    } else {
        addValidation('config', $description, 'fail', 'Not configured');
        echo "<div class='check-item check-fail'>✗ $description - Not configured</div>";
    }
}

echo "</div>";

// 5. Design Consistency Validation
echo "<div class='validation-section'>";
echo "<h3>🎨 Design Consistency Validation</h3>";

$designFiles = [
    'modules/password_reset_email.php' => 'Password reset email page design',
    'modules/password_reset_form.php' => 'Password reset form page design',
    'modules/forgot_password.php' => 'Forgot password page design (reference)'
];

foreach ($designFiles as $file => $description) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        
        // Check for Azia template CSS
        if (strpos($content, 'base-styles.css') !== false && 
            strpos($content, 'lecturer-header.css') !== false) {
            addValidation('design', $description, 'pass', 'Uses Azia template CSS');
            echo "<div class='check-item check-pass'>✓ $description - Uses Azia template CSS</div>";
        } else {
            addValidation('design', $description, 'warn', 'May not use consistent CSS');
            echo "<div class='check-item check-warn'>⚠ $description - May not use consistent CSS</div>";
        }
        
        // Check for consistent styling patterns
        if (strpos($content, 'var(--') !== false) {
            addValidation('design', "$description variables", 'pass', 'Uses CSS variables');
            echo "<div class='check-item check-pass'>  ✓ Uses CSS variables</div>";
        } else {
            addValidation('design', "$description variables", 'warn', 'May not use CSS variables');
            echo "<div class='check-item check-warn'>  ⚠ May not use CSS variables</div>";
        }
    }
}

echo "</div>";

// 6. Integration Validation
echo "<div class='validation-section'>";
echo "<h3>🔗 Integration Validation</h3>";

// Check login page integration
if (file_exists('index.php')) {
    $loginContent = file_get_contents('index.php');
    
    if (strpos($loginContent, 'modules/password_reset_email.php') !== false) {
        addValidation('integration', 'Login page integration', 'pass', 'Redirects to password reset email page');
        echo "<div class='check-item check-pass'>✓ Login page redirects to password reset email page</div>";
    } else {
        addValidation('integration', 'Login page integration', 'fail', 'Missing redirect to password reset email page');
        echo "<div class='check-item check-fail'>✗ Login page missing redirect to password reset email page</div>";
    }
    
    if (strpos($loginContent, 'FirstLogin') !== false) {
        addValidation('integration', 'First-time login detection', 'pass', 'Checks FirstLogin flag');
        echo "<div class='check-item check-pass'>✓ Login page checks FirstLogin flag</div>";
    } else {
        addValidation('integration', 'First-time login detection', 'fail', 'Missing FirstLogin check');
        echo "<div class='check-item check-fail'>✗ Login page missing FirstLogin check</div>";
    }
}

echo "</div>";

// Summary
echo "<div class='summary'>";
echo "<h3>📊 Validation Summary</h3>";
echo "<p><strong>Total Checks:</strong> $totalChecks</p>";
echo "<p><strong>Passed:</strong> <span class='success'>$passedChecks</span></p>";
echo "<p><strong>Failed:</strong> <span class='error'>" . ($totalChecks - $passedChecks) . "</span></p>";

$successRate = round(($passedChecks / $totalChecks) * 100, 1);
echo "<p><strong>Success Rate:</strong> ";
if ($successRate >= 90) {
    echo "<span class='success'>$successRate%</span> - Excellent!";
} elseif ($successRate >= 75) {
    echo "<span class='warning'>$successRate%</span> - Good, minor issues";
} else {
    echo "<span class='error'>$successRate%</span> - Needs attention";
}
echo "</p>";

if ($successRate >= 90) {
    echo "<div class='check-item check-pass'>";
    echo "<strong>🎉 System Status: READY FOR PRODUCTION</strong><br>";
    echo "The first-time login system is fully functional and ready for use.";
    echo "</div>";
} elseif ($successRate >= 75) {
    echo "<div class='check-item check-warn'>";
    echo "<strong>⚠️ System Status: MOSTLY READY</strong><br>";
    echo "The system is functional but has minor issues that should be addressed.";
    echo "</div>";
} else {
    echo "<div class='check-item check-fail'>";
    echo "<strong>❌ System Status: NEEDS FIXES</strong><br>";
    echo "Critical issues found that must be resolved before production use.";
    echo "</div>";
}

echo "</div>";

echo "<p><a href='index.php'>← Back to Login</a> | <a href='test_complete_workflow.php'>🧪 Run Complete Test</a></p>";
?>
