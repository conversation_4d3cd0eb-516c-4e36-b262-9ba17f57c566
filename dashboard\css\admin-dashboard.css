/* ─────────────────────────────────────────────────────────────────────────────
   admin-dashboard.css
   - Admin-specific dashboard styling
   - Table column adjustments for better name display
   ───────────────────────────────────────────────────────────────────────────── */

/* ═══════════════════════════════════════════════════════════════════════════
   ADMIN DASHBOARD TABLE STYLING
   ═══════════════════════════════════════════════════════════════════════════ */

/* Column width adjustments for better name display */
.courses-table th:nth-child(1),
.courses-table td:nth-child(1) {
  width: 35%; /* More space for student names */
}

.courses-table th:nth-child(2),
.courses-table td:nth-child(2) {
  width: 25%; /* Course name column */
  text-align: center;
}

.courses-table th:nth-child(3),
.courses-table td:nth-child(3) {
  width: 15%; /* Section column */
  text-align: center;
}

.courses-table th:nth-child(4),
.courses-table td:nth-child(4) {
  width: 25%; /* Date column */
  text-align: center;
}

/* Enhanced table styling for admin dashboard */
.courses-table td {
  vertical-align: middle;
  white-space: nowrap; /* Prevent text wrapping */
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .courses-table th:nth-child(1),
  .courses-table td:nth-child(1) {
    width: 40%; /* Even more space for names on mobile */
  }
  
  .courses-table th:nth-child(2),
  .courses-table td:nth-child(2) {
    width: 30%;
  }
  
  .courses-table th:nth-child(3),
  .courses-table td:nth-child(3) {
    width: 15%;
  }
  
  .courses-table th:nth-child(4),
  .courses-table td:nth-child(4) {
    width: 15%;
  }
}
