<?php
session_start();
require '../config/config.php';

// 1) Only students
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header("Location: ../index.php");
    exit();
}

// ─────────────────────────────────────────────────────────────────────────────
// 2) DATABASE CAPABILITIES DETECTION
// ─────────────────────────────────────────────────────────────────────────────
function checkDatabaseCapabilities($conn) {
    $capabilities = [
        'course_code' => false,
        'course_status' => false,
        'course_assignments' => false,
        'registration_section' => false
    ];

    // Check if Course_Code column exists
    $checkCodeColumn = "SHOW COLUMNS FROM course LIKE 'Course_Code'";
    $result = $conn->query($checkCodeColumn);
    $capabilities['course_code'] = $result && $result->num_rows > 0;

    // Check if Status column exists
    $checkStatusColumn = "SHOW COLUMNS FROM course LIKE 'Status'";
    $result = $conn->query($checkStatusColumn);
    $capabilities['course_status'] = $result && $result->num_rows > 0;

    // Check if course_assignments table exists
    $checkAssignmentsTable = "SHOW TABLES LIKE 'course_assignments'";
    $result = $conn->query($checkAssignmentsTable);
    $capabilities['course_assignments'] = $result && $result->num_rows > 0;

    // Check if Section column exists in course_registration
    $checkSectionColumn = "SHOW COLUMNS FROM course_registration LIKE 'Section'";
    $result = $conn->query($checkSectionColumn);
    $capabilities['registration_section'] = $result && $result->num_rows > 0;

    return $capabilities;
}

$dbCapabilities = checkDatabaseCapabilities($conn);

// ─────────────────────────────────────────────────────────────────────────────
// 3) FETCH STUDENT INFO (for header/sidebar display)
// ─────────────────────────────────────────────────────────────────────────────
$student_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT Name, UserID, Photo FROM student WHERE StudentID = ?");
$stmt->bind_param("i", $student_id);
$stmt->execute();
$res = $stmt->get_result();
$student = $res->fetch_assoc();
$stmt->close();

$student_name   = $student['Name']   ?? 'Student Name';
$student_userid = $student['UserID'] ?? 'N/A';
$photo_url      = !empty($student['Photo'])
                 ? "../uploads/".rawurlencode($student['Photo'])
                 : "../assets/images/user1.png";

// ─────────────────────────────────────────────────────────────────────────────
// 4) ENHANCED QR CODE PROCESSING
// ─────────────────────────────────────────────────────────────────────────────
$message           = '';
$messageType       = '';
$newAttendanceID   = 0;
$scannedCourseID   = 0;
$scannedAssignmentID = 0;
$course_name       = '';
$course_code       = '';
$section_info      = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $qr_data    = $_POST['qr_data'] ?? '';
    parse_str($qr_data, $data);
    $course_id     = intval($data['course_id']     ?? 0);
    $lecturer_id   = intval($data['lecturer_id']   ?? 0);
    $assignment_id = intval($data['assignment_id'] ?? 0);
    $section       = $data['section'] ?? '';
    $token         = $data['token'] ?? '';
    $expires_at    = intval($data['expires_at'] ?? 0);

    $scannedCourseID = $course_id;
    $scannedAssignmentID = $assignment_id;

    // DEBUG: Log QR data for troubleshooting
    error_log("QR Scan Debug - Raw QR Data: " . $qr_data);
    error_log("QR Scan Debug - Parsed Data: " . print_r($data, true));
    error_log("QR Scan Debug - Course ID: " . $course_id . ", Lecturer ID: " . $lecturer_id . ", Assignment ID: " . $assignment_id . ", Section: " . $section);
    error_log("QR Scan Debug - Token: " . $token . ", Expires At: " . $expires_at . " (Current: " . time() . ")");
    error_log("QR Scan Debug - DB Capabilities: " . print_r($dbCapabilities, true));

    // ─────────────────────────────────────────────────────────────────────────────
    // 5) TOKEN AND EXPIRATION VALIDATION
    // ─────────────────────────────────────────────────────────────────────────────

    // Check if QR code has expired
    if ($expires_at > 0 && time() > $expires_at) {
        $message = "QR code has expired. Please ask your lecturer to generate a new one.";
        $messageType = "error";
        error_log("QR Scan Debug - QR code expired: " . date('Y-m-d H:i:s', $expires_at) . " vs current: " . date('Y-m-d H:i:s'));
    }

    // Check if token is empty (basic validation)
    elseif (empty($token)) {
        $message = "Invalid QR code format. Missing security token.";
        $messageType = "error";
        error_log("QR Scan Debug - Missing token in QR code");
    }

    // Check basic QR data integrity
    elseif ($course_id <= 0 || $lecturer_id <= 0) {
        $message = "Invalid QR code format. Missing course or lecturer information.";
        $messageType = "error";
        error_log("QR Scan Debug - Invalid course_id ($course_id) or lecturer_id ($lecturer_id)");
    }

    // ─────────────────────────────────────────────────────────────────────────────
    // 6) PROCEED WITH COURSE VALIDATION IF TOKEN IS VALID
    // ─────────────────────────────────────────────────────────────────────────────
    else {
        // Enhanced course validation with assignment support
        if ($dbCapabilities['course_assignments'] && $assignment_id > 0) {
        // New system: validate assignment-based QR code
        $cchk = $conn->prepare("
            SELECT
                c.Course_Name,
                " . ($dbCapabilities['course_code'] ? "c.Course_Code," : "c.CourseID as Course_Code,") . "
                ca.Section,
                ca.Status as AssignmentStatus
            FROM course_assignments ca
            JOIN course c ON ca.CourseID = c.CourseID
            WHERE ca.AssignmentID = ? AND ca.LectID = ? AND ca.Status = 'Active'
            " . ($dbCapabilities['course_status'] ? "AND c.Status = 'Active'" : "") . "
        ");
        $cchk->bind_param("ii", $assignment_id, $lecturer_id);
        $cchk->execute();
        $cres = $cchk->get_result();

        if ($cres->num_rows === 0) {
            $message = "Invalid course assignment or lecturer.";
            $messageType = "error";
        } else {
            $rowCourse = $cres->fetch_assoc();
            $course_name = $rowCourse['Course_Name'];
            $course_code = $rowCourse['Course_Code'];
            $section_info = $rowCourse['Section'];
        }
    } else {
        // Legacy system: validate course-based QR code
        $cchk = $conn->prepare("
            SELECT
                Course_Name" .
                ($dbCapabilities['course_code'] ? ", Course_Code" : ", CourseID as Course_Code") . "
            FROM course
            WHERE CourseID = ? AND LectID = ?
            " . ($dbCapabilities['course_status'] ? "AND Status = 'Active'" : "") . "
        ");
        $cchk->bind_param("ii", $course_id, $lecturer_id);
        $cchk->execute();
        $cres = $cchk->get_result();

        if ($cres->num_rows === 0) {
            $message = "Invalid course or lecturer.";
            $messageType = "error";
        } else {
            $rowCourse = $cres->fetch_assoc();
            $course_name = $rowCourse['Course_Name'];
            $course_code = $rowCourse['Course_Code'] ?? '';
        }
    }

    if ($messageType !== 'error') {

        // Enhanced registration check with section support
        if ($dbCapabilities['course_assignments'] && $dbCapabilities['registration_section'] && $assignment_id > 0) {
            // New system: check section-specific registration
            $rchk = $conn->prepare("
                SELECT 1 FROM course_registration cr
                JOIN course_assignments ca ON cr.CourseID = ca.CourseID AND cr.Section = ca.Section
                WHERE cr.StudentID = ? AND ca.AssignmentID = ? AND ca.Status = 'Active'
            ");
            $rchk->bind_param("ii", $student_id, $assignment_id);
        } else {
            // Legacy system or course-based registration
            $rchk = $conn->prepare("SELECT 1 FROM course_registration WHERE StudentID = ? AND CourseID = ?");
            $rchk->bind_param("ii", $student_id, $course_id);
        }

        $rchk->execute();
        $rres = $rchk->get_result();

        if ($rres->num_rows === 0) {
            if ($dbCapabilities['course_assignments'] && $assignment_id > 0) {
                $message = "You are not registered for this course section.";
            } else {
                $message = "You are not registered for this course.";
            }
            $messageType = "error";
        } else {
            // Enhanced attendance checking with assignment support
            if ($dbCapabilities['course_assignments'] && $assignment_id > 0) {
                // New system: check attendance for specific assignment
                $tchk = $conn->prepare("
                    SELECT ar.AttendanceID FROM attendance_report ar
                    WHERE ar.StudentID = ? AND ar.CourseID = ? AND ar.Att_Date = CURDATE()
                    AND EXISTS (
                        SELECT 1 FROM course_assignments ca
                        WHERE ca.AssignmentID = ? AND ca.CourseID = ar.CourseID AND ca.LectID = ar.LectID
                    )
                ");
                $tchk->bind_param("iii", $student_id, $course_id, $assignment_id);
            } else {
                // Legacy system: check attendance for course
                $tchk = $conn->prepare("
                    SELECT AttendanceID FROM attendance_report
                    WHERE StudentID = ? AND CourseID = ? AND Att_Date = CURDATE()
                ");
                $tchk->bind_param("ii", $student_id, $course_id);
            }

            $tchk->execute();
            $tres = $tchk->get_result();

            if ($tres->num_rows > 0) {
                $message = "You have already marked attendance today for this " .
                          ($dbCapabilities['course_assignments'] && $assignment_id > 0 ? "course section" : "course") . ".";
                $messageType = "error";
            } else {
                // Enhanced attendance recording
                if ($dbCapabilities['course_assignments'] && $assignment_id > 0) {
                    // New system: record with assignment reference
                    $ins = $conn->prepare("
                        INSERT INTO attendance_report
                            (StudentID, CourseID, LectID, Att_Date, Att_Status, Timestamp, Remark)
                        VALUES (?, ?, ?, CURDATE(), 'Present', NOW(), ?)
                    ");
                    // Check if section already contains "Section" to avoid duplication
                    $sectionDisplay = $section_info && strpos($section_info, 'Section') === 0 ? $section_info : "Section " . $section_info;
                    $remark = "Assignment ID: " . $assignment_id .
                             ($section_info ? " (" . $sectionDisplay . ")" : "");
                    $ins->bind_param("iiis", $student_id, $course_id, $lecturer_id, $remark);
                } else {
                    // Legacy system: record without assignment
                    $ins = $conn->prepare("
                        INSERT INTO attendance_report
                            (StudentID, CourseID, LectID, Att_Date, Att_Status, Timestamp)
                        VALUES (?, ?, ?, CURDATE(), 'Present', NOW())
                    ");
                    $ins->bind_param("iii", $student_id, $course_id, $lecturer_id);
                }

                if ($ins->execute()) {
                    $newAttendanceID = $ins->insert_id;
                    $successMsg = "Attendance scanned successfully!";
                    if ($section_info) {
                        // Check if section already contains "Section" to avoid duplication
                        $sectionDisplay = strpos($section_info, 'Section') === 0 ? $section_info : "Section " . $section_info;
                        $successMsg .= " (" . $sectionDisplay . ")";
                    }
                    $message = $successMsg;
                    $messageType = "success";
                } else {
                    $message = "Database error; please try again.";
                    $messageType = "error";
                }
                $ins->close();
            }
            $tchk->close();
        }
        $rchk->close();
    }
    $cchk->close();
    } // End of token validation else block
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Scan QR Code for Attendance</title>

  <!-- Your existing CSS -->
  <link rel="stylesheet" href="../dashboard/css/base-styles.css">
  <link rel="stylesheet" href="../dashboard/css/lecturer-header.css">
  <link rel="stylesheet" href="../dashboard/css/lecturer-sidebar.css">
  <link rel="stylesheet" href="../dashboard/css/lecturer-footer.css">
  <link rel="stylesheet" href="../dashboard/css/student-dashboard-enhanced.css">
  <link rel="stylesheet" href="../dashboard/css/qr-scan.css">
  
  <!-- FontAwesome, HTML5-QRCode & Web3 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="../assets/js/html5-qrcode.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/web3/dist/web3.min.js"></script>
</head>
<body>
  <!-- HEADER -->
  <div class="header">
    <div class="header-left">
      <img src="../assets/images/logo-uthm2.png" class="logo" alt="UTHM Logo">
    </div>
    <div class="header-right">
      <a href="qr_scan.php" class="qr-button"><i class="fas fa-qrcode"></i> Scan QR Code</a>
      <span class="user-id"><?= htmlspecialchars($student_userid) ?></span>
    </div>
  </div>

  <!-- SIDEBAR + MAIN -->
  <div class="container">
    <div class="sidebar">
      <div class="profile">
        <img src="<?= $photo_url ?>" class="profile-pic" alt="">
        <p class="profile-name"><?= htmlspecialchars($student_name) ?></p>
        <p class="profile-id"><?= htmlspecialchars($student_userid) ?></p>
      </div>
      <ul class="menu">
        <li><a href="../dashboard/student.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
        <li><a href="../modules/profile_student.php"><i class="fas fa-user"></i> Profile</a></li>
        <li><a href="register_courses.php"><i class="fas fa-edit"></i> Register Courses</a></li>
        <li><a href="report.php"><i class="fas fa-book"></i> Attendance Details</a></li>
        <li><a href="../dashboard/student_blockchain_records.php"><i class="fas fa-link"></i> Blockchain Records</a></li>
        <li><a href="../dashboard/student_transaction_lookup.php"><i class="fas fa-search"></i> Transaction Lookup</a></li>
        <li><a href="qr_scan.php" class="active"><i class="fas fa-qrcode"></i> QR Code Scan</a></li>
        <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
      </ul>
    </div>
    <div class="main-content">
      <div class="page-header-qr">
        <h1><i class="fas fa-qrcode"></i> Scan QR Code</h1>
        <p>Point your camera at the lecturer's QR code to mark your attendance.</p>
      </div>

      <div class="qr-card">
        <div id="qr-reader"></div>
        <div id="result"></div>

        <!-- Camera Controls -->
        <div class="camera-controls" style="margin-top: 15px; text-align: center;">
          <button onclick="restartCamera()" class="action-button" style="background: #6c757d;">
            <i class="fas fa-redo"></i> Restart Camera
          </button>
          <button onclick="checkCameraPermissions()" class="action-button" style="background: #ffc107; color: #000;">
            <i class="fas fa-camera"></i> Check Camera
          </button>
          <button onclick="toggleCameraMode()" class="action-button" style="background: #e83e8c;">
            <i class="fas fa-sync-alt"></i> Switch Camera
          </button>
        </div>

        <!-- Camera Status -->
        <div id="camera-status" style="margin-top: 10px; text-align: center; font-size: 14px;"></div>

        <!-- Manual QR Input for Testing -->
        <div class="manual-input-section" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
          <h4><i class="fas fa-keyboard"></i> Manual QR Input (For Testing)</h4>
          <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
            <input type="text" id="manual-qr-input" placeholder="Paste QR data here..." style="flex: 1; min-width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            <button onclick="submitManualQR()" class="action-button" style="background: #28a745;">
              <i class="fas fa-paper-plane"></i> Submit
            </button>
            <button onclick="testWithSampleQR()" class="action-button" style="background: #17a2b8;">
              <i class="fas fa-vial"></i> Test Valid QR
            </button>
            <button onclick="testWithExpiredQR()" class="action-button" style="background: #dc3545;">
              <i class="fas fa-clock"></i> Test Expired QR
            </button>
          </div>
          <small style="color: #666; margin-top: 5px; display: block;">
            Use this if camera scanning is not working. Paste QR data or click "Test Sample" to use sample data.
          </small>
        </div>

        <div id="blockchain-status"></div>

        <!-- Debug MetaMask Connection -->
        <!-- QR Scan Debug Info -->
        <?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
        <div class="debug-section" style="background: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 8px;">
          <h4><i class="fas fa-bug"></i> QR Scan Debug Information</h4>
          <div style="font-family: monospace; font-size: 12px;">
            <strong>Raw QR Data:</strong> <?= htmlspecialchars($qr_data) ?><br>
            <strong>Parsed Course ID:</strong> <?= $course_id ?><br>
            <strong>Parsed Lecturer ID:</strong> <?= $lecturer_id ?><br>
            <strong>Parsed Assignment ID:</strong> <?= $assignment_id ?><br>
            <strong>Parsed Section:</strong> <?= htmlspecialchars($section) ?><br>
            <strong>Parsed Token:</strong> <?= htmlspecialchars($token) ?><br>
            <strong>Expires At:</strong> <?= $expires_at > 0 ? date('Y-m-d H:i:s', $expires_at) : 'Not set' ?> (Current: <?= date('Y-m-d H:i:s') ?>)<br>
            <strong>Token Valid:</strong> <?= !empty($token) ? 'Yes' : 'No' ?><br>
            <strong>Not Expired:</strong> <?= $expires_at > 0 && time() <= $expires_at ? 'Yes' : 'No' ?><br>
            <strong>DB Capabilities:</strong> <?= htmlspecialchars(json_encode($dbCapabilities)) ?><br>
            <strong>Message:</strong> <?= htmlspecialchars($message) ?><br>
            <strong>Message Type:</strong> <?= htmlspecialchars($messageType) ?><br>
          </div>
        </div>
        <?php endif; ?>

        <div class="debug-section">
          <h4><i class="fas fa-tools"></i> Debug MetaMask Connection</h4>
          <div style="display: flex; gap: var(--space-sm); flex-wrap: wrap;">
            <button id="testMetaMaskBtn" class="action-button">
              <i class="fas fa-vial"></i> Test MetaMask
            </button>
            <button id="testTransactionBtn" class="action-button" disabled>
              <i class="fas fa-paper-plane"></i> Test Transaction
            </button>
            <button id="testContractBtn" class="action-button" disabled>
              <i class="fas fa-file-contract"></i> Test Contract
            </button>
            <button id="testBlockchainBtn" class="action-button" style="background: #EF4444;">
              <i class="fas fa-rocket"></i> Test Blockchain Transaction
            </button>
            <button id="testGanacheBtn" class="action-button" style="background: #10B981;">
              <i class="fas fa-network-wired"></i> Test Ganache Connection
            </button>
            <button id="testDuplicateBtn" class="action-button" style="background: #8B5CF6;">
              <i class="fas fa-search"></i> Test Duplicate Check
            </button>
          </div>
          <div id="debug-info">Click "Test MetaMask" to check your connection...</div>
        </div>

        <form id="qr-form" method="POST">
          <input type="hidden" name="qr_data" id="qr-data">
        </form>
      </div>
    </div>
  </div>

  <!-- ENHANCED ATTENDANCE CONFIRMATION MODAL -->
  <?php if ($message !== ''): ?>
    <div id="popupModal" class="modal">
      <div class="modal-content">
        <button class="close-button" onclick="closeModal()">
          <i class="fas fa-times"></i>
        </button>

        <!-- Modal Header -->
        <div class="modal-header">
          <?php if ($messageType === 'success'): ?>
            <div class="modal-icon">
              <i class="fas fa-check-circle"></i>
            </div>
            <h3 class="modal-title">Attendance Confirmed!</h3>
            <p class="modal-subtitle">Your attendance has been successfully recorded</p>
          <?php else: ?>
            <div class="modal-icon error">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h3 class="modal-title">Attendance Failed</h3>
            <p class="modal-subtitle">Unable to record your attendance</p>
          <?php endif; ?>
        </div>

        <!-- Modal Body -->
        <div class="modal-body">
          <div class="modal-message">
            <?= htmlspecialchars($message) ?>
          </div>

          <?php if ($messageType === 'success' && $newAttendanceID > 0): ?>
            <div class="modal-details">
              <div class="modal-detail-item">
                <span class="modal-detail-label">Student ID:</span>
                <span class="modal-detail-value"><?= htmlspecialchars($student_userid) ?></span>
              </div>
              <div class="modal-detail-item">
                <span class="modal-detail-label">Student Name:</span>
                <span class="modal-detail-value"><?= htmlspecialchars($student_name) ?></span>
              </div>
              <?php if (!empty($course_code)): ?>
              <div class="modal-detail-item">
                <span class="modal-detail-label">Course Code:</span>
                <span class="modal-detail-value"><?= htmlspecialchars($course_code) ?></span>
              </div>
              <?php endif; ?>
              <?php if (!empty($course_name)): ?>
              <div class="modal-detail-item">
                <span class="modal-detail-label">Course Name:</span>
                <span class="modal-detail-value"><?= htmlspecialchars($course_name) ?></span>
              </div>
              <?php endif; ?>
              <?php if (!empty($section_info)): ?>
              <div class="modal-detail-item">
                <span class="modal-detail-label">Section:</span>
                <span class="modal-detail-value"><?= htmlspecialchars($section_info) ?></span>
              </div>
              <?php endif; ?>
              <div class="modal-detail-item">
                <span class="modal-detail-label">Attendance ID:</span>
                <span class="modal-detail-value">#<?= $newAttendanceID ?></span>
              </div>
              <div class="modal-detail-item">
                <span class="modal-detail-label">Timestamp:</span>
                <span class="modal-detail-value"><?= date('Y-m-d H:i:s') ?></span>
              </div>
            </div>

            <div id="blockchain-progress"></div>
          <?php elseif ($messageType === 'error'): ?>
            <div class="modal-details error">
              <div class="modal-detail-item">
                <span class="modal-detail-label">Student ID:</span>
                <span class="modal-detail-value"><?= htmlspecialchars($student_userid) ?></span>
              </div>
              <div class="modal-detail-item">
                <span class="modal-detail-label">Student Name:</span>
                <span class="modal-detail-value"><?= htmlspecialchars($student_name) ?></span>
              </div>
              <?php if (!empty($course_code)): ?>
                <div class="modal-detail-item">
                  <span class="modal-detail-label">Course Code:</span>
                  <span class="modal-detail-value"><?= htmlspecialchars($course_code) ?></span>
                </div>
              <?php endif; ?>
              <?php if (!empty($course_name)): ?>
                <div class="modal-detail-item">
                  <span class="modal-detail-label">Course Name:</span>
                  <span class="modal-detail-value"><?= htmlspecialchars($course_name) ?></span>
                </div>
              <?php endif; ?>
              <?php if (!empty($section_info)): ?>
                <div class="modal-detail-item">
                  <span class="modal-detail-label">Section:</span>
                  <span class="modal-detail-value"><?= htmlspecialchars($section_info) ?></span>
                </div>
              <?php endif; ?>
              <div class="modal-detail-item">
                <span class="modal-detail-label">Timestamp:</span>
                <span class="modal-detail-value"><?= date('Y-m-d H:i:s') ?></span>
              </div>
            </div>

            <!-- Error Help Section -->
            <div class="modal-help">
              <?php if (strpos($message, 'not registered') !== false): ?>
                <p><strong>What to do next:</strong></p>
                <ul>
                  <li>Register for this course first</li>
                  <li>Contact your academic advisor if you need assistance</li>
                  <li>Ensure you're scanning the correct QR code</li>
                </ul>
              <?php elseif (strpos($message, 'already marked') !== false): ?>
                <p><strong>Note:</strong> You can only mark attendance once per day for each course.</p>
              <?php elseif (strpos($message, 'expired') !== false): ?>
                <p><strong>What to do next:</strong></p>
                <ul>
                  <li>Ask your lecturer to generate a new QR code</li>
                  <li>QR codes expire every 30 seconds for security</li>
                  <li>Make sure you scan the QR code quickly after it's generated</li>
                </ul>
              <?php elseif (strpos($message, 'Invalid QR code format') !== false): ?>
                <p><strong>What to do next:</strong></p>
                <ul>
                  <li>Make sure you're scanning a valid attendance QR code</li>
                  <li>Ask your lecturer to generate a new QR code</li>
                  <li>Check that the QR code is not damaged or blurry</li>
                </ul>
              <?php elseif (strpos($message, 'Invalid course') !== false): ?>
                <p><strong>What to do next:</strong></p>
                <ul>
                  <li>Ensure you're scanning the correct QR code</li>
                  <li>Check with your lecturer if the QR code is valid</li>
                  <li>Try scanning again</li>
                </ul>
              <?php endif; ?>
            </div>
          <?php endif; ?>
        </div>

        <!-- Modal Buttons -->
        <?php if ($messageType === 'success' && $newAttendanceID > 0): ?>
          <div class="modal-buttons">
            <button id="markChainBtn" class="modal-button primary">
              <i class="fas fa-link"></i> Record on Blockchain
            </button>
            <button id="forceChainBtn" class="modal-button secondary">
              <i class="fas fa-exclamation-triangle"></i> Force Record
            </button>
          </div>
        <?php elseif ($messageType === 'error'): ?>
          <div class="modal-buttons">
            <button onclick="closeModal()" class="modal-button primary">
              <i class="fas fa-times"></i> Close
            </button>
            <?php if (strpos($message, 'not registered') !== false): ?>
              <button onclick="window.location.href='register_courses.php'" class="modal-button secondary">
                <i class="fas fa-edit"></i> Register Courses
              </button>
            <?php endif; ?>
          </div>
        <?php else: ?>
          <div class="modal-buttons">
            <button onclick="closeModal()" class="modal-button primary">
              <i class="fas fa-check"></i> Continue
            </button>
          </div>
        <?php endif; ?>
      </div>
    </div>
  <?php endif; ?>

  <footer><p>UNIVERSITI TUN HUSSEIN ONN MALAYSIA</p></footer>

  <script>
    // ─── 1) Enhanced QR Reader with Better Error Handling ───
    let html5QrcodeScanner;
    let isScanning = false;
    let currentCameraMode = "environment"; // "environment" for back camera, "user" for front camera

    function onScanSuccess(decodedText, decodedResult) {
      console.log("QR Code scanned successfully:", decodedText);
      console.log("Decoded result:", decodedResult);

      // Set the scanned data
      document.getElementById("qr-data").value = decodedText;

      // Stop scanning to prevent multiple scans
      if (isScanning) {
        html5QrcodeScanner.stop().then(() => {
          console.log("QR Scanner stopped successfully");
          // Submit the form
          document.getElementById("qr-form").submit();
        }).catch(err => {
          console.error("Error stopping scanner:", err);
          // Submit anyway
          document.getElementById("qr-form").submit();
        });
        isScanning = false;
      }
    }

    function onScanFailure(error) {
      // Handle scan failure - this is called frequently, so we don't log every failure
      // console.warn("QR scan failure:", error);
    }

    // Initialize QR Scanner
    function initializeQRScanner() {
      try {
        console.log("Initializing QR Scanner...");

        // Check if the qr-reader element exists
        const qrReaderElement = document.getElementById("qr-reader");
        if (!qrReaderElement) {
          console.error("QR reader element not found!");
          return;
        }

        html5QrcodeScanner = new Html5Qrcode("qr-reader");

        // Configuration for the scanner
        const config = {
          fps: 10,
          qrbox: { width: 250, height: 250 },
          aspectRatio: 1.0
        };

        // Try to start with current camera mode
        const cameraConstraint = { facingMode: currentCameraMode };
        const cameraName = currentCameraMode === "environment" ? "back" : "front";

        html5QrcodeScanner.start(
          cameraConstraint,
          config,
          onScanSuccess,
          onScanFailure
        ).then(() => {
          console.log(`QR Scanner started successfully with ${cameraName} camera`);
          isScanning = true;
          document.getElementById("result").innerHTML = `<div style="color: green; margin-top: 10px;"><i class="fas fa-camera"></i> ${cameraName.charAt(0).toUpperCase() + cameraName.slice(1)} camera ready - point at QR code</div>`;
          document.getElementById("camera-status").innerHTML = `<div style="color: green;"><i class="fas fa-check"></i> Using ${cameraName} camera</div>`;
        }).catch(err => {
          console.warn(`${cameraName} camera failed, trying alternative:`, err);

          // Try the opposite camera
          const altCameraMode = currentCameraMode === "environment" ? "user" : "environment";
          const altCameraName = altCameraMode === "environment" ? "back" : "front";

          html5QrcodeScanner.start(
            { facingMode: altCameraMode },
            config,
            onScanSuccess,
            onScanFailure
          ).then(() => {
            console.log(`QR Scanner started successfully with ${altCameraName} camera`);
            isScanning = true;
            currentCameraMode = altCameraMode; // Update current mode
            document.getElementById("result").innerHTML = `<div style="color: orange; margin-top: 10px;"><i class="fas fa-camera"></i> Using ${altCameraName} camera - point at QR code</div>`;
            document.getElementById("camera-status").innerHTML = `<div style="color: orange;"><i class="fas fa-info-circle"></i> Switched to ${altCameraName} camera</div>`;
          }).catch(err2 => {
            console.error("Both specific cameras failed, trying default:", err2);

            // Last resort - try without camera constraints
            html5QrcodeScanner.start(
              undefined, // Let browser choose
              config,
              onScanSuccess,
              onScanFailure
            ).then(() => {
              console.log("QR Scanner started with default camera");
              isScanning = true;
              document.getElementById("result").innerHTML = '<div style="color: blue; margin-top: 10px;"><i class="fas fa-camera"></i> Camera ready - point at QR code</div>';
              document.getElementById("camera-status").innerHTML = '<div style="color: blue;"><i class="fas fa-camera"></i> Using default camera</div>';
            }).catch(err3 => {
              console.error("All camera options failed:", err3);
              document.getElementById("result").innerHTML = '<div style="color: red; margin-top: 10px;"><i class="fas fa-exclamation-triangle"></i> Camera access failed. Please check permissions.</div>';
              document.getElementById("camera-status").innerHTML = '<div style="color: red;"><i class="fas fa-times"></i> Camera access failed</div>';
            });
          });
        });

      } catch (error) {
        console.error("Error initializing QR scanner:", error);
        document.getElementById("result").innerHTML = '<div style="color: red; margin-top: 10px;"><i class="fas fa-exclamation-triangle"></i> QR Scanner initialization failed.</div>';
      }
    }

    // Manual QR input functions
    function submitManualQR() {
      const manualInput = document.getElementById("manual-qr-input");
      const qrData = manualInput.value.trim();

      if (!qrData) {
        alert("Please enter QR data");
        return;
      }

      console.log("Manual QR submission:", qrData);
      document.getElementById("qr-data").value = qrData;
      document.getElementById("qr-form").submit();
    }

    function testWithSampleQR() {
      // Sample QR data for testing with proper token and expiration
      const currentTime = Math.floor(Date.now() / 1000);
      const expiresAt = currentTime + 300; // 5 minutes from now
      const token = "sample" + Math.random().toString(36).substr(2, 8); // Generate random token

      const sampleQR = `course_id=1&lecturer_id=1&token=${token}&expires_at=${expiresAt}&assignment_id=1&section=A`;

      console.log("Testing with sample QR:", sampleQR);
      console.log("Current time:", currentTime, "Expires at:", expiresAt);
      document.getElementById("manual-qr-input").value = sampleQR;
      document.getElementById("qr-data").value = sampleQR;
      document.getElementById("qr-form").submit();
    }

    function testWithExpiredQR() {
      // Sample QR data for testing expired QR code
      const currentTime = Math.floor(Date.now() / 1000);
      const expiresAt = currentTime - 300; // 5 minutes ago (expired)
      const token = "expired" + Math.random().toString(36).substr(2, 8);

      const expiredQR = `course_id=1&lecturer_id=1&token=${token}&expires_at=${expiresAt}&assignment_id=1&section=A`;

      console.log("Testing with expired QR:", expiredQR);
      console.log("Current time:", currentTime, "Expired at:", expiresAt);
      document.getElementById("manual-qr-input").value = expiredQR;
      document.getElementById("qr-data").value = expiredQR;
      document.getElementById("qr-form").submit();
    }

    // Toggle camera mode function
    function toggleCameraMode() {
      currentCameraMode = currentCameraMode === "environment" ? "user" : "environment";
      const cameraName = currentCameraMode === "environment" ? "back" : "front";
      console.log("Switching to " + cameraName + " camera...");
      document.getElementById("camera-status").innerHTML = '<div style="color: blue;"><i class="fas fa-sync fa-spin"></i> Switching to ' + cameraName + ' camera...</div>';

      if (html5QrcodeScanner && isScanning) {
        html5QrcodeScanner.stop().then(() => {
          console.log("Camera stopped, switching...");
          isScanning = false;
          setTimeout(() => {
            initializeQRScanner();
          }, 1000);
        }).catch(err => {
          console.error("Error stopping camera:", err);
          isScanning = false;
          setTimeout(() => {
            initializeQRScanner();
          }, 1000);
        });
      } else {
        setTimeout(() => {
          initializeQRScanner();
        }, 1000);
      }
    }

    // Restart camera function
    function restartCamera() {
      console.log("Restarting camera...");
      document.getElementById("result").innerHTML = '<div style="color: blue; margin-top: 10px;"><i class="fas fa-sync fa-spin"></i> Restarting camera...</div>';
      document.getElementById("camera-status").innerHTML = '<div style="color: blue;"><i class="fas fa-sync fa-spin"></i> Restarting camera...</div>';

      if (html5QrcodeScanner && isScanning) {
        html5QrcodeScanner.stop().then(() => {
          console.log("Camera stopped, reinitializing...");
          isScanning = false;
          setTimeout(() => {
            initializeQRScanner();
          }, 1000);
        }).catch(err => {
          console.error("Error stopping camera:", err);
          isScanning = false;
          setTimeout(() => {
            initializeQRScanner();
          }, 1000);
        });
      } else {
        setTimeout(() => {
          initializeQRScanner();
        }, 1000);
      }
    }

    // Camera permission check
    function checkCameraPermissions() {
      console.log("Checking camera permissions...");
      document.getElementById("result").innerHTML = '<div style="color: blue; margin-top: 10px;"><i class="fas fa-sync fa-spin"></i> Checking camera permissions...</div>';

      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        navigator.mediaDevices.getUserMedia({ video: true })
          .then(function(stream) {
            console.log("Camera permission granted");
            // Stop the stream immediately as we just wanted to check permission
            stream.getTracks().forEach(track => track.stop());
            document.getElementById("result").innerHTML = '<div style="color: green; margin-top: 10px;"><i class="fas fa-check"></i> Camera permission granted - ready to scan</div>';
          })
          .catch(function(err) {
            console.error("Camera permission denied:", err);
            document.getElementById("result").innerHTML = '<div style="color: red; margin-top: 10px;"><i class="fas fa-exclamation-triangle"></i> Camera permission denied. Please allow camera access and refresh the page.</div>';
          });
      } else {
        console.error("getUserMedia not supported");
        document.getElementById("result").innerHTML = '<div style="color: red; margin-top: 10px;"><i class="fas fa-exclamation-triangle"></i> Camera not supported in this browser.</div>';
      }
    }

    // Check if HTML5 QR Code library is loaded
    function checkLibraryLoaded() {
      if (typeof Html5Qrcode === 'undefined') {
        console.error("Html5Qrcode library not loaded!");
        document.getElementById("result").innerHTML = '<div style="color: red; margin-top: 10px;"><i class="fas fa-exclamation-triangle"></i> QR Code library not loaded. Please refresh the page.</div>';
        return false;
      }
      console.log("Html5Qrcode library loaded successfully");
      return true;
    }

    // Initialize when page loads
    document.addEventListener('DOMContentLoaded', function() {
      console.log("DOM loaded, checking library and camera permissions...");

      // Check if library is loaded
      if (!checkLibraryLoaded()) {
        return;
      }

      checkCameraPermissions();

      // Delay QR scanner initialization slightly
      setTimeout(() => {
        console.log("Initializing QR scanner...");
        initializeQRScanner();
      }, 1000);
    });

    // ─── 2) Blockchain Initialization ───
    let web3, contract, currentAccount, contractAddress;
    const GANACHE_NETWORK_ID = "5777";
    const GANACHE_RPC_URL = "http://127.0.0.1:7545";

    async function initializeBlockchain() {
      const statusDiv = document.getElementById("blockchain-status");
      try {
        // Check MetaMask availability
        if (!window.ethereum) {
          throw new Error("MetaMask not detected. Please install MetaMask extension.");
        }

        statusDiv.innerHTML = `<div class="status-message status-warning">
          <i class="fas fa-sync fa-spin"></i> Connecting to MetaMask...
        </div>`;

        // Request account access
        const accounts = await ethereum.request({method: "eth_requestAccounts"});
        if (!accounts.length) {
          throw new Error("Please unlock MetaMask and connect your account.");
        }
        currentAccount = accounts[0];

        // Initialize Web3
        web3 = new Web3(window.ethereum);

        // Check network
        const networkId = await web3.eth.net.getId();
        console.log("Current network ID:", networkId);

        if (networkId.toString() !== GANACHE_NETWORK_ID) {
          statusDiv.innerHTML = `<div class="status-message status-warning">
            <i class="fas fa-exclamation-triangle"></i> Wrong network detected.
            <button onclick="switchToGanache()" class="action-button" style="margin-left: 10px;">
              Switch to Ganache
            </button>
          </div>`;
          return false;
        }

        // Load contract ABI and address
        statusDiv.innerHTML = `<div class="status-message status-warning">
          <i class="fas fa-sync fa-spin"></i> Loading smart contract...
        </div>`;

        const response = await fetch("../assets/js/Attendance.json");
        if (!response.ok) {
          throw new Error("Failed to load contract ABI. Please check if Attendance.json exists.");
        }

        const artifact = await response.json();
        if (!artifact.networks[GANACHE_NETWORK_ID]) {
          throw new Error("Smart contract not deployed on Ganache network. Please deploy the contract first.");
        }

        contractAddress = artifact.networks[GANACHE_NETWORK_ID].address;
        contract = new web3.eth.Contract(artifact.abi, contractAddress);

        // Test contract connection
        await contract.methods.getRecordCount().call();

        statusDiv.innerHTML = `<div class="status-message status-success">
          <i class="fas fa-check-circle"></i> Blockchain ready! Connected to: ${currentAccount.substring(0, 10)}...
        </div>`;

        return true;
      } catch(err) {
        console.error("Blockchain initialization error:", err);
        statusDiv.innerHTML = `<div class="status-message status-error">
          <i class="fas fa-exclamation-triangle"></i> ${err.message}
        </div>`;
        return false;
      }
    }

    // Helper function to switch to Ganache network
    async function switchToGanache() {
      try {
        await ethereum.request({
          method: 'wallet_switchEthereumChain',
          params: [{ chainId: '0x1691' }], // 5777 in hex
        });
      } catch (switchError) {
        // If network doesn't exist, add it
        if (switchError.code === 4902) {
          try {
            await ethereum.request({
              method: 'wallet_addEthereumChain',
              params: [{
                chainId: '0x1691',
                chainName: 'Ganache Local',
                nativeCurrency: {
                  name: 'Ethereum',
                  symbol: 'ETH',
                  decimals: 18,
                },
                rpcUrls: [GANACHE_RPC_URL],
              }],
            });
          } catch (addError) {
            console.error('Failed to add Ganache network:', addError);
          }
        } else {
          console.error('Failed to switch to Ganache network:', switchError);
        }
      }
    }

    // ─── 3) Enhanced Record On-Chain (UserID-based duplicate detection with assignment support) ───
    async function recordOnChain(attID, courseID, studName, courseName, skipDuplicateCheck = false, assignmentID = 0) {
      console.log("🚀 recordOnChain function called!");
      console.log("Parameters:", {attID, courseID, studName, courseName, skipDuplicateCheck});

      const progressDiv = document.getElementById("blockchain-progress");
      const btn = document.getElementById("markChainBtn");

      // Get student UserID from PHP session - used for duplicate detection instead of Ethereum address
      const studentUserID = <?= json_encode($student_userid) ?>;

      try {
        // Ensure blockchain connection
        if (!contract) {
          progressDiv.innerHTML = `<div class="modal-progress warning">
            <i class="fas fa-sync fa-spin"></i> Initializing blockchain connection...
          </div>`;
          const connected = await initializeBlockchain();
          if (!connected) {
            throw new Error("Failed to connect to blockchain. Please check your MetaMask connection and network.");
          }
        }

        // Disable button and show processing state
        btn.disabled = true;
        btn.classList.add('loading');
        btn.innerHTML = 'Processing...';

        // Step 1: Debug and check if already marked
        progressDiv.innerHTML = `<div class="modal-progress warning">
          <i class="fas fa-sync fa-spin"></i> Checking attendance status...
        </div>`;

        // Debug logging
        console.log("=== BLOCKCHAIN DEBUG INFO ===");
        console.log("Course ID:", courseID, typeof courseID);
        console.log("Student UserID:", studentUserID);
        console.log("Current Account:", currentAccount);
        console.log("Contract Address:", contract.options.address);
        console.log("Student Name:", studName);
        console.log("Course Name:", courseName);
        console.log("Skip Duplicate Check:", skipDuplicateCheck);

        // Validate inputs
        if (!courseID || courseID <= 0) {
          throw new Error("Invalid course ID. Please scan a valid QR code first.");
        }
        if (!currentAccount) {
          throw new Error("No MetaMask account connected. Please connect your wallet.");
        }
        if (!studentUserID) {
          throw new Error("Student UserID not found. Please refresh the page and try again.");
        }

        // Verify account is connected
        if (!currentAccount) {
          throw new Error("No MetaMask account connected. Please connect your wallet.");
        }

        // Verify contract is loaded
        if (!contract || !contract.methods) {
          throw new Error("Smart contract not loaded properly. Please refresh and try again.");
        }

        try {
          // Test contract connection first
          const recordCount = await contract.methods.getRecordCount().call();
          console.log("Contract record count:", recordCount);

          // Check if already marked (unless skipping duplicate check)
          if (!skipDuplicateCheck) {
            console.log("=== DUPLICATE CHECK DEBUG ===");
            console.log("Checking hasAttended for:");
            console.log("- Course ID:", courseID.toString());
            console.log("- Student UserID:", studentUserID);

            try {
              // Create keccak256 hash of studentUserID for the new contract
              const studentIDHash = web3.utils.keccak256(studentUserID);
              console.log("- Student UserID Hash:", studentIDHash);

              const alreadyMarked = await contract.methods.hasAttended(courseID.toString(), studentIDHash).call();
              console.log("hasAttended result:", alreadyMarked);
              console.log("Type of result:", typeof alreadyMarked);
              console.log("Strict equality check (=== true):", alreadyMarked === true);
              console.log("Truthy check:", !!alreadyMarked);

              // Use strict equality check for boolean true
              if (alreadyMarked === true) {
                throw new Error(`You (${studentUserID}) have already marked attendance on blockchain for this course.`);
              }
              console.log("✅ Duplicate check passed - proceeding with transaction");
            } catch (hasAttendedError) {
              console.error("hasAttended call failed:", hasAttendedError);
              if (hasAttendedError.message.includes("already marked attendance")) {
                throw hasAttendedError; // Re-throw if it's our custom error
              }
              // If hasAttended call fails, log warning but continue (might be network issue)
              console.warn("⚠️ Could not check hasAttended, proceeding with caution:", hasAttendedError.message);
            }
          } else {
            console.log("⚠️ Skipping duplicate check (force mode)");
          }
        } catch (contractError) {
          console.error("Contract call error:", contractError);
          if (!skipDuplicateCheck && contractError.message.includes("already marked attendance")) {
            throw contractError; // Re-throw duplicate attendance errors
          } else if (!skipDuplicateCheck) {
            throw new Error(`Contract interaction failed: ${contractError.message}`);
          } else {
            console.warn("Contract check failed but continuing in force mode:", contractError);
          }
        }

        // Step 2: Estimate gas with comprehensive error handling
        progressDiv.innerHTML = `<div class="modal-progress warning">
          <i class="fas fa-sync fa-spin"></i> Preparing transaction...
        </div>`;

        let gasEstimate;
        let gasEstimationSucceeded = false;

        try {
          console.log("Attempting gas estimation...");
          console.log("Method call parameters:", {
            courseID: courseID.toString(),
            studentUserID,
            studName,
            courseName,
            from: currentAccount
          });

          gasEstimate = await contract.methods
            .markAttendance(courseID.toString(), studentUserID, studName, courseName)
            .estimateGas({from: currentAccount});

          // Convert BigInt to number and add 20% buffer
          gasEstimate = Math.floor(Number(gasEstimate) * 1.2);
          gasEstimationSucceeded = true;
          console.log("Gas estimation successful:", gasEstimate);

        } catch (gasError) {
          console.error("Gas estimation failed:", gasError);
          console.error("Full error object:", JSON.stringify(gasError, null, 2));

          // Check if it's a revert error (already marked attendance)
          if (gasError.message && gasError.message.includes("Already marked attendance")) {
            throw new Error(`UserID ${studentUserID} has already marked attendance on blockchain for this course.`);
          }

          // Check for other common errors
          if (gasError.message && gasError.message.includes("insufficient funds")) {
            throw new Error("Insufficient ETH balance to pay for gas fees.");
          }

          if (gasError.message && gasError.message.includes("Internal JSON-RPC error")) {
            console.warn("Gas estimation failed with JSON-RPC error, using fallback gas limit");
            // Use a reasonable fallback gas limit for markAttendance function
            gasEstimate = 150000; // Conservative estimate for this function
            gasEstimationSucceeded = false;
          } else {
            throw new Error(`Gas estimation failed: ${gasError.message || 'Unknown error'}`);
          }
        }

        if (!gasEstimationSucceeded) {
          progressDiv.innerHTML = `<div class="modal-progress warning">
            <i class="fas fa-exclamation-triangle"></i> Using fallback gas limit (${gasEstimate})...
          </div>`;
        }

        // Step 3: Get gas price
        const gasPrice = await web3.eth.getGasPrice();
        const gasPriceGwei = web3.utils.fromWei(gasPrice.toString(), 'gwei');
        console.log(`Gas estimate: ${gasEstimate}, Gas price: ${gasPriceGwei} Gwei`);

        // Step 4: Send transaction with enhanced error handling
        progressDiv.innerHTML = `<div class="modal-progress warning">
          <i class="fas fa-sync fa-spin"></i> Please confirm the transaction in MetaMask...<br>
          <small>Gas Limit: ${gasEstimate} | Gas Price: ${gasPriceGwei} Gwei</small>
        </div>`;

        const transactionPromise = new Promise((resolve, reject) => {
          const txParams = {
            from: currentAccount,
            gas: gasEstimate.toString(),
            gasPrice: gasPrice.toString()
          };

          console.log("Sending transaction with params:", txParams);

          contract.methods
            .markAttendance(courseID.toString(), studentUserID, studName, courseName)
            .send(txParams)
            .on("transactionHash", (hash) => {
              console.log("✅ Transaction hash received:", hash);
              progressDiv.innerHTML = `<div class="modal-progress warning">
                <i class="fas fa-sync fa-spin"></i> Transaction submitted!<br>
                <small><strong>Hash:</strong> ${hash.substring(0, 20)}...</small><br>
                <small>Waiting for confirmation...</small>
              </div>`;
            })
            .on("confirmation", (confirmationNumber, receipt) => {
              console.log(`Confirmation ${confirmationNumber}:`, receipt);
            })
            .on("receipt", (receipt) => {
              console.log("✅ Transaction receipt received:", receipt);
              resolve(receipt);
            })
            .on("error", (error) => {
              console.error("❌ Transaction error:", error);
              reject(error);
            });
        });

        // Wait for transaction completion
        const receipt = await transactionPromise;

        // Success! Now save the transaction hash to the database
        progressDiv.innerHTML = `<div class="modal-progress warning">
          <i class="fas fa-sync fa-spin"></i> Saving transaction record...
        </div>`;

        try {
          // Save transaction hash to database
          const saveResponse = await fetch("../modules/save_tx.php", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              attendanceID: attID,
              txHash: receipt.transactionHash
            })
          });

          const saveResult = await saveResponse.json();

          if (saveResult.success) {
            progressDiv.innerHTML = `<div class="modal-progress success">
              <i class="fas fa-check-circle"></i> Attendance successfully recorded on blockchain!<br>
              <small><strong>Transaction Hash:</strong> ${receipt.transactionHash}</small><br>
              <small><strong>Block Number:</strong> ${receipt.blockNumber}</small><br>
              <small><strong>Gas Used:</strong> ${receipt.gasUsed}</small><br>
              <small><strong>Database:</strong> Transaction record saved</small>
            </div>`;
          } else {
            progressDiv.innerHTML = `<div class="modal-progress warning">
              <i class="fas fa-exclamation-triangle"></i> Blockchain transaction successful, but failed to save record to database.<br>
              <small><strong>Transaction Hash:</strong> ${receipt.transactionHash}</small><br>
              <small><strong>Error:</strong> ${saveResult.error || 'Unknown error'}</small>
            </div>`;
          }
        } catch (saveError) {
          console.error("Failed to save transaction record:", saveError);
          progressDiv.innerHTML = `<div class="modal-progress warning">
            <i class="fas fa-exclamation-triangle"></i> Blockchain transaction successful, but failed to save record to database.<br>
            <small><strong>Transaction Hash:</strong> ${receipt.transactionHash}</small><br>
            <small><strong>Error:</strong> ${saveError.message}</small>
          </div>`;
        }

        btn.innerHTML = '<i class="fas fa-check"></i> Completed Successfully';
        btn.style.backgroundColor = 'var(--success)';

      } catch(error) {
        console.error("recordOnChain error:", error);

        let errorMessage = "An unknown error occurred.";

        // Handle specific error types
        if (error.code === 4001) {
          errorMessage = "Transaction cancelled by user.";
        } else if (error.code === -32603) {
          errorMessage = "Internal JSON-RPC error. Please check your network connection.";
        } else if (error.message.includes("insufficient funds")) {
          errorMessage = "Insufficient ETH balance to pay for gas fees.";
        } else if (error.message.includes("User denied")) {
          errorMessage = "Transaction denied by user.";
        } else if (error.message) {
          errorMessage = error.message;
        }

        progressDiv.innerHTML = `<div class="modal-progress error">
          <i class="fas fa-exclamation-triangle"></i> ${errorMessage}
        </div>`;

        // Re-enable button
        if (btn) {
          btn.disabled = false;
          btn.classList.remove('loading');
          btn.innerHTML = '<i class="fas fa-link"></i> Record on Blockchain';
          btn.style.backgroundColor = '';
        }
      }
    }

    // ─── 4) Enhanced Modal & Debug Setup ───
    document.addEventListener("DOMContentLoaded", async () => {
      const msg           = <?= json_encode($message) ?>;
      const attID         = <?= intval($newAttendanceID) ?>;
      const courseID      = <?= intval($scannedCourseID) ?>;
      const assignmentID  = <?= intval($scannedAssignmentID) ?>;
      const studName      = <?= json_encode($student_name) ?>;
      const courseName    = <?= json_encode($course_name) ?>;
      const courseCode    = <?= json_encode($course_code) ?>;
      const sectionInfo   = <?= json_encode($section_info) ?>;
      const dbCapabilities = <?= json_encode($dbCapabilities) ?>;
      const modal         = document.getElementById("popupModal");
      const btn           = document.getElementById("markChainBtn");

      // Initialize blockchain connection early (but don't block UI)
      initializeBlockchain().catch(err => {
        console.log("Initial blockchain connection failed, will retry when needed:", err);
      });

      // Set up debug buttons
      const testMetaMaskBtn = document.getElementById("testMetaMaskBtn");
      const testTransactionBtn = document.getElementById("testTransactionBtn");

      if (testMetaMaskBtn) {
        testMetaMaskBtn.addEventListener("click", testMetaMaskConnection);
      }
      if (testTransactionBtn) {
        testTransactionBtn.addEventListener("click", testSimpleTransaction);
      }

      const testContractBtn = document.getElementById("testContractBtn");
      if (testContractBtn) {
        testContractBtn.addEventListener("click", testContractInteraction);
      }

      const testBlockchainBtn = document.getElementById("testBlockchainBtn");
      if (testBlockchainBtn) {
        testBlockchainBtn.addEventListener("click", () => {
          // Test with dummy data
          recordOnChain(999, 999, "Test Student", "Test Course", true, 0);
        });
      }

      const testGanacheBtn = document.getElementById("testGanacheBtn");
      if (testGanacheBtn) {
        testGanacheBtn.addEventListener("click", testGanacheConnection);
      }

      const testDuplicateBtn = document.getElementById("testDuplicateBtn");
      if (testDuplicateBtn) {
        testDuplicateBtn.addEventListener("click", () => testDuplicateCheck(courseID));
      }

      // Handle modal display and blockchain button
      if (msg && modal) {
        modal.style.display = "flex";

        // Auto-hide modal after 30 seconds
        setTimeout(() => {
          if (modal.style.display === "flex") {
            modal.style.display = "none";
          }
        }, 30000);

        // Set up enhanced blockchain button if attendance was successfully recorded
        if (btn && attID > 0) {
          btn.addEventListener("click", async () => {
            // Enhanced course name with section info
            let enhancedCourseName = courseName;
            if (sectionInfo && dbCapabilities.course_assignments) {
              // Check if section already contains "Section" to avoid duplication
              const sectionDisplay = sectionInfo.startsWith('Section') ? sectionInfo : `Section ${sectionInfo}`;
              enhancedCourseName = `${courseName} (${sectionDisplay})`;
            }
            await recordOnChain(attID, courseID, studName, enhancedCourseName, false, assignmentID);
          });
        }

        // Set up force blockchain button (for testing)
        const forceBtn = document.getElementById("forceChainBtn");
        if (forceBtn && attID > 0) {
          forceBtn.addEventListener("click", async () => {
            let enhancedCourseName = courseName;
            if (sectionInfo && dbCapabilities.course_assignments) {
              // Check if section already contains "Section" to avoid duplication
              const sectionDisplay = sectionInfo.startsWith('Section') ? sectionInfo : `Section ${sectionInfo}`;
              enhancedCourseName = `${courseName} (${sectionDisplay})`;
            }
            await recordOnChain(attID, courseID, studName, enhancedCourseName, true, assignmentID); // true = skip duplicate check
          });
        }
      }

      // Handle MetaMask account/network changes
      if (window.ethereum) {
        window.ethereum.on("accountsChanged", (accounts) => {
          console.log("Account changed:", accounts);
          if (accounts.length === 0) {
            // User disconnected
            document.getElementById("blockchain-status").innerHTML = `
              <div class="status-message status-warning">
                <i class="fas fa-exclamation-triangle"></i> MetaMask disconnected. Please reconnect.
              </div>`;
          } else {
            // Account changed, reinitialize
            location.reload();
          }
        });

        window.ethereum.on("chainChanged", (chainId) => {
          console.log("Network changed:", chainId);
          location.reload();
        });
      }
    });

    // ─── Debug Helpers ───

    // Test duplicate check functionality
    async function testDuplicateCheck(courseID) {
      const debug = document.getElementById("debug-info");
      const studentUserID = <?= json_encode($student_userid) ?>;
      debug.innerHTML = "🔍 Testing duplicate check functionality...";

      try {
        if (!contract || !currentAccount) {
          debug.innerHTML = "❌ Blockchain not initialized. Please test MetaMask first.";
          return;
        }

        if (!studentUserID) {
          debug.innerHTML = "❌ Student UserID not found. Please refresh the page.";
          return;
        }

        debug.innerHTML += `<br><br>=== DUPLICATE CHECK TEST (UserID-based) ===`;
        debug.innerHTML += `<br>Course ID: ${courseID}`;
        debug.innerHTML += `<br>Student UserID: ${studentUserID}`;
        debug.innerHTML += `<br>Student Address: ${currentAccount}`;
        debug.innerHTML += `<br>Contract Address: ${contract.options.address}`;

        // Create keccak256 hash of studentUserID
        const studentIDHash = web3.utils.keccak256(studentUserID);
        debug.innerHTML += `<br>Student UserID Hash: ${studentIDHash}`;

        // Test hasAttended function with new signature
        const hasAttended = await contract.methods.hasAttended(courseID.toString(), studentIDHash).call();
        debug.innerHTML += `<br><br>📋 hasAttended(${courseID}, ${studentIDHash.substring(0, 10)}...): ${hasAttended}`;
        debug.innerHTML += `<br>Type: ${typeof hasAttended}`;

        if (hasAttended === true) {
          debug.innerHTML += `<br>🚫 Result: UserID ${studentUserID} has already marked attendance for this course`;
        } else if (hasAttended === false) {
          debug.innerHTML += `<br>✅ Result: UserID ${studentUserID} can mark attendance for this course`;
        } else {
          debug.innerHTML += `<br>⚠️ Unexpected result type: ${hasAttended}`;
        }

        // Test with a different course ID to show it works
        const testCourseId = 99999;
        const testHasAttended = await contract.methods.hasAttended(testCourseId, studentIDHash).call();
        debug.innerHTML += `<br><br>🧪 Test with course ${testCourseId}: ${testHasAttended}`;

        debug.innerHTML += `<br><br>✅ UserID-based duplicate check test completed successfully!`;

      } catch (error) {
        console.error("Duplicate check test error:", error);
        debug.innerHTML += `<br>❌ Error: ${error.message}`;
      }
    }

    async function testMetaMaskConnection() {
      const debug = document.getElementById("debug-info");
      const testBtn = document.getElementById("testTransactionBtn");

      try {
        debug.innerHTML = "🔄 Testing connection...";

        if (!window.ethereum) {
          throw new Error("MetaMask not detected. Please install MetaMask.");
        }

        // Get accounts
        let accounts = await ethereum.request({method: "eth_accounts"});
        if (!accounts.length) {
          accounts = await ethereum.request({method: "eth_requestAccounts"});
        }

        // Get network info
        const networkId = await ethereum.request({method: "net_version"});
        const chainId = await ethereum.request({method: "eth_chainId"});

        // Get balance
        const balance = await ethereum.request({
          method: "eth_getBalance",
          params: [accounts[0], "latest"]
        });
        const ethBalance = Number(BigInt(balance)) / 1e18;

        // Check if on correct network
        const isCorrectNetwork = networkId === GANACHE_NETWORK_ID;
        const networkStatus = isCorrectNetwork ? "✅" : "❌";

        debug.innerHTML = `
          <strong>MetaMask Connection Test:</strong><br>
          ${networkStatus} Network: ${networkId} (Chain: ${chainId})<br>
          ✅ Account: ${accounts[0].substring(0, 10)}...${accounts[0].substring(38)}<br>
          ✅ Balance: ${ethBalance.toFixed(4)} ETH<br>
          ${isCorrectNetwork ?
            "✅ Connected to Ganache" :
            "❌ Wrong network! Please switch to Ganache (5777)"
          }
        `;

        if (testBtn) {
          testBtn.disabled = !isCorrectNetwork;
        }

        const testContractBtn = document.getElementById("testContractBtn");
        if (testContractBtn) {
          testContractBtn.disabled = !isCorrectNetwork;
        }

        // Update current account
        currentAccount = accounts[0];

      } catch(error) {
        console.error("MetaMask test error:", error);
        debug.innerHTML = `❌ Error: ${error.message}`;
        if (testBtn) {
          testBtn.disabled = true;
        }
      }
    }

    async function testSimpleTransaction() {
      const debug = document.getElementById("debug-info");

      try {
        debug.innerHTML = "🔄 Sending test transaction...";

        if (!currentAccount) {
          throw new Error("No account connected. Please test MetaMask connection first.");
        }

        const txHash = await ethereum.request({
          method: "eth_sendTransaction",
          params: [{
            from: currentAccount,
            to: currentAccount,
            value: "0x0", // 0 ETH
            gas: "0x5208" // 21000 gas
          }]
        });

        debug.innerHTML = `✅ Test transaction sent!<br>Hash: ${txHash}`;

      } catch(error) {
        console.error("Test transaction error:", error);

        if (error.code === 4001) {
          debug.innerHTML = "❌ Transaction cancelled by user";
        } else {
          debug.innerHTML = `❌ Transaction failed: ${error.message}`;
        }
      }
    }

    async function testContractInteraction() {
      const debug = document.getElementById("debug-info");
      const testBtn = document.getElementById("testContractBtn");

      try {
        debug.innerHTML = "🔄 Testing contract interaction...";
        testBtn.disabled = true;

        if (!contract) {
          const connected = await initializeBlockchain();
          if (!connected) {
            throw new Error("Failed to initialize blockchain connection");
          }
        }

        if (!currentAccount) {
          throw new Error("No account connected");
        }

        // Test 1: Get record count
        const recordCount = await contract.methods.getRecordCount().call();
        debug.innerHTML += `<br>✅ Record count: ${recordCount}`;

        // Test 2: Check hasAttended for test course (UserID-based)
        const studentUserID = <?= json_encode($student_userid) ?>;
        const testCourseId = 999;
        const studentIDHash = web3.utils.keccak256(studentUserID);
        const hasAttended = await contract.methods.hasAttended(testCourseId, studentIDHash).call();
        debug.innerHTML += `<br>✅ HasAttended(${testCourseId}, ${studentUserID}): ${hasAttended}`;
        debug.innerHTML += `<br>   UserID Hash: ${studentIDHash.substring(0, 20)}...`;

        // Test 2b: Check hasAttended for a real course if available
        const urlParams = new URLSearchParams(window.location.search);
        const realCourseId = urlParams.get('course_id');
        if (realCourseId) {
          const realHasAttended = await contract.methods.hasAttended(realCourseId, studentIDHash).call();
          debug.innerHTML += `<br>✅ HasAttended(${realCourseId}, ${studentUserID}): ${realHasAttended}`;
        }

        // Test 3: Try to estimate gas for markAttendance
        try {
          const gasEstimate = await contract.methods
            .markAttendance(testCourseId.toString(), studentUserID, "Test Student", "Test Course")
            .estimateGas({from: currentAccount});
          debug.innerHTML += `<br>✅ Gas estimate: ${gasEstimate}`;
        } catch (gasError) {
          console.error("Gas estimation test error:", gasError);
          if (gasError.message && gasError.message.includes("Already marked attendance")) {
            debug.innerHTML += `<br>⚠️ Gas estimation: Already marked (expected for test)`;
          } else if (gasError.message && gasError.message.includes("Internal JSON-RPC error")) {
            debug.innerHTML += `<br>⚠️ Gas estimation: JSON-RPC error (will use fallback)`;
          } else {
            debug.innerHTML += `<br>❌ Gas estimation failed: ${gasError.message}`;
          }
        }

        // Test 4: Check account balance
        try {
          const balance = await web3.eth.getBalance(currentAccount);
          const ethBalance = web3.utils.fromWei(balance.toString(), 'ether');
          debug.innerHTML += `<br>✅ Account balance: ${parseFloat(ethBalance).toFixed(4)} ETH`;
        } catch (balanceError) {
          debug.innerHTML += `<br>❌ Balance check failed: ${balanceError.message}`;
        }

        debug.innerHTML += `<br><strong>✅ Contract interaction test completed!</strong>`;

      } catch (error) {
        console.error("Contract test error:", error);
        debug.innerHTML += `<br>❌ Contract test failed: ${error.message}`;
      } finally {
        testBtn.disabled = false;
      }
    }

    async function testGanacheConnection() {
      const debug = document.getElementById("debug-info");
      const testBtn = document.getElementById("testGanacheBtn");

      try {
        debug.innerHTML = "🔄 Testing Ganache connection...";
        testBtn.disabled = true;

        // Test 1: Direct HTTP connection to Ganache
        try {
          const ganacheWeb3 = new Web3(new Web3.providers.HttpProvider(GANACHE_RPC_URL));
          const networkId = await ganacheWeb3.eth.net.getId();
          const blockNumber = await ganacheWeb3.eth.getBlockNumber();
          const accounts = await ganacheWeb3.eth.getAccounts();

          debug.innerHTML += `<br>✅ Ganache HTTP connection successful`;
          debug.innerHTML += `<br>✅ Network ID: ${networkId}`;
          debug.innerHTML += `<br>✅ Block Number: ${blockNumber}`;
          debug.innerHTML += `<br>✅ Available Accounts: ${accounts.length}`;

        } catch (ganacheError) {
          debug.innerHTML += `<br>❌ Ganache connection failed: ${ganacheError.message}`;
          throw ganacheError;
        }

        // Test 2: Load and verify contract
        try {
          const response = await fetch("../assets/js/Attendance.json");
          const artifact = await response.json();
          const contractAddress = artifact.networks[GANACHE_NETWORK_ID]?.address;

          if (!contractAddress) {
            throw new Error("Contract not deployed on network " + GANACHE_NETWORK_ID);
          }

          debug.innerHTML += `<br>✅ Contract address: ${contractAddress}`;

          // Test contract call
          const ganacheWeb3 = new Web3(new Web3.providers.HttpProvider(GANACHE_RPC_URL));
          const testContract = new ganacheWeb3.eth.Contract(artifact.abi, contractAddress);
          const recordCount = await testContract.methods.getRecordCount().call();

          debug.innerHTML += `<br>✅ Contract accessible, records: ${recordCount}`;

        } catch (contractError) {
          debug.innerHTML += `<br>❌ Contract test failed: ${contractError.message}`;
          throw contractError;
        }

        debug.innerHTML += `<br><strong>✅ Ganache connection test completed successfully!</strong>`;

      } catch (error) {
        console.error("Ganache test error:", error);
        debug.innerHTML += `<br><strong>❌ Ganache test failed: ${error.message}</strong>`;
        debug.innerHTML += `<br>💡 Make sure Ganache is running on ${GANACHE_RPC_URL}`;
      } finally {
        testBtn.disabled = false;
      }
    }

    // ─── 4) Enhanced Modal Functions ───
    function closeModal() {
      const modal = document.getElementById("popupModal");
      if (modal) {
        modal.style.display = "none";
        // Return focus to the body for accessibility
        document.body.focus();
      }
    }

    function showModal() {
      const modal = document.getElementById("popupModal");
      if (modal) {
        modal.style.display = "flex";

        // Focus management for accessibility
        const firstButton = modal.querySelector('.modal-button');
        if (firstButton) {
          setTimeout(() => firstButton.focus(), 100);
        }

        // Close modal on Escape key
        const handleEscape = (e) => {
          if (e.key === 'Escape') {
            closeModal();
            document.removeEventListener('keydown', handleEscape);
          }
        };
        document.addEventListener('keydown', handleEscape);

        // Close modal when clicking outside
        const handleOutsideClick = (e) => {
          if (e.target === modal) {
            closeModal();
            modal.removeEventListener('click', handleOutsideClick);
          }
        };
        modal.addEventListener('click', handleOutsideClick);
      }
    }

    // Auto-show modal if there's a message
    <?php if ($message !== ''): ?>
      document.addEventListener('DOMContentLoaded', function() {
        showModal();
      });
    <?php endif; ?>
  </script>
</body>
</html>