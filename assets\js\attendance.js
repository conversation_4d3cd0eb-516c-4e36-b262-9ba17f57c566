// assets/js/attendance.js

window.addEventListener("load", async () => {
  console.log("✅ attendance.js loaded");

  // ------------------------------------------------------------
  // 1) Detect Ethereum provider (MetaMask) and request accounts
  // ------------------------------------------------------------
  if (typeof window.ethereum === "undefined") {
    console.error("❌ window.ethereum not found. Install MetaMask!");
    alert("MetaMask not detected in this browser. Please install and unlock MetaMask.");
    return;
  }

  // We know window.ethereum exists. Create web3Write from it.
  const web3Write = new Web3(window.ethereum);

  // Immediately request account access so MetaMask pops up now
  let currentAccount = null;
  try {
    console.log("ℹ️ Requesting MetaMask accounts…");
    const accounts = await window.ethereum.request({ method: "eth_requestAccounts" });
    if (accounts.length === 0) {
      console.warn("⚠️ MetaMask is unlocked but no accounts available.");
      alert("Please create or unlock an account in MetaMask to continue.");
      return;
    }
    currentAccount = accounts[0];
    console.log("✅ MetaMask account:", currentAccount);
  } catch (err) {
    console.error("❌ MetaMask access request denied or error:", err);
    alert("MetaMask access was denied. Please allow access to continue.");
    return;
  }

  // Listen for account / network changes
  window.ethereum.on("accountsChanged", (accs) => {
    currentAccount = accs[0] || null;
    console.log("🔄 accountsChanged, new currentAccount:", currentAccount);
    if (!currentAccount) {
      alert("MetaMask account disconnected. Please connect again.");
    }
  });
  window.ethereum.on("chainChanged", () => {
    console.log("🔄 chainChanged, reloading page...");
    window.location.reload();
  });

  // ------------------------------------------------------------
  // 2) Create a read-only Web3 instance (HTTP) for history
  // ------------------------------------------------------------
  const GANACHE_HTTP = "http://127.0.0.1:7545";
  const web3Read = new Web3(new Web3.providers.HttpProvider(GANACHE_HTTP));

  // ------------------------------------------------------------
  // 3) Get network ID using read-only provider
  // ------------------------------------------------------------
  let networkId;
  try {
    networkId = await web3Read.eth.net.getId();
    console.log("✅ Connected to Ganache network ID:", networkId);
  } catch (err) {
    console.error("❌ Cannot connect to Ganache:", err);
    alert("Cannot connect to the blockchain node. Make sure Ganache is running at " + GANACHE_HTTP);
    return;
  }

  // ------------------------------------------------------------
  // 4) Load Attendance.json (ABI + networks)
  // ------------------------------------------------------------
  let AttendanceArtifact;
  try {
    console.log("ℹ️ Fetching Attendance.json from ../assets/js/Attendance.json");
    const response = await fetch("../assets/js/Attendance.json");
    if (!response.ok) throw new Error("HTTP " + response.status);
    AttendanceArtifact = await response.json();
    console.log("✅ Loaded Attendance.json:", AttendanceArtifact);
  } catch (err) {
    console.error("❌ Failed to load Attendance.json:", err);
    alert("Cannot load contract ABI. Ensure ../assets/js/Attendance.json exists.");
    return;
  }

  // ------------------------------------------------------------
  // 5) Confirm the contract is deployed on this network
  // ------------------------------------------------------------
  const deployedNetwork = AttendanceArtifact.networks[networkId];
  if (!deployedNetwork) {
    console.error(`❌ Attendance contract not deployed on network ${networkId}`);
    alert("Attendance contract is not deployed on this Ganache network.");
    return;
  }
  const contractAddress = deployedNetwork.address;
  console.log("✅ Attendance contract address:", contractAddress);

  // ------------------------------------------------------------
  // 6) Instantiate two contract instances:
  //    - contractWrite (for sending transactions via MetaMask)
  //    - contractRead  (for read-only calls via HTTP)
  // ------------------------------------------------------------
  const contractWrite = new web3Write.eth.Contract(
    AttendanceArtifact.abi,
    contractAddress
  );
  const contractRead = new web3Read.eth.Contract(
    AttendanceArtifact.abi,
    contractAddress
  );

  // ------------------------------------------------------------
  // 7) Bind “Mark Attendance” button, but only if enabled
  // ------------------------------------------------------------
  const markBtn = document.getElementById("markAttendanceBtn");
  if (!markBtn) {
    console.error("❌ Cannot find #markAttendanceBtn in DOM");
    return;
  }

  // If the button is enabled, let’s attach a click listener
  if (!markBtn.disabled) {
    console.log("ℹ️ Binding click listener to Mark Attendance button");
    markBtn.addEventListener("click", async () => {
      // Double-check that the button is not disabled
      if (markBtn.disabled) {
        console.warn("⚠️ markAttendanceBtn was disabled at click time");
        return;
      }

      const inputEl = document.getElementById("courseIdInput");
      const courseId = parseInt(inputEl.value);
      if (isNaN(courseId) || courseId <= 0) {
        alert("Enter a valid Course ID (positive integer).");
        return;
      }
      if (!currentAccount) {
        alert("MetaMask account not available. Please reconnect.");
        return;
      }

      try {
        console.log(`ℹ️ Sending markAttendance(${courseId}) from ${currentAccount}`);
        await contractWrite.methods
          .markAttendance(courseId)
          .send({ from: currentAccount })
          .on("transactionHash", (hash) => {
            console.log("📡 Transaction hash:", hash);
          })
          .on("receipt", (receipt) => {
            console.log("📖 Transaction receipt:", receipt);
            alert("✅ Attendance recorded on blockchain!\nTxHash: " + receipt.transactionHash);

            // Save txHash off-chain
            fetch("../save_tx.php", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                courseId: courseId,
                txHash: receipt.transactionHash
              }),
            })
            .then((res) => res.json())
            .then((data) => {
              console.log("📥 save_tx response:", data);
            })
            .catch((err) => {
              console.error("❌ AJAX to save_tx.php failed:", err);
            });
          })
          .on("error", (err) => {
            console.error("❌ Error in markAttendance call:", err);
            alert("Failed to mark attendance on-chain. Check console for details.");
          });
      } catch (err) {
        console.error("❌ Unexpected error sending transaction:", err);
        alert("An unexpected error occurred. See console.");
      }
    });
  } else {
    console.log("ℹ️ markAttendanceBtn is disabled; no click listener bound");
  }

  // ------------------------------------------------------------
  // 8) AUTO-TRIGGER if a valid courseId was provided by QR scan
  // ------------------------------------------------------------
  const prefilledInput = document.getElementById("courseIdInput");
  if (prefilledInput && markBtn && !markBtn.disabled) {
    const preValue = prefilledInput.value.trim();
    if (preValue && !isNaN(preValue) && Number(preValue) > 0) {
      console.log("ℹ️ Prepopulated courseId detected; will click in 2 seconds");
      setTimeout(() => {
        markBtn.click();
      }, 2000);
    }
  }

  // --------------------------------------------------------------------
  // 9) Load Student’s On-Chain History (if #historyList exists)
  // --------------------------------------------------------------------
  const historyList = document.getElementById("historyList");
  if (historyList) {
    try {
      const total = await contractRead.methods.getRecordCount().call();
      console.log("ℹ️ getRecordCount returned:", total);
      historyList.innerHTML = "";
      for (let i = 0; i < total; i++) {
        const rec = await contractRead.methods.getRecord(i).call();
        const [studentAddr, recCourseId, timestamp] = rec;
        if (studentAddr.toLowerCase() === (currentAccount || "").toLowerCase()) {
          const date = new Date(timestamp * 1000).toLocaleString();
          const li = document.createElement("li");
          li.textContent = `Course ${recCourseId} — ${date}`;
          historyList.appendChild(li);
        }
      }
    } catch (err) {
      console.error("❌ Error loading attendance history:", err);
      historyList.innerHTML = "<li>Error loading history</li>";
    }
  }
});
