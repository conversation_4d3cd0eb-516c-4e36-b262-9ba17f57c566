<?php
/**
 * Forgot Password Email Verification Page
 * Shows confirmation that password reset email has been sent
 * Provides options to resend email or update email address
 * Follows the same pattern as password_reset_email.php for first time login
 */

session_start();
require '../config/config.php';
require_once __DIR__ . '/../includes/PasswordResetService.php';

// Security check: only allow access during forgot password process
if (!isset($_SESSION['forgot_password_email_user_id']) || !isset($_SESSION['forgot_password_email_role'])) {
    header("Location: forgot_password.php");
    exit();
}

$user_id = $_SESSION['forgot_password_email_user_id'];
$role = $_SESSION['forgot_password_email_role'];
$message = '';
$messageType = 'info';

// Get user information
$table = ($role === 'student') ? 'student' : 'lecturer';
$id_field = ($role === 'student') ? 'UserID' : 'UserID';

$stmt = $conn->prepare("SELECT Name, Email FROM $table WHERE UserID = ?");
$stmt->bind_param("s", $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows !== 1) {
    header("Location: forgot_password.php");
    exit();
}

$user = $result->fetch_assoc();
$userName = $user['Name'];
$userEmail = $user['Email'];

$passwordResetService = new PasswordResetService($conn);

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['resend_email'])) {
        // Resend forgot password email
        $result = $passwordResetService->initiateForgotPasswordReset($user_id, $role, $userEmail, $userName);

        if ($result['success']) {
            $message = "Password reset email has been resent to your registered email address.";
            $messageType = 'success';
        } else {
            $message = "Failed to resend email: " . $result['message'];
            $messageType = 'error';
        }
        
    } elseif (isset($_POST['update_email'])) {
        // Update email address
        $newEmail = trim($_POST['new_email']);
        
        if (empty($newEmail)) {
            $message = "Please enter a valid email address.";
            $messageType = 'error';
        } elseif (!filter_var($newEmail, FILTER_VALIDATE_EMAIL)) {
            $message = "Please enter a valid email address format.";
            $messageType = 'error';
        } else {
            // Update email in database
            $updateStmt = $conn->prepare("UPDATE $table SET Email = ? WHERE UserID = ?");
            $updateStmt->bind_param("ss", $newEmail, $user_id);
            
            if ($updateStmt->execute()) {
                $userEmail = $newEmail;
                
                // Send forgot password email to new address
                $result = $passwordResetService->initiateForgotPasswordReset($user_id, $role, $newEmail, $userName);
                
                if ($result['success']) {
                    $message = "Email address updated successfully. Password reset email has been sent to your new email address.";
                    $messageType = 'success';
                } else {
                    $message = "Email address updated, but failed to send password reset email: " . $result['message'];
                    $messageType = 'warning';
                }
            } else {
                $message = "Failed to update email address. Please try again.";
                $messageType = 'error';
            }
        }
    }
}

// If no email has been sent yet, send initial forgot password email
if (!isset($_POST['resend_email']) && !isset($_POST['update_email']) && empty($message)) {
    $result = $passwordResetService->initiateForgotPasswordReset($user_id, $role, $userEmail, $userName);

    if ($result['success']) {
        $message = "A secure password reset link has been sent to your registered email address.";
        $messageType = 'success';
    } else {
        $message = "Failed to send password reset email: " . $result['message'];
        $messageType = 'error';
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification - UTHM Attendance</title>
    
    <!-- ─── GLOBAL CSS (same as other dashboards) ─── -->
    <link rel="stylesheet" href="../dashboard/css/base-styles.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-header.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-sidebar.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-footer.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-dashboard-styles.css" />
    
    <!-- ─── ICONS ─── -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        /* ─────────── EMAIL VERIFICATION PAGE SPECIFIC STYLES ─────────── */
        .verification-container {
            max-width: 600px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }
        
        .verification-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-md);
            text-align: center;
        }
        
        .verification-icon {
            font-size: 4rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-lg);
        }
        
        .verification-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
        }
        
        .verification-subtitle {
            font-size: 1rem;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xl);
            line-height: 1.6;
        }
        
        .email-display {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: var(--border-radius);
            padding: var(--spacing-md);
            margin: var(--spacing-lg) 0;
            font-weight: 600;
            color: var(--primary-color);
        }
        
        .help-section {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: var(--spacing-lg);
            margin-top: var(--spacing-xl);
            text-align: left;
        }
        
        .help-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .help-content {
            color: var(--text-secondary);
            line-height: 1.6;
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-md);
            justify-content: center;
            margin-top: var(--spacing-lg);
            flex-wrap: wrap;
        }
        
        .btn-resend {
            background: #3b82f6;
            color: #ffffff;
            border: 2px solid #3b82f6;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            min-height: 48px;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
        }

        .btn-resend:hover {
            background: #2563eb;
            border-color: #2563eb;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }

        .btn-update {
            background: #10b981;
            color: #ffffff;
            border: 2px solid #10b981;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            min-height: 48px;
            box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
        }

        .btn-update:hover {
            background: #059669;
            border-color: #059669;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
        }
        
        .btn-back {
            background: #6b7280;
            color: #ffffff;
            border: 2px solid #6b7280;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            min-height: 48px;
            text-decoration: none;
            box-shadow: 0 2px 4px rgba(107, 114, 128, 0.2);
        }

        .btn-back:hover {
            background: #495057;
            border-color: #495057;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(107, 114, 128, 0.4);
            color: #ffffff;
            text-decoration: none;
        }
        
        .update-email-form {
            display: none;
            margin-top: var(--spacing-lg);
            padding: var(--spacing-lg);
            background: rgba(16, 185, 129, 0.05);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: var(--border-radius);
        }
        
        .update-email-form.show {
            display: block;
            animation: slideDown 0.3s ease-out;
        }
        
        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .form-group {
            margin-bottom: var(--spacing-md);
            text-align: left;
        }
        
        .form-group label {
            display: block;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }
        
        .form-input {
            width: 100%;
            padding: var(--spacing-md);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .form-actions {
            display: flex;
            gap: var(--spacing-sm);
            justify-content: flex-end;
        }
        
        .btn-cancel {
            background: #6b7280;
            color: #ffffff;
            border: 2px solid #6b7280;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .btn-cancel:hover {
            background: #495057;
            border-color: #495057;
        }

        .btn-submit {
            background: #10b981;
            color: #ffffff;
            border: 2px solid #10b981;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .btn-submit:hover {
            background: #059669;
            border-color: #059669;
        }
        
        /* ─────────── RESPONSIVE DESIGN ─────────── */
        @media (max-width: 768px) {
            .verification-container {
                padding: var(--spacing-lg);
            }
            
            .verification-card {
                padding: var(--spacing-lg);
            }
            
            .verification-icon {
                font-size: 3rem;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn-resend, .btn-update, .btn-back {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-left">
            <img src="../assets/images/logo-uthm2.png" alt="UTHM Logo" class="logo">
        </div>
        <div class="header-right">
            <span class="user-id"><?= htmlspecialchars($user_id) ?></span>
        </div>
    </div>

    <div class="container">
        <div class="verification-container">
            <!-- Main Verification Card -->
            <div class="verification-card">
                <div class="verification-icon">
                    <i class="fas fa-envelope-open-text"></i>
                </div>

                <h1 class="verification-title">Password Reset Email Sent</h1>
                <p class="verification-subtitle">
                    Hello, <strong><?= htmlspecialchars($userName) ?></strong>!
                    We've sent a secure password reset link to your email address. Please check your email to continue.
                </p>

                <!-- Message Display -->
                <?php if (!empty($message)): ?>
                    <div class="popup-message <?= $messageType ?>" style="margin: var(--spacing-lg) 0;">
                        <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : ($messageType === 'error' ? 'times-circle' : 'info-circle') ?>"></i>
                        <?= htmlspecialchars($message) ?>
                    </div>
                <?php endif; ?>

                <!-- Email Display -->
                <div class="email-display">
                    <i class="fas fa-envelope"></i>
                    Email sent to: <?= htmlspecialchars($userEmail) ?>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <form method="post" style="display: inline;">
                        <button type="submit" name="resend_email" class="btn-resend">
                            <i class="fas fa-redo"></i>
                            Resend Email
                        </button>
                    </form>

                    <button type="button" class="btn-update" onclick="toggleUpdateForm()">
                        <i class="fas fa-edit"></i>
                        Update Email Address
                    </button>

                    <a href="forgot_password.php?reset=true" class="btn-back">
                        <i class="fas fa-arrow-left"></i>
                        Try Different Account
                    </a>
                </div>

                <!-- Update Email Form -->
                <div id="updateEmailForm" class="update-email-form">
                    <form method="post">
                        <div class="form-group">
                            <label for="new_email">New Email Address:</label>
                            <input type="email" id="new_email" name="new_email" class="form-input"
                                   placeholder="Enter your new email address" required>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="btn-cancel" onclick="toggleUpdateForm()">
                                Cancel
                            </button>
                            <button type="submit" name="update_email" class="btn-submit">
                                Update & Send Email
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Help Section -->
            <div class="help-section">
                <h3 class="help-title">
                    <i class="fas fa-question-circle"></i>
                    Didn't receive the email?
                </h3>
                <div class="help-content">
                    <p><strong>Please check the following:</strong></p>
                    <ul>
                        <li>Check your <strong>spam/junk folder</strong> - automated emails sometimes end up there</li>
                        <li>Wait a few minutes - email delivery can take up to 5 minutes</li>
                        <li>Verify your email address is correct</li>
                        <li>Contact your system administrator if you continue to have issues</li>
                    </ul>

                    <p style="margin-top: var(--spacing-md);"><strong>Security Note:</strong></p>
                    <ul>
                        <li>The password reset link will expire in <strong>1 hour</strong> for security</li>
                        <li>You can request a new link if the current one expires</li>
                        <li>Never share the reset link with anyone else</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <p>UNIVERSITI TUN HUSSEIN ONN MALAYSIA</p>
    </footer>

    <script>
        function toggleUpdateForm() {
            const form = document.getElementById('updateEmailForm');
            form.classList.toggle('show');

            if (form.classList.contains('show')) {
                document.getElementById('new_email').focus();
            }
        }

        // Auto-hide success messages after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const successMessages = document.querySelectorAll('.popup-message.success');
            successMessages.forEach(function(message) {
                setTimeout(function() {
                    message.style.opacity = '0';
                    setTimeout(function() {
                        message.style.display = 'none';
                    }, 300);
                }, 5000);
            });
        });
    </script>
</body>
</html>
