// assets/js/attendance_read.js

window.addEventListener("load", async () => {
  // 1) Connect to Ganache via HTTP (read-only)
  const GANACHE_HTTP = "http://127.0.0.1:7545";
  const web3Read = new Web3(new Web3.providers.HttpProvider(GANACHE_HTTP));

  // 2) Get network ID to ensure we’re on the correct chain
  let networkId;
  try {
    networkId = await web3Read.eth.net.getId();
  } catch (err) {
    console.error("Cannot connect to Ganache HTTP provider:", err);
    alert("Cannot connect to the blockchain node. Make sure Ganache is running at " + GANACHE_HTTP);
    return;
  }

  // 3) Load Attendance.json (ABI + network info)
  let AttendanceArtifact;
  try {
    const response = await fetch("../assets/js/Attendance.json");
    AttendanceArtifact = await response.json();
  } catch (err) {
    console.error("Failed to load Attendance.json:", err);
    alert("Cannot load contract ABI. Ensure ../assets/js/Attendance.json exists.");
    return;
  }

  // 4) Confirm the contract is deployed on this network
  const deployedNetwork = AttendanceArtifact.networks[networkId];
  if (!deployedNetwork) {
    alert("Attendance contract not deployed on network ID: " + networkId);
    return;
  }

  // 5) Instantiate a read-only contract instance
  const contractRead = new web3Read.eth.Contract(
    AttendanceArtifact.abi,
    deployedNetwork.address
  );

  // 6) Iterate over all <td class="onchainCheck"> cells
  const checkCells = document.querySelectorAll(".onchainCheck");
  for (let cell of checkCells) {
    const studentEth = cell.getAttribute("data-student") || "";
    const courseId   = parseInt(cell.getAttribute("data-course"), 10);
    const verCell    = cell.nextElementSibling; // the “On-Chain Verified?” cell

    // If EthAddress is missing or invalid, show N/A or ❌ immediately
    if (!studentEth || studentEth === "N/A") {
      verCell.textContent = "N/A";
      continue;
    }

    // If courseId is not valid, show “Error”
    if (isNaN(courseId) || courseId <= 0) {
      verCell.textContent = "Error";
      continue;
    }

    // 7) Otherwise, call hasAttended(courseId, studentEth)
    try {
      const attended = await contractRead.methods
        .hasAttended(courseId, studentEth)
        .call();
      verCell.textContent = attended ? "✅ Yes" : "❌ No";
    } catch (e) {
      console.error("Error verifying on-chain attendance:", e);
      verCell.textContent = "❌ No";
    }
  }
});
