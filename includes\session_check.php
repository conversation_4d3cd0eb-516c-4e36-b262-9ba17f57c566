<?php
/**
 * ===================================================================
 * SESSION SECURITY CHECK
 * Include this file at the top of protected pages for automatic
 * session validation and timeout checking
 * ===================================================================
 */

// Include session security utilities
require_once __DIR__ . '/SessionSecurity.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Configure secure session settings
SessionSecurity::configureSecureSession();

// Check if user is authenticated
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role'])) {
    // Not authenticated, redirect to login
    header("Location: " . getLoginUrl());
    exit();
}

// Validate session and check timeout (30 minutes default)
if (!SessionSecurity::validateSession(30)) {
    // Session expired or invalid
    SessionSecurity::secureLogout(getLoginUrl());
    exit();
}

// Update last activity timestamp
$_SESSION['last_activity'] = time();

/**
 * Get appropriate login URL based on current location
 */
function getLoginUrl() {
    $currentPath = $_SERVER['REQUEST_URI'];
    
    // Determine relative path to login based on current location
    if (strpos($currentPath, '/dashboard/') !== false) {
        return '../index.php';
    } elseif (strpos($currentPath, '/modules/') !== false) {
        return '../index.php';
    } else {
        return 'index.php';
    }
}

/**
 * Check if user has required role for current page
 */
function requireRole($required_role) {
    if (!isset($_SESSION['role']) || $_SESSION['role'] !== $required_role) {
        error_log("ACCESS DENIED: User ID {$_SESSION['user_id']} attempted to access $required_role area");
        SessionSecurity::secureLogout(getLoginUrl());
        exit();
    }
}

/**
 * Get current user information safely
 */
function getCurrentUser() {
    return SessionSecurity::getCurrentUser();
}

/**
 * Check for suspicious session activity
 */
function checkSessionSecurity() {
    // Check for session hijacking indicators
    if (isset($_SESSION['user_agent'])) {
        if ($_SESSION['user_agent'] !== $_SERVER['HTTP_USER_AGENT']) {
            error_log("SECURITY ALERT: Session hijacking attempt detected for User ID {$_SESSION['user_id']}");
            SessionSecurity::secureLogout(getLoginUrl());
            exit();
        }
    } else {
        $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'];
    }
    
    // Check for IP address changes (optional, can be problematic with mobile users)
    if (isset($_SESSION['ip_address'])) {
        if ($_SESSION['ip_address'] !== $_SERVER['REMOTE_ADDR']) {
            // Log but don't automatically logout (mobile users change IPs frequently)
            error_log("IP CHANGE: User ID {$_SESSION['user_id']} IP changed from {$_SESSION['ip_address']} to {$_SERVER['REMOTE_ADDR']}");
            $_SESSION['ip_address'] = $_SERVER['REMOTE_ADDR'];
        }
    } else {
        $_SESSION['ip_address'] = $_SERVER['REMOTE_ADDR'];
    }
}

// Perform security checks
checkSessionSecurity();

// Set security headers
header("X-Content-Type-Options: nosniff");
header("X-Frame-Options: DENY");
header("X-XSS-Protection: 1; mode=block");
header("Referrer-Policy: strict-origin-when-cross-origin");

// Prevent caching of sensitive pages
header("Cache-Control: no-cache, no-store, must-revalidate");
header("Pragma: no-cache");
header("Expires: 0");
?>
