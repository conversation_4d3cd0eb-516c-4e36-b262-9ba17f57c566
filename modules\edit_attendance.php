<?php
// ─────────────────────────────────────────────────────────────────────────────
//  modules/edit_attendance.php
//  Enhanced attendance editing with support for creating new records
//  and handling both assignment-based and legacy course systems
// ─────────────────────────────────────────────────────────────────────────────

session_start();
require '../config/config.php';

// Security check: only lecturers can access this
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'lecturer') {
    header("Location: ../index.php");
    exit();
}

// Database capabilities detection
function checkDatabaseCapabilities($conn) {
    $capabilities = [
        'course_assignments' => false,
        'registration_section' => false
    ];

    // Check if course_assignments table exists
    $checkAssignmentsTable = "SHOW TABLES LIKE 'course_assignments'";
    $result = $conn->query($checkAssignmentsTable);
    $capabilities['course_assignments'] = $result && $result->num_rows > 0;

    // Check if Section column exists in course_registration
    $checkSectionColumn = "SHOW COLUMNS FROM course_registration LIKE 'Section'";
    $result = $conn->query($checkSectionColumn);
    $capabilities['registration_section'] = $result && $result->num_rows > 0;

    return $capabilities;
}

$dbCapabilities = checkDatabaseCapabilities($conn);
$lecturer_id = $_SESSION['user_id'];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    // ─────────────────────────────────────────────────────────────────────────
    // UPDATE OR CREATE ATTENDANCE RECORD
    // ─────────────────────────────────────────────────────────────────────────
    if (isset($_POST['update'])) {
        $status = $_POST['status'] ?? 'Absent';
        $remark = $_POST['remark'] ?? '';
        $attendance_id = $_POST['attendance_id'] ?? '';

        // Handle existing attendance record update
        if (!empty($attendance_id)) {
            $update_query = $conn->prepare("
                UPDATE attendance_report
                SET Att_Status = ?, Remark = ?, Timestamp = CURRENT_TIMESTAMP
                WHERE AttendanceID = ?
            ");
            $update_query->bind_param("ssi", $status, $remark, $attendance_id);

            if ($update_query->execute()) {
                echo json_encode(['success' => true, 'message' => 'Attendance updated successfully']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to update attendance']);
            }
            $update_query->close();

        } else {
            // Create new attendance record
            $student_id = intval($_POST['student_id'] ?? 0);
            $course_id = intval($_POST['course_id'] ?? 0);
            $assignment_id = intval($_POST['assignment_id'] ?? 0);

            if ($student_id > 0 && $course_id > 0) {
                // Check if record already exists for today
                $check_query = $conn->prepare("
                    SELECT AttendanceID FROM attendance_report
                    WHERE StudentID = ? AND CourseID = ? AND LectID = ? AND DATE(Att_Date) = CURDATE()
                ");
                $check_query->bind_param("iii", $student_id, $course_id, $lecturer_id);
                $check_query->execute();
                $existing = $check_query->get_result();

                if ($existing->num_rows > 0) {
                    // Update existing record
                    $existing_record = $existing->fetch_assoc();
                    $existing_id = $existing_record['AttendanceID'];

                    $update_existing = $conn->prepare("
                        UPDATE attendance_report
                        SET Att_Status = ?, Remark = ?, Timestamp = CURRENT_TIMESTAMP
                        WHERE AttendanceID = ?
                    ");
                    $update_existing->bind_param("ssi", $status, $remark, $existing_id);

                    if ($update_existing->execute()) {
                        echo json_encode(['success' => true, 'message' => 'Attendance updated successfully']);
                    } else {
                        echo json_encode(['success' => false, 'message' => 'Failed to update existing attendance']);
                    }
                    $update_existing->close();

                } else {
                    // Create new record
                    $enhanced_remark = $remark;
                    if ($dbCapabilities['course_assignments'] && $assignment_id > 0) {
                        $enhanced_remark = "Assignment ID: " . $assignment_id .
                                         (!empty($remark) ? " - " . $remark : "");
                    }

                    $insert_query = $conn->prepare("
                        INSERT INTO attendance_report
                        (StudentID, CourseID, LectID, Att_Date, Att_Status, Remark, Timestamp)
                        VALUES (?, ?, ?, CURDATE(), ?, ?, NOW())
                    ");
                    $insert_query->bind_param("iiiss", $student_id, $course_id, $lecturer_id, $status, $enhanced_remark);

                    if ($insert_query->execute()) {
                        echo json_encode(['success' => true, 'message' => 'New attendance record created successfully']);
                    } else {
                        echo json_encode(['success' => false, 'message' => 'Failed to create attendance record']);
                    }
                    $insert_query->close();
                }
                $check_query->close();

            } else {
                echo json_encode(['success' => false, 'message' => 'Invalid student or course ID']);
            }
        }

        // If this is an AJAX request, don't redirect
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            exit();
        }

        header("Location: ../dashboard/attendance_report.php");
        exit();
    }

    // ─────────────────────────────────────────────────────────────────────────
    // DELETE ATTENDANCE RECORD
    // ─────────────────────────────────────────────────────────────────────────
    if (isset($_POST['delete'])) {
        $attendance_id = intval($_POST['attendance_id'] ?? 0);

        if ($attendance_id > 0) {
            // Verify the record belongs to this lecturer
            $verify_query = $conn->prepare("
                SELECT AttendanceID FROM attendance_report
                WHERE AttendanceID = ? AND LectID = ?
            ");
            $verify_query->bind_param("ii", $attendance_id, $lecturer_id);
            $verify_query->execute();
            $verify_result = $verify_query->get_result();

            if ($verify_result->num_rows > 0) {
                $delete_query = $conn->prepare("DELETE FROM attendance_report WHERE AttendanceID = ?");
                $delete_query->bind_param("i", $attendance_id);

                if ($delete_query->execute()) {
                    echo json_encode(['success' => true, 'message' => 'Attendance record deleted successfully']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Failed to delete attendance record']);
                }
                $delete_query->close();
            } else {
                echo json_encode(['success' => false, 'message' => 'Unauthorized: Record not found or access denied']);
            }
            $verify_query->close();
        } else {
            echo json_encode(['success' => false, 'message' => 'Invalid attendance ID']);
        }

        header("Location: ../dashboard/attendance_report.php");
        exit();
    }
}

// If no valid action was specified
echo json_encode(['success' => false, 'message' => 'No valid action specified']);
?>
