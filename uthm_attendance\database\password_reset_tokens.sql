-- Password Reset Tokens Table for UTHM Attendance System
-- This table stores secure tokens for email-based password reset functionality

CREATE TABLE IF NOT EXISTS password_reset_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    user_role ENUM('student', 'lecturer') NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL,
    expires_at DATETIME NOT NULL,
    used TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    used_at TIMESTAMP NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    
    INDEX idx_token (token),
    INDEX idx_user_id_role (user_id, user_role),
    INDEX idx_expires_at (expires_at),
    INDEX idx_email (email)
);

-- Clean up expired tokens (run this periodically)
-- DELETE FROM password_reset_tokens WHER<PERSON> expires_at < NOW() OR used = 1;
