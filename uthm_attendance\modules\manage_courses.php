<?php
session_start();
require '../config/config.php';

// ─────────────────────────────────────────────────────────────
// Ensure only admin can access
// ─────────────────────────────────────────────────────────────
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// ─────────────────────────────────────────────────────────────
// CSRF Protection Setup
// ─────────────────────────────────────────────────────────────
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// ─────────────────────────────────────────────────────────────
// Rate Limiting Setup
// ─────────────────────────────────────────────────────────────
$rate_limit_key = 'course_management_' . $_SERVER['REMOTE_ADDR'];
if (!isset($_SESSION[$rate_limit_key])) {
    $_SESSION[$rate_limit_key] = ['count' => 0, 'last_attempt' => time()];
}

// Reset rate limiting after 5 minutes
if (time() - $_SESSION[$rate_limit_key]['last_attempt'] > 300) {
    $_SESSION[$rate_limit_key] = ['count' => 0, 'last_attempt' => time()];
}

// ─────────────────────────────────────────────────────────────
// Database Functions for Course Management
// ─────────────────────────────────────────────────────────────

// Function to create course_assignments table if it doesn't exist
function ensureCourseAssignmentsTable($conn) {
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS course_assignments (
            AssignmentID INT AUTO_INCREMENT PRIMARY KEY,
            CourseID INT NOT NULL,
            LectID INT NOT NULL,
            Section ENUM('Section 1', 'Section 2') NOT NULL,
            Status ENUM('Active', 'Inactive') DEFAULT 'Active',
            CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_assignment (CourseID, LectID, Section),
            FOREIGN KEY (CourseID) REFERENCES course(CourseID) ON DELETE CASCADE,
            FOREIGN KEY (LectID) REFERENCES lecturer(LectID) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ";

    if (!$conn->query($createTableSQL)) {
        error_log("Failed to create course_assignments table: " . $conn->error);
    }
}

// Function to create system_settings table for global settings
function ensureSystemSettingsTable($conn) {
    // First, check if table exists
    $checkTable = "SHOW TABLES LIKE 'system_settings'";
    $tableResult = $conn->query($checkTable);
    $tableExists = $tableResult && $tableResult->num_rows > 0;

    if (!$tableExists) {
        // Create table with minimal required columns
        $createTableSQL = "
            CREATE TABLE system_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) NOT NULL UNIQUE,
                setting_value TEXT NOT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ";

        if (!$conn->query($createTableSQL)) {
            error_log("Failed to create system_settings table: " . $conn->error);
            return;
        }
    }

    // Insert default registration setting if it doesn't exist
    $checkSetting = "SELECT id FROM system_settings WHERE setting_key = 'course_registration_enabled'";
    $result = $conn->query($checkSetting);
    if ($result && $result->num_rows == 0) {
        // Insert with only the required columns
        $insertDefault = "
            INSERT INTO system_settings (setting_key, setting_value)
            VALUES ('course_registration_enabled', '1')
        ";
        if (!$conn->query($insertDefault)) {
            error_log("Failed to insert default registration setting: " . $conn->error);
        }
    }
}

// Function to modify course table to support new structure
function modifyCourseTable($conn) {
    // Check if Course_Code column exists before adding it
    $checkCodeColumn = "SHOW COLUMNS FROM course LIKE 'Course_Code'";
    $result = $conn->query($checkCodeColumn);
    if ($result && $result->num_rows == 0) {
        $addCodeSQL = "ALTER TABLE course ADD COLUMN Course_Code VARCHAR(20) UNIQUE AFTER CourseID";
        $conn->query($addCodeSQL);
    }

    // Make LectID nullable (this is safe to run multiple times)
    $modifySQL = "ALTER TABLE course MODIFY LectID INT NULL";
    $conn->query($modifySQL);

    // Check if Status column exists before adding it
    $checkStatusColumn = "SHOW COLUMNS FROM course LIKE 'Status'";
    $result = $conn->query($checkStatusColumn);
    if ($result && $result->num_rows == 0) {
        $addStatusSQL = "ALTER TABLE course ADD COLUMN Status ENUM('Active', 'Inactive') DEFAULT 'Active' AFTER Course_Name";
        $conn->query($addStatusSQL);
    }
}

// Functions to manage global registration setting
function getRegistrationStatus($conn) {
    // Check if table exists first
    $checkTable = "SHOW TABLES LIKE 'system_settings'";
    if ($conn->query($checkTable)->num_rows == 0) {
        return true; // Default to enabled if table doesn't exist
    }

    $stmt = $conn->prepare("SELECT setting_value FROM system_settings WHERE setting_key = 'course_registration_enabled'");
    if ($stmt) {
        $stmt->execute();
        $result = $stmt->get_result();
        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            return (bool)$row['setting_value'];
        }
        $stmt->close();
    }
    return true; // Default to enabled if setting doesn't exist
}

function updateRegistrationStatus($conn, $enabled) {
    $value = $enabled ? '1' : '0';

    // Check if setting exists, if not create it
    $checkStmt = $conn->prepare("SELECT id FROM system_settings WHERE setting_key = 'course_registration_enabled'");
    if (!$checkStmt) {
        return false;
    }

    $checkStmt->execute();
    $result = $checkStmt->get_result();

    if ($result->num_rows > 0) {
        // Update existing setting (only update columns that definitely exist)
        $stmt = $conn->prepare("
            UPDATE system_settings
            SET setting_value = ?, updated_at = NOW()
            WHERE setting_key = 'course_registration_enabled'
        ");
        if ($stmt) {
            $stmt->bind_param("s", $value);
            $success = $stmt->execute();
            $stmt->close();
        } else {
            $success = false;
        }
    } else {
        // Insert new setting (only use columns that definitely exist)
        $stmt = $conn->prepare("
            INSERT INTO system_settings (setting_key, setting_value)
            VALUES ('course_registration_enabled', ?)
        ");
        if ($stmt) {
            $stmt->bind_param("s", $value);
            $success = $stmt->execute();
            $stmt->close();
        } else {
            $success = false;
        }
    }

    $checkStmt->close();
    return $success ?? false;
}

// Initialize database structure
ensureSystemSettingsTable($conn);
ensureCourseAssignmentsTable($conn);
modifyCourseTable($conn);

// ─────────────────────────────────────────────────────────────
// Validation Functions
// ─────────────────────────────────────────────────────────────
function validateCourseCode($code) {
    if (empty($code)) return "Course code is required.";
    if (strlen($code) < 3 || strlen($code) > 20) return "Course code must be between 3 and 20 characters.";
    if (!preg_match('/^[A-Z0-9]+$/', $code)) return "Course code must contain only uppercase letters and numbers.";
    return true;
}

function validateCourseName($name) {
    if (empty($name)) return "Course name is required.";
    if (strlen($name) < 3 || strlen($name) > 100) return "Course name must be between 3 and 100 characters.";
    if (!preg_match('/^[a-zA-Z0-9\s\-\&\(\)\.]+$/', $name)) return "Course name contains invalid characters.";
    return true;
}

function checkDuplicateCourse($conn, $courseCode, $courseName) {
    // Check if Course_Code column exists first
    $checkColumn = "SHOW COLUMNS FROM course LIKE 'Course_Code'";
    $columnResult = $conn->query($checkColumn);

    if ($columnResult && $columnResult->num_rows > 0) {
        // Course_Code column exists, check both code and name
        $stmt = $conn->prepare("SELECT CourseID FROM course WHERE Course_Code = ? OR Course_Name = ?");
        $stmt->bind_param("ss", $courseCode, $courseName);
    } else {
        // Course_Code column doesn't exist, only check name
        $stmt = $conn->prepare("SELECT CourseID FROM course WHERE Course_Name = ?");
        $stmt->bind_param("s", $courseName);
    }

    $stmt->execute();
    $result = $stmt->get_result();
    $exists = $result->num_rows > 0;
    $stmt->close();
    return $exists;
}

function checkDuplicateAssignment($conn, $courseId, $lectId, $section) {
    $stmt = $conn->prepare("SELECT AssignmentID FROM course_assignments WHERE CourseID = ? AND LectID = ? AND Section = ?");
    $stmt->bind_param("iis", $courseId, $lectId, $section);
    $stmt->execute();
    $result = $stmt->get_result();
    $exists = $result->num_rows > 0;
    $stmt->close();
    return $exists;
}

// ─────────────────────────────────────────────────────────────
// Initialize Variables
// ─────────────────────────────────────────────────────────────
$message = '';
$messageType = 'error';
$errors = [];

// Get current registration status
$registrationEnabled = getRegistrationStatus($conn);

// ─────────────────────────────────────────────────────────────
// Handle Form Submissions
// ─────────────────────────────────────────────────────────────
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // CSRF Protection
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $message = "Invalid security token. Please refresh the page and try again.";
    } else {
        // Rate Limiting Check
        if ($_SESSION[$rate_limit_key]['count'] >= 5) {
            $message = "Too many attempts. Please wait 5 minutes before trying again.";
        } else {
            $_SESSION[$rate_limit_key]['count']++;
            
            if (isset($_POST['add_course'])) {
                // PHASE 1: Add Course (without lecturer assignment)
                $course_code = strtoupper(trim($_POST['course_code'] ?? ''));
                $course_name = trim($_POST['course_name'] ?? '');
                
                // Validate inputs
                $codeValidation = validateCourseCode($course_code);
                if ($codeValidation !== true) {
                    $errors['course_code'] = $codeValidation;
                }
                
                $nameValidation = validateCourseName($course_name);
                if ($nameValidation !== true) {
                    $errors['course_name'] = $nameValidation;
                }
                
                // Check for duplicates
                if (empty($errors) && checkDuplicateCourse($conn, $course_code, $course_name)) {
                    $errors['duplicate'] = "A course with this code or name already exists.";
                }
                
                if (empty($errors)) {
                    // Check if Course_Code and Status columns exist
                    $checkCodeColumn = "SHOW COLUMNS FROM course LIKE 'Course_Code'";
                    $codeColumnExists = $conn->query($checkCodeColumn)->num_rows > 0;

                    $checkStatusColumn = "SHOW COLUMNS FROM course LIKE 'Status'";
                    $statusColumnExists = $conn->query($checkStatusColumn)->num_rows > 0;

                    // Build the appropriate INSERT statement based on available columns
                    if ($codeColumnExists && $statusColumnExists) {
                        $stmt = $conn->prepare("INSERT INTO course (Course_Code, Course_Name, Status) VALUES (?, ?, 'Active')");
                        $stmt->bind_param("ss", $course_code, $course_name);
                    } elseif ($codeColumnExists) {
                        $stmt = $conn->prepare("INSERT INTO course (Course_Code, Course_Name) VALUES (?, ?)");
                        $stmt->bind_param("ss", $course_code, $course_name);
                    } else {
                        // Fallback to original structure
                        $stmt = $conn->prepare("INSERT INTO course (Course_Name) VALUES (?)");
                        $stmt->bind_param("s", $course_name);
                    }

                    if ($stmt->execute()) {
                        $message = "Course added successfully! Course Code: " . htmlspecialchars($course_code);
                        $messageType = 'success';
                        $_SESSION[$rate_limit_key]['count'] = 0;
                        $_POST = []; // Clear form
                    } else {
                        $message = "Error: Unable to add course. Please try again. " . $conn->error;
                    }
                    $stmt->close();
                } else {
                    $message = "Please correct the errors below and try again.";
                }
                
            } elseif (isset($_POST['assign_lecturer'])) {
                // PHASE 2: Assign Lecturer to Course
                $course_id = intval($_POST['course_id'] ?? 0);
                $lecturer_id = intval($_POST['lecturer_id'] ?? 0);
                $section = $_POST['section'] ?? '';
                
                // Validate inputs
                if ($course_id === 0) $errors['course_id'] = "Please select a course.";
                if ($lecturer_id === 0) $errors['lecturer_id'] = "Please select a lecturer.";
                if (!in_array($section, ['Section 1', 'Section 2'])) $errors['section'] = "Please select a valid section.";
                
                // Check for duplicate assignment
                if (empty($errors) && checkDuplicateAssignment($conn, $course_id, $lecturer_id, $section)) {
                    $errors['duplicate'] = "This lecturer is already assigned to this course and section.";
                }
                
                if (empty($errors)) {
                    $stmt = $conn->prepare("INSERT INTO course_assignments (CourseID, LectID, Section) VALUES (?, ?, ?)");
                    $stmt->bind_param("iis", $course_id, $lecturer_id, $section);
                    
                    if ($stmt->execute()) {
                        $message = "Lecturer assigned successfully to course section!";
                        $messageType = 'success';
                        $_SESSION[$rate_limit_key]['count'] = 0;
                        $_POST = []; // Clear form
                    } else {
                        $message = "Error: Unable to assign lecturer. Please try again.";
                    }
                    $stmt->close();
                } else {
                    $message = "Please correct the errors below and try again.";
                }

            } elseif (isset($_POST['toggle_registration'])) {
                // TOGGLE GLOBAL REGISTRATION STATUS
                $newStatus = isset($_POST['registration_enabled']) ? true : false;

                if (updateRegistrationStatus($conn, $newStatus)) {
                    $registrationEnabled = $newStatus; // Update local variable
                    $statusText = $newStatus ? 'enabled' : 'disabled';
                    $message = "Course registration has been $statusText successfully!";
                    $messageType = 'success';
                    $_SESSION[$rate_limit_key]['count'] = 0;
                } else {
                    $message = "Error: Unable to update registration status. Please try again.";
                }
            }
        }
    }
}

// ─────────────────────────────────────────────────────────────
// Fetch Data for Display
// ─────────────────────────────────────────────────────────────

// Check which columns exist in the course table
$checkCodeColumn = "SHOW COLUMNS FROM course LIKE 'Course_Code'";
$codeColumnExists = $conn->query($checkCodeColumn)->num_rows > 0;

$checkStatusColumn = "SHOW COLUMNS FROM course LIKE 'Status'";
$statusColumnExists = $conn->query($checkStatusColumn)->num_rows > 0;

// Build the appropriate SELECT statement based on available columns
if ($codeColumnExists && $statusColumnExists) {
    $courses_query = "SELECT CourseID, Course_Code, Course_Name, Status FROM course ORDER BY Course_Code ASC";
} elseif ($codeColumnExists) {
    $courses_query = "SELECT CourseID, Course_Code, Course_Name, 'Active' as Status FROM course ORDER BY Course_Code ASC";
} else {
    $courses_query = "SELECT CourseID, CourseID as Course_Code, Course_Name, 'Active' as Status FROM course ORDER BY Course_Name ASC";
}

$courses_result = $conn->query($courses_query);

// Get all lecturers (for Phase 2 dropdown)
$lecturers_query = "SELECT LectID, Name, UserID FROM lecturer ORDER BY Name ASC";
$lecturers_result = $conn->query($lecturers_query);

// Get course assignments (for display)
if ($codeColumnExists) {
    $assignments_query = "
        SELECT
            ca.AssignmentID,
            c.Course_Code,
            c.Course_Name,
            l.Name AS LecturerName,
            l.UserID AS LecturerID,
            ca.Section,
            ca.Status,
            ca.CreatedDate
        FROM course_assignments ca
        JOIN course c ON ca.CourseID = c.CourseID
        JOIN lecturer l ON ca.LectID = l.LectID
        ORDER BY c.Course_Code ASC, ca.Section ASC
    ";
} else {
    $assignments_query = "
        SELECT
            ca.AssignmentID,
            c.CourseID as Course_Code,
            c.Course_Name,
            l.Name AS LecturerName,
            l.UserID AS LecturerID,
            ca.Section,
            ca.Status,
            ca.CreatedDate
        FROM course_assignments ca
        JOIN course c ON ca.CourseID = c.CourseID
        JOIN lecturer l ON ca.LectID = l.LectID
        ORDER BY c.Course_Name ASC, ca.Section ASC
    ";
}
$assignments_result = $conn->query($assignments_query);

// Get course count
$course_count_query = "SELECT COUNT(*) as count FROM course";
$course_count_result = $conn->query($course_count_query);
$course_count = $course_count_result->fetch_assoc()['count'];

// Get assignment count
$assignment_count_query = "SELECT COUNT(*) as count FROM course_assignments";
$assignment_count_result = $conn->query($assignment_count_query);
$assignment_count = $assignment_count_result->fetch_assoc()['count'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Manage Courses – UTHM Attendance</title>

  <!-- FontAwesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- ─── GLOBAL CSS (same as other dashboards) ─── -->
  <link rel="stylesheet" href="../dashboard/css/base-styles.css" />
  <link rel="stylesheet" href="../dashboard/css/lecturer-header.css" />
  <link rel="stylesheet" href="../dashboard/css/lecturer-sidebar.css" />
  <link rel="stylesheet" href="../dashboard/css/lecturer-footer.css" />
  <link rel="stylesheet" href="../dashboard/css/lecturer-dashboard-styles.css" />

  <!-- ─── PAGE-SPECIFIC CSS ─── -->
  <link rel="stylesheet" href="css/manage-users1.css" />
  <link rel="stylesheet" href="css/manage-courses.css" />

</head>
<body>
  <!-- ───────── HEADER ───────── -->
  <div class="header">
    <div class="header-left">
      <img src="../assets/images/logo-uthm2.png" alt="UTHM Logo" class="logo">
    </div>
    <div class="header-right">
      <span class="user-id">ADMIN</span>
    </div>
  </div>

  <div class="container">
    <!-- ───────── SIDEBAR ───────── -->
    <div class="sidebar">
      <div class="profile">
        <img src="../assets/images/user1.png" alt="Admin Profile" class="profile-pic">
        <p class="profile-name">ADMIN</p>
      </div>
      <ul class="menu">
        <li><a href="../dashboard/admin.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
        <li><a href="../modules/manage_users.php"><i class="fas fa-user-plus"></i> Manage Users</a></li>
        <li><a href="../modules/manage_courses.php" class="active"><i class="fas fa-book"></i> Manage Courses</a></li>
        <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
      </ul>
    </div>

    <!-- ───────── MAIN CONTENT ───────── -->
    <div class="main-content">
      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">Manage Courses</h1>
        <p class="page-subtitle">Create courses and assign lecturers to sections using a two-phase workflow.</p>
      </div>

      <!-- ─────────────────── GLOBAL REGISTRATION CONTROL ─────────────────── -->
      <div class="section-header">
        <h2 class="section-title"><i class="fas fa-toggle-on"></i> Global Registration Control</h2>
        <p class="section-subtitle">Enable or disable course registration for all students system-wide.</p>
      </div>

      <div class="courses-container">
        <div class="registration-toggle-container">
          <div class="toggle-status-card <?= $registrationEnabled ? 'enabled' : 'disabled' ?>">
            <div class="toggle-status-icon">
              <i class="fas <?= $registrationEnabled ? 'fa-check-circle' : 'fa-times-circle' ?>"></i>
            </div>
            <div class="toggle-status-content">
              <h3 class="toggle-status-title">
                Course Registration is <?= $registrationEnabled ? 'ENABLED' : 'DISABLED' ?>
              </h3>
              <p class="toggle-status-description">
                <?php if ($registrationEnabled): ?>
                  Students can currently register for courses. All course registration functionality is active.
                <?php else: ?>
                  Course registration is currently disabled. Students cannot register for any courses.
                <?php endif; ?>
              </p>
            </div>
            <div class="toggle-status-action">
              <form method="POST" action="" class="toggle-form" id="registrationToggleForm">
                <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token']) ?>">
                <input type="hidden" name="toggle_registration" value="1">
                <div class="toggle-switch">
                  <input type="checkbox"
                         name="registration_enabled"
                         id="registration_toggle"
                         class="toggle-input"
                         <?= $registrationEnabled ? 'checked' : '' ?>
                         onchange="confirmToggle(this)">
                  <label for="registration_toggle" class="toggle-label">
                    <span class="toggle-slider"></span>
                  </label>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>

      <!-- Popup Message (success or error) -->
      <?php if (!empty($message)): ?>
        <div class="popup-message <?= $messageType === 'success' ? 'success' : 'error' ?>" id="popup-message">
          <?php echo htmlspecialchars($message); ?>
          <button class="close-btn" onclick="closePopup()">&times;</button>
        </div>
      <?php endif; ?>

      <!-- Display individual field errors -->
      <?php if (!empty($errors)): ?>
        <div class="validation-errors" id="validation-errors">
          <h4><i class="fas fa-exclamation-triangle"></i> Please correct the following errors:</h4>
          <ul>
            <?php foreach ($errors as $field => $error): ?>
              <li><?= htmlspecialchars($error) ?></li>
            <?php endforeach; ?>
          </ul>
        </div>
      <?php endif; ?>

      <!-- ─────────────────── PHASE 1: COURSE CREATION ─────────────────── -->
      <div class="section-header">
        <h2 class="section-title"><i class="fas fa-plus-circle"></i> Phase 1: Create New Course</h2>
        <p class="section-subtitle">Add a new course to the system without lecturer assignment.</p>
      </div>

      <div class="courses-container">
        <form method="POST" action="" class="add-user-form" id="addCourseForm" novalidate>
          <!-- CSRF Token -->
          <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token']) ?>">

          <!-- Course Information Section -->
          <div class="form-section">
            <h3 class="form-section-title"><i class="fas fa-info-circle"></i> Course Information</h3>
            <div class="form-grid">
              <!-- Course Code -->
              <div class="form-group">
                <label for="course_code">Course Code <span class="required">*</span></label>
                <input type="text"
                       name="course_code"
                       id="course_code"
                       placeholder="E.g. BITM2013, BITA3023"
                       required
                       maxlength="20"
                       pattern="^[A-Z0-9]+$"
                       title="Only uppercase letters and numbers are allowed"
                       class="form-input <?= isset($errors['course_code']) ? 'error' : '' ?>"
                       value="<?= htmlspecialchars($_POST['course_code'] ?? '') ?>">
                <small class="field-hint">Use uppercase letters and numbers only (e.g., BITM2013).</small>
                <?php if (isset($errors['course_code'])): ?>
                  <span class="error-message"><?= htmlspecialchars($errors['course_code']) ?></span>
                <?php endif; ?>
              </div>

              <!-- Course Name -->
              <div class="form-group">
                <label for="course_name">Course Name <span class="required">*</span></label>
                <input type="text"
                       name="course_name"
                       id="course_name"
                       placeholder="E.g. Database Systems, Web Programming"
                       required
                       maxlength="100"
                       class="form-input <?= isset($errors['course_name']) ? 'error' : '' ?>"
                       value="<?= htmlspecialchars($_POST['course_name'] ?? '') ?>">
                <small class="field-hint">Enter the full course name (3-100 characters).</small>
                <?php if (isset($errors['course_name'])): ?>
                  <span class="error-message"><?= htmlspecialchars($errors['course_name']) ?></span>
                <?php endif; ?>
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="form-actions">
            <button type="submit"
                    name="add_course"
                    class="action-btn primary"
                    id="add-course-btn">
              <i class="fas fa-plus"></i> Create Course
            </button>
          </div>
        </form>
      </div>

      <!-- ─────────────────── PHASE 2: LECTURER ASSIGNMENT ─────────────────── -->
      <div class="section-header">
        <h2 class="section-title"><i class="fas fa-user-tie"></i> Phase 2: Assign Lecturer to Course Section</h2>
        <p class="section-subtitle">Assign lecturers to existing courses with specific sections.</p>
      </div>

      <div class="courses-container">
        <form method="POST" action="" class="add-user-form" id="assignLecturerForm" novalidate>
          <!-- CSRF Token -->
          <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token']) ?>">

          <!-- Assignment Information Section -->
          <div class="form-section">
            <h3 class="form-section-title"><i class="fas fa-link"></i> Assignment Information</h3>
            <div class="form-grid">
              <!-- Course Selection -->
              <div class="form-group">
                <label for="course_id">Select Course <span class="required">*</span></label>
                <select name="course_id" id="course_id" required class="form-input <?= isset($errors['course_id']) ? 'error' : '' ?>">
                  <option value="" disabled selected>-- Select Course --</option>
                  <?php
                  $courses_result->data_seek(0); // Reset result pointer
                  while ($course = $courses_result->fetch_assoc()): ?>
                    <option value="<?= $course['CourseID'] ?>" <?= ($_POST['course_id'] ?? '') == $course['CourseID'] ? 'selected' : '' ?>>
                      <?= htmlspecialchars($course['Course_Code']) ?> - <?= htmlspecialchars($course['Course_Name']) ?>
                    </option>
                  <?php endwhile; ?>
                </select>
                <?php if (isset($errors['course_id'])): ?>
                  <span class="error-message"><?= htmlspecialchars($errors['course_id']) ?></span>
                <?php endif; ?>
              </div>

              <!-- Lecturer Selection -->
              <div class="form-group">
                <label for="lecturer_id">Select Lecturer <span class="required">*</span></label>
                <select name="lecturer_id" id="lecturer_id" required class="form-input <?= isset($errors['lecturer_id']) ? 'error' : '' ?>">
                  <option value="" disabled selected>-- Select Lecturer --</option>
                  <?php while ($lecturer = $lecturers_result->fetch_assoc()): ?>
                    <option value="<?= $lecturer['LectID'] ?>" <?= ($_POST['lecturer_id'] ?? '') == $lecturer['LectID'] ? 'selected' : '' ?>>
                      <?= htmlspecialchars($lecturer['Name']) ?> (<?= htmlspecialchars($lecturer['UserID']) ?>)
                    </option>
                  <?php endwhile; ?>
                </select>
                <?php if (isset($errors['lecturer_id'])): ?>
                  <span class="error-message"><?= htmlspecialchars($errors['lecturer_id']) ?></span>
                <?php endif; ?>
              </div>

              <!-- Section Selection -->
              <div class="form-group">
                <label for="section">Select Section <span class="required">*</span></label>
                <select name="section" id="section" required class="form-input <?= isset($errors['section']) ? 'error' : '' ?>">
                  <option value="" disabled selected>-- Select Section --</option>
                  <option value="Section 1" <?= ($_POST['section'] ?? '') === 'Section 1' ? 'selected' : '' ?>>Section 1</option>
                  <option value="Section 2" <?= ($_POST['section'] ?? '') === 'Section 2' ? 'selected' : '' ?>>Section 2</option>
                </select>
                <small class="field-hint">Each course has exactly 2 sections. Each lecturer can only be assigned to one section per course.</small>
                <?php if (isset($errors['section'])): ?>
                  <span class="error-message"><?= htmlspecialchars($errors['section']) ?></span>
                <?php endif; ?>
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="form-actions">
            <button type="submit"
                    name="assign_lecturer"
                    class="action-btn primary"
                    id="assign-lecturer-btn">
              <i class="fas fa-user-tie"></i> Assign Lecturer
            </button>
          </div>
        </form>
      </div>

      <!-- ─────────────────── COURSE MANAGEMENT INTERFACE ─────────────────── -->
      <div class="section-header">
        <h2 class="section-title"><i class="fas fa-list-alt"></i> Course Management</h2>
        <p class="section-subtitle">View and manage all courses and lecturer assignments in the system.</p>
      </div>

      <!-- Course Management Filter Interface -->
      <div class="user-filter-container">
        <div class="filter-tabs">
          <button class="filter-tab active" data-filter="courses" id="courses-tab">
            <i class="fas fa-book"></i>
            Courses
            <span class="user-count-badge"><?= $course_count ?></span>
          </button>
          <button class="filter-tab" data-filter="assignments" id="assignments-tab">
            <i class="fas fa-user-tie"></i>
            Assignments
            <span class="user-count-badge"><?= $assignment_count ?></span>
          </button>
        </div>

        <!-- Courses Table -->
        <div class="user-table-container active" id="courses-container">
          <?php if ($courses_result && $courses_result->num_rows > 0): ?>
            <div class="courses-container">
              <table class="courses-table">
                <thead>
                  <tr>
                    <th>Course Code</th>
                    <th>Course Name</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <?php
                  $courses_result->data_seek(0); // Reset result pointer
                  while ($course = $courses_result->fetch_assoc()): ?>
                    <tr>
                      <td data-label="Course Code">
                        <span class="user-id-badge student">
                          <?= htmlspecialchars($course['Course_Code']) ?>
                        </span>
                      </td>
                      <td data-label="Course Name">
                        <span class="user-name">
                          <?= htmlspecialchars($course['Course_Name']) ?>
                        </span>
                      </td>
                      <td data-label="Status">
                        <span class="status-badge <?= strtolower($course['Status']) ?>">
                          <?= htmlspecialchars($course['Status']) ?>
                        </span>
                      </td>
                      <td data-label="Actions">
                        <button class="action-btn view-att" title="View Details">
                          <i class="fas fa-eye"></i> View
                        </button>
                      </td>
                    </tr>
                  <?php endwhile; ?>
                </tbody>
              </table>
            </div>
          <?php else: ?>
            <div class="no-data-msg">
              <i class="fas fa-book"></i>
              <strong>No courses found.</strong><br>
              Courses you create will appear here.
            </div>
          <?php endif; ?>
        </div>

        <!-- Assignments Table -->
        <div class="user-table-container" id="assignments-container">
          <?php if ($assignments_result && $assignments_result->num_rows > 0): ?>
            <div class="courses-container">
              <table class="courses-table">
                <thead>
                  <tr>
                    <th>Course Code</th>
                    <th>Course Name</th>
                    <th>Lecturer</th>
                    <th>Section</th>
                    <th>Status</th>
                  </tr>
                </thead>
                <tbody>
                  <?php while ($assignment = $assignments_result->fetch_assoc()): ?>
                    <tr>
                      <td data-label="Course Code">
                        <span class="user-id-badge student">
                          <?= htmlspecialchars($assignment['Course_Code']) ?>
                        </span>
                      </td>
                      <td data-label="Course Name">
                        <span class="user-name">
                          <?= htmlspecialchars($assignment['Course_Name']) ?>
                        </span>
                      </td>
                      <td data-label="Lecturer">
                        <span class="user-name">
                          <?= htmlspecialchars($assignment['LecturerName']) ?>
                        </span>
                        <br>
                        <small class="faculty-text"><?= htmlspecialchars($assignment['LecturerID']) ?></small>
                      </td>
                      <td data-label="Section">
                        <span class="user-id-badge lecturer">
                          <?= htmlspecialchars($assignment['Section']) ?>
                        </span>
                      </td>
                      <td data-label="Status">
                        <span class="status-badge <?= strtolower($assignment['Status']) ?>">
                          <?= htmlspecialchars($assignment['Status']) ?>
                        </span>
                      </td>
                    </tr>
                  <?php endwhile; ?>
                </tbody>
              </table>
            </div>
          <?php else: ?>
            <div class="no-data-msg">
              <i class="fas fa-user-tie"></i>
              <strong>No lecturer assignments found.</strong><br>
              Lecturer assignments you create will appear here.
            </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>

  <!-- ───────── FOOTER ───────── -->
  <footer>
    <p>UNIVERSITI TUN HUSSEIN ONN MALAYSIA</p>
  </footer>

  <!-- ───────── JAVASCRIPT ───────── -->
  <script>
    // 1) Close popup message after a few seconds (or on × click)
    function closePopup() {
      let popup = document.getElementById("popup-message");
      if (popup) popup.style.display = "none";
    }

    document.addEventListener("DOMContentLoaded", function() {
      let popup = document.getElementById("popup-message");
      if (popup) {
        setTimeout(closePopup, 6000);
      }

      let errors = document.getElementById("validation-errors");
      if (errors) {
        setTimeout(function() { errors.style.display = "none"; }, 10000);
      }

      // Add event listeners for real-time validation
      document.getElementById("course_code").addEventListener("blur", validateCourseCode);
      document.getElementById("course_name").addEventListener("blur", validateCourseName);

      // Initialize course management filtering
      initializeCourseFiltering();
    });

    // ─────────── VALIDATION FUNCTIONS ───────────
    function validateCourseCode() {
      const input = document.getElementById("course_code");
      const value = input.value.trim().toUpperCase();
      clearFieldValidation(input);

      if (value.length < 3 || value.length > 20) {
        showFieldError(input, "Course code must be between 3 and 20 characters.");
        return false;
      }
      if (!/^[A-Z0-9]+$/.test(value)) {
        showFieldError(input, "Course code must contain only uppercase letters and numbers.");
        return false;
      }

      // Auto-convert to uppercase
      input.value = value;
      showFieldSuccess(input);
      return true;
    }

    function validateCourseName() {
      const input = document.getElementById("course_name");
      const value = input.value.trim();
      clearFieldValidation(input);

      if (value.length < 3 || value.length > 100) {
        showFieldError(input, "Course name must be between 3 and 100 characters.");
        return false;
      }
      if (!/^[a-zA-Z0-9\s\-\&\(\)\.]+$/.test(value)) {
        showFieldError(input, "Course name contains invalid characters.");
        return false;
      }

      showFieldSuccess(input);
      return true;
    }

    function clearFieldValidation(input) {
      input.classList.remove('error', 'success');
      const existingError = input.parentNode.querySelector('.error-message');
      if (existingError) existingError.remove();
    }

    function showFieldError(input, message) {
      input.classList.add('error');
      const errorSpan = document.createElement('span');
      errorSpan.className = 'error-message';
      errorSpan.textContent = message;
      input.parentNode.appendChild(errorSpan);
    }

    function showFieldSuccess(input) {
      input.classList.add('success');
    }

    // ─────────── COURSE FILTERING FUNCTIONALITY ───────────
    function initializeCourseFiltering() {
      const filterTabs = document.querySelectorAll('.filter-tab');
      filterTabs.forEach(tab => {
        tab.addEventListener('click', function() {
          const filter = this.dataset.filter;

          // Update active tab
          filterTabs.forEach(t => t.classList.remove('active'));
          this.classList.add('active');

          // Show/hide containers
          const containers = document.querySelectorAll('.user-table-container');
          containers.forEach(container => {
            container.classList.remove('active');
          });

          const targetContainer = document.getElementById(filter + '-container');
          if (targetContainer) {
            targetContainer.classList.add('active');
          }
        });
      });
    }

    // ─────────── REGISTRATION TOGGLE FUNCTIONALITY ───────────
    function confirmToggle(checkbox) {
      const isEnabled = checkbox.checked;
      const action = isEnabled ? 'enable' : 'disable';
      const message = `Are you sure you want to ${action} course registration for all students?`;

      if (confirm(message)) {
        document.getElementById('registrationToggleForm').submit();
      } else {
        // Revert the checkbox state if user cancels
        checkbox.checked = !isEnabled;
      }
    }

    // ─────────── FORM SUBMISSION VALIDATION ───────────
    document.getElementById("addCourseForm").addEventListener("submit", function(e) {
      const isValidCode = validateCourseCode();
      const isValidName = validateCourseName();

      if (!isValidCode || !isValidName) {
        e.preventDefault();
        alert("Please correct the validation errors before submitting.");
        return false;
      }
    });
  </script>
</body>
</html>
