/* ===================================================================
   STUDENT DASHBOARD — REFINED TO MATCH LECTURER DASHBOARD STYLE
   (student-dashboard-enhanced.css)
   ================================================================= */

/* ─────────── COLOR PALETTE ─────────── */
:root {
  /* Primary “purple” accent (same as lecturer) */
  --primary: #6366F1;
  --primary-dark: #4F46E5;
  --primary-light: #818CF8;

  /* Gray scale (matching lecturer) */
  --gray-50:  #F9FAFB;
  --gray-100: #F3F4F6;
  --gray-200: #E5E7EB;
  --gray-300: #D1D5DB;
  --gray-400: #9CA3AF;
  --gray-500: #6B7280;
  --gray-600: #4B5563;
  --gray-700: #374151;
  --gray-800: #1F2937;
  --gray-900: #111827;

  /* Semantic colors (if needed) */
  --success: #10B981;
  --warning: #F59E0B;
  --danger: #EF4444;
  --info: #3B82F6;

  /* Neutrals */
  --white:  #FFFFFF;
  --black:  #000000;

  /* Shadows & Borders (matching lecturer) */
  --shadow-sm: 0 1px 2px rgba(0,0,0,0.05);
  --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
  --border-color: #E5E7EB;

  /* Border radius (use 8px to match lecturer’s cards) */
  --radius: 8px;

  /* Spacing */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;

  /* Typography */
  --font-sans: 'Inter', sans-serif;
  --text-xs: 0.75rem;  /* 12px */
  --text-sm: 0.875rem; /* 14px */
  --text-base: 1rem;   /* 16px */
  --text-lg: 1.125rem; /* 18px */
  --text-xl: 1.25rem;  /* 20px */
  --text-2xl: 1.5rem;  /* 24px */
  --text-3xl: 1.875rem;/* 30px */

  /* Transitions */
  --transition: 200ms ease-in-out;
}

/* ─────────── RESET & BASE STYLES ─────────── */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-sans);
  background-color: var(--gray-50);
  color: var(--gray-800);
  line-height: 1.6;
  min-height: 100vh;
}

/* ─────────── MAIN CONTENT AREA ─────────── */
.main-content {
  flex: 1;
  padding: var(--space-lg);
  background: transparent;
}

/* ─────────── PAGE HEADER ─────────── */
.page-header {
  background-color: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  padding: var(--space-lg);
  margin-bottom: var(--space-lg);
  box-shadow: var(--shadow-sm);
}
.page-header h1 {
  font-size: var(--text-2xl);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-sm);
}
.page-header p {
  font-size: var(--text-base);
  color: var(--gray-600);
}

/* ─────────── SUMMARY CARDS ─────────── */
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.stats-card {
  background-color: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  padding: var(--space-md);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  gap: var(--space-md);
  transition: transform var(--transition), box-shadow var(--transition);
}
.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.card-icon {
  font-size: 2rem;
  color: var(--primary);
  width: 40px;
  text-align: center;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}
.card-number {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--gray-900);
}
.card-label {
  font-size: var(--text-base);
  color: var(--gray-600);
}

/* ─────────── ATTENDANCE CHART CARD ─────────── */
.chart-card {
  background-color: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  padding: var(--space-lg);
  margin-bottom: var(--space-lg);
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition), box-shadow var(--transition);
}
.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}
.chart-title {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-md);
}
.chart-title i {
  color: var(--primary);
  font-size: 1.25rem;
}
#attendanceChart {
  width: 100%;
  height: 180px; /* smaller, consistent size */
}

/* ─────────── TABLE CARD ─────────── */
.table-card {
  background-color: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  padding: var(--space-lg);
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition), box-shadow var(--transition);
}
.table-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}
.table-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-md);
}

/* ─────────── COURSES TABLE ─────────── */
.courses-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: var(--text-sm);
}

.courses-table th,
.courses-table td {
  padding: var(--space-md);
  text-align: left;
  vertical-align: middle;
}

.courses-table th {
  background-color: var(--gray-100);
  color: var(--gray-700);
  font-weight: 600;
  border-bottom: 1px solid var(--border-color);
}

.courses-table td {
  border-bottom: 1px solid var(--gray-100);
}

.courses-table tr:nth-child(even) td {
  background-color: var(--gray-100);
}

.courses-table tr:hover td {
  background-color: var(--white);
}

@media (max-width: 768px) {
  .courses-table th,
  .courses-table td {
    padding: var(--space-sm);
    font-size: var(--text-xs);
  }
}

/* ─────────── EMPTY STATE ─────────── */
.empty-state {
  background-color: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  padding: var(--space-2xl);
  text-align: center;
  box-shadow: var(--shadow-sm);
}
.empty-state i {
  font-size: 3rem;
  color: var(--gray-300);
  margin-bottom: var(--space-lg);
}
.empty-state h3 {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: var(--space-sm);
}
.empty-state p {
  font-size: var(--text-base);
  color: var(--gray-600);
  margin-bottom: var(--space-lg);
}
.cta-button {
  background-color: var(--primary);
  color: var(--white);
  text-decoration: none;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-full);
  font-weight: 600;
  font-size: var(--text-sm);
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  transition: background-color var(--transition), box-shadow var(--transition);
}
.cta-button:hover {
  background-color: var(--primary-dark);
  box-shadow: var(--shadow-sm);
}

/* ─────────── RESPONSIVE BEHAVIOR ─────────── */
@media (max-width: 992px) {
  .container {
    flex-direction: column;
  }
  .sidebar {
    width: 100%;
    border-bottom: 1px solid var(--border-color);
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: var(--space-md);
  }

  .page-header {
    padding: var(--space-md);
  }

  .page-header h1 {
    font-size: var(--text-xl);
  }

  .card-container {
    grid-template-columns: 1fr;
  }

  .stats-card {
    flex-direction: column;
    text-align: center;
  }

  #attendanceChart {
    height: 160px;
  }

  .table-card {
    padding: var(--space-md);
  }

  .courses-table th,
  .courses-table td {
    padding: var(--space-sm);
  }
}

/* ─────────── ACCESSIBILITY ─────────── */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
  }
}

/* Focus states for keyboard users */
.qr-button:focus,
.standalone-btn:focus,
.cta-button:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}
