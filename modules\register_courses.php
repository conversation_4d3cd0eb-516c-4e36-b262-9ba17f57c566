<?php
// ─────────────────────────────────────────────────────────────────────────────
//  modules/register_courses.php
//  - Updated to work with new course management structure (course codes, sections, assignments)
//  - Allows students to register for specific course sections
//  - Integrates with course_assignments table for lecturer-section relationships
// ─────────────────────────────────────────────────────────────────────────────

session_start();
require '../config/config.php';

// 1) SECURITY CHECK: Only 'student' role can access
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header("Location: ../index.php");
    exit();
}

// ─────────────────────────────────────────────────────────────────────────────
// Database Structure Compatibility Functions
// ─────────────────────────────────────────────────────────────────────────────

// Function to ensure course_registration table supports sections
function ensureCourseRegistrationTable($conn) {
    // Check if Section column exists in course_registration
    $checkSectionColumn = "SHOW COLUMNS FROM course_registration LIKE 'Section'";
    $result = $conn->query($checkSectionColumn);
    if ($result && $result->num_rows == 0) {
        $addSectionSQL = "ALTER TABLE course_registration ADD COLUMN Section ENUM('Section 1', 'Section 2') AFTER CourseID";
        $conn->query($addSectionSQL);

        // Update unique constraint to include section
        $conn->query("ALTER TABLE course_registration DROP INDEX StudentCourse");
        $conn->query("ALTER TABLE course_registration ADD UNIQUE KEY StudentCourseSection (StudentID, CourseID, Section)");
    }
}

// Function to check database capabilities
function checkDatabaseCapabilities($conn) {
    $capabilities = [
        'course_code' => false,
        'course_status' => false,
        'course_assignments' => false,
        'registration_section' => false
    ];

    // Check if Course_Code column exists
    $checkCodeColumn = "SHOW COLUMNS FROM course LIKE 'Course_Code'";
    $capabilities['course_code'] = $conn->query($checkCodeColumn)->num_rows > 0;

    // Check if Status column exists
    $checkStatusColumn = "SHOW COLUMNS FROM course LIKE 'Status'";
    $capabilities['course_status'] = $conn->query($checkStatusColumn)->num_rows > 0;

    // Check if course_assignments table exists
    $checkAssignmentsTable = "SHOW TABLES LIKE 'course_assignments'";
    $capabilities['course_assignments'] = $conn->query($checkAssignmentsTable)->num_rows > 0;

    // Check if Section column exists in course_registration
    $checkSectionColumn = "SHOW COLUMNS FROM course_registration LIKE 'Section'";
    $capabilities['registration_section'] = $conn->query($checkSectionColumn)->num_rows > 0;

    return $capabilities;
}

// Function to check global registration status
function getRegistrationStatus($conn) {
    // Check if table exists first
    $checkTable = "SHOW TABLES LIKE 'system_settings'";
    $tableResult = $conn->query($checkTable);
    if (!$tableResult || $tableResult->num_rows == 0) {
        return true; // Default to enabled if table doesn't exist
    }

    $stmt = $conn->prepare("SELECT setting_value FROM system_settings WHERE setting_key = 'course_registration_enabled'");
    if ($stmt) {
        $stmt->execute();
        $result = $stmt->get_result();
        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $stmt->close();
            return (bool)$row['setting_value'];
        }
        $stmt->close();
    }
    return true; // Default to enabled if setting doesn't exist or query fails
}

// Initialize database structure
ensureCourseRegistrationTable($conn);
$dbCapabilities = checkDatabaseCapabilities($conn);

// Check global registration status
$registrationEnabled = getRegistrationStatus($conn);

// 2) FETCH STUDENT INFO (for header/sidebar display)
$student_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT Name, UserID, Photo FROM student WHERE StudentID = ?");
$stmt->bind_param("i", $student_id);
$stmt->execute();
$res = $stmt->get_result();
$student = $res->fetch_assoc();
$student_name   = $student['Name']   ?? 'Student Name';
$student_userid = $student['UserID'] ?? 'N/A';
$stmt->close();
$photo_url = !empty($student['Photo'])
    ? "../uploads/" . rawurlencode($student['Photo'])
    : "../assets/images/user1.png";

// 3) HANDLE FORM SUBMISSIONS (register OR unregister)
$alertMsg   = "";
$alertType  = ""; // "success" or "error"

// Register course with section
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['assignment_id']) && !isset($_POST['unregister'])) {
    // Check if registration is globally enabled
    if (!$registrationEnabled) {
        $alertMsg = "Course registration is currently disabled by the administrator. Please try again later.";
        $alertType = "error";
    } else {
        $assignment_id = intval($_POST['assignment_id']);

    // Get course and section info from assignment
    if ($dbCapabilities['course_assignments']) {
        $assignmentStmt = $conn->prepare("
            SELECT ca.CourseID, ca.Section, c.Course_Name, c.Course_Code
            FROM course_assignments ca
            JOIN course c ON ca.CourseID = c.CourseID
            WHERE ca.AssignmentID = ? AND ca.Status = 'Active'
        ");
        $assignmentStmt->bind_param("i", $assignment_id);
        $assignmentStmt->execute();
        $assignmentResult = $assignmentStmt->get_result();

        if ($assignmentResult->num_rows > 0) {
            $assignment = $assignmentResult->fetch_assoc();
            $course_id = $assignment['CourseID'];
            $section = $assignment['Section'];
            $course_name = $assignment['Course_Name'];
            $course_code = $assignment['Course_Code'] ?? '';

            // Check if already registered for this course (any section)
            if ($dbCapabilities['registration_section']) {
                $cStmt = $conn->prepare("
                    SELECT Section FROM course_registration
                    WHERE StudentID = ? AND CourseID = ?
                ");
                $cStmt->bind_param("ii", $student_id, $course_id);
                $cStmt->execute();
                $existingResult = $cStmt->get_result();

                if ($existingResult->num_rows > 0) {
                    $existing = $existingResult->fetch_assoc();
                    $alertMsg = "You are already registered for this course in " . $existing['Section'] . ". You cannot register for multiple sections of the same course.";
                    $alertType = "error";
                } else {
                    // Register for the specific section
                    $iStmt = $conn->prepare("
                        INSERT INTO course_registration (StudentID, CourseID, Section)
                        VALUES (?, ?, ?)
                    ");
                    $iStmt->bind_param("iis", $student_id, $course_id, $section);
                    if ($iStmt->execute()) {
                        $courseDisplay = $course_code ? "$course_code - $course_name" : $course_name;
                        $alertMsg = "Successfully registered for $courseDisplay ($section)!";
                        $alertType = "success";
                    } else {
                        $alertMsg = "Registration failed. Please try again later.";
                        $alertType = "error";
                    }
                    $iStmt->close();
                }
                $cStmt->close();
            } else {
                // Fallback for databases without section support
                $cStmt = $conn->prepare("
                    SELECT 1 FROM course_registration
                    WHERE StudentID = ? AND CourseID = ?
                ");
                $cStmt->bind_param("ii", $student_id, $course_id);
                $cStmt->execute();
                $cStmt->store_result();
                if ($cStmt->num_rows > 0) {
                    $alertMsg = "You are already registered for this course.";
                    $alertType = "error";
                } else {
                    $iStmt = $conn->prepare("
                        INSERT INTO course_registration (StudentID, CourseID)
                        VALUES (?, ?)
                    ");
                    $iStmt->bind_param("ii", $student_id, $course_id);
                    if ($iStmt->execute()) {
                        $courseDisplay = $course_code ? "$course_code - $course_name" : $course_name;
                        $alertMsg = "Successfully registered for $courseDisplay!";
                        $alertType = "success";
                    } else {
                        $alertMsg = "Registration failed. Please try again later.";
                        $alertType = "error";
                    }
                    $iStmt->close();
                }
                $cStmt->close();
            }
        } else {
            $alertMsg = "Invalid course selection. Please try again.";
            $alertType = "error";
        }
        $assignmentStmt->close();
    } else {
        $alertMsg = "Course assignment system not available. Please contact administrator.";
        $alertType = "error";
    }
    } // Close the registration enabled check
}

// Unregister course
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['course_id']) && isset($_POST['unregister'])) {
    // Check if registration is globally enabled (affects both registration AND unregistration)
    if (!$registrationEnabled) {
        $alertMsg = "Course registration and unregistration are currently disabled by the administrator. Please try again later.";
        $alertType = "error";
    } else {
        $course_id = intval($_POST['course_id']);
        $section = $_POST['section'] ?? '';

        if ($dbCapabilities['registration_section'] && !empty($section)) {
            $dStmt = $conn->prepare("
                DELETE FROM course_registration
                WHERE StudentID = ? AND CourseID = ? AND Section = ?
            ");
            $dStmt->bind_param("iis", $student_id, $course_id, $section);
        } else {
            $dStmt = $conn->prepare("
                DELETE FROM course_registration
                WHERE StudentID = ? AND CourseID = ?
            ");
            $dStmt->bind_param("ii", $student_id, $course_id);
        }

        if ($dStmt->execute() && $dStmt->affected_rows > 0) {
            $alertMsg = "Successfully removed the course!";
            $alertType = "success";
        } else {
            $alertMsg = "Failed to remove the course. Please try again.";
            $alertType = "error";
        }
        $dStmt->close();
    }
}

// 4) FETCH REGISTERED COURSES
if ($dbCapabilities['course_assignments'] && $dbCapabilities['registration_section']) {
    // New system with course assignments and sections
    $rStmt = $conn->prepare("
        SELECT
            c.CourseID,
            c.Course_Name,
            " . ($dbCapabilities['course_code'] ? "c.Course_Code," : "c.CourseID as Course_Code,") . "
            cr.Section,
            l.Name AS LecturerName,
            l.UserID AS LecturerID
        FROM course c
        JOIN course_registration cr ON c.CourseID = cr.CourseID
        LEFT JOIN course_assignments ca ON c.CourseID = ca.CourseID AND cr.Section = ca.Section
        LEFT JOIN lecturer l ON ca.LectID = l.LectID
        WHERE cr.StudentID = ?
        ORDER BY c.Course_Name ASC, cr.Section ASC
    ");
} else if ($dbCapabilities['course_code']) {
    // Legacy system with course codes but no sections
    $rStmt = $conn->prepare("
        SELECT
            c.CourseID,
            c.Course_Name,
            c.Course_Code,
            '' as Section,
            l.Name AS LecturerName,
            l.UserID AS LecturerID
        FROM course c
        JOIN course_registration cr ON c.CourseID = cr.CourseID
        LEFT JOIN lecturer l ON c.LectID = l.LectID
        WHERE cr.StudentID = ?
        ORDER BY c.Course_Name ASC
    ");
} else {
    // Original system
    $rStmt = $conn->prepare("
        SELECT
            c.CourseID,
            c.Course_Name,
            c.CourseID as Course_Code,
            '' as Section,
            l.Name AS LecturerName,
            l.UserID AS LecturerID
        FROM course c
        JOIN course_registration cr ON c.CourseID = cr.CourseID
        LEFT JOIN lecturer l ON c.LectID = l.LectID
        WHERE cr.StudentID = ?
        ORDER BY c.Course_Name ASC
    ");
}
$rStmt->bind_param("i", $student_id);
$rStmt->execute();
$registeredCourses = $rStmt->get_result();
$rStmt->close();

// 5) FETCH AVAILABLE COURSE ASSIGNMENTS (NOT YET REGISTERED)
if ($dbCapabilities['course_assignments']) {
    // New system: fetch available course-section assignments
    $aStmt = $conn->prepare("
        SELECT
            ca.AssignmentID,
            c.CourseID,
            c.Course_Name,
            " . ($dbCapabilities['course_code'] ? "c.Course_Code," : "c.CourseID as Course_Code,") . "
            ca.Section,
            l.Name AS LecturerName,
            l.UserID AS LecturerID,
            " . ($dbCapabilities['course_status'] ? "c.Status" : "'Active' as Status") . "
        FROM course_assignments ca
        JOIN course c ON ca.CourseID = c.CourseID
        JOIN lecturer l ON ca.LectID = l.LectID
        WHERE ca.Status = 'Active'
        " . ($dbCapabilities['course_status'] ? "AND c.Status = 'Active'" : "") . "
        AND ca.AssignmentID NOT IN (
            SELECT ca2.AssignmentID
            FROM course_assignments ca2
            JOIN course_registration cr ON ca2.CourseID = cr.CourseID
            " . ($dbCapabilities['registration_section'] ? "AND ca2.Section = cr.Section" : "") . "
            WHERE cr.StudentID = ?
        )
        ORDER BY c.Course_Name ASC, ca.Section ASC
    ");
    $aStmt->bind_param("i", $student_id);
    $aStmt->execute();
    $availableCourses = $aStmt->get_result();
    $aStmt->close();
} else {
    // Legacy system: fetch courses without assignments
    if ($dbCapabilities['course_code']) {
        $aStmt = $conn->prepare("
            SELECT
                c.CourseID as AssignmentID,
                c.CourseID,
                c.Course_Name,
                c.Course_Code,
                '' as Section,
                l.Name AS LecturerName,
                l.UserID AS LecturerID,
                " . ($dbCapabilities['course_status'] ? "c.Status" : "'Active' as Status") . "
            FROM course c
            LEFT JOIN lecturer l ON c.LectID = l.LectID
            WHERE c.CourseID NOT IN (
                SELECT CourseID FROM course_registration WHERE StudentID = ?
            )
            " . ($dbCapabilities['course_status'] ? "AND c.Status = 'Active'" : "") . "
            ORDER BY c.Course_Name ASC
        ");
    } else {
        $aStmt = $conn->prepare("
            SELECT
                c.CourseID as AssignmentID,
                c.CourseID,
                c.Course_Name,
                c.CourseID as Course_Code,
                '' as Section,
                l.Name AS LecturerName,
                l.UserID AS LecturerID,
                'Active' as Status
            FROM course c
            LEFT JOIN lecturer l ON c.LectID = l.LectID
            WHERE c.CourseID NOT IN (
                SELECT CourseID FROM course_registration WHERE StudentID = ?
            )
            ORDER BY c.Course_Name ASC
        ");
    }
    $aStmt->bind_param("i", $student_id);
    $aStmt->execute();
    $availableCourses = $aStmt->get_result();
    $aStmt->close();
}

// 6) CALCULATE SUMMARY STATISTICS
$totalRegistered = $registeredCourses->num_rows;
$totalAvailable = $availableCourses->num_rows;
$totalCourses = $totalRegistered + $totalAvailable;
$completionPercentage = $totalCourses > 0 ? round(($totalRegistered / $totalCourses) * 100, 2) : 0;
$registeredCourses->data_seek(0);
$availableCourses->data_seek(0);
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Register Courses</title>

  <!-- FontAwesome for icons -->
  <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap"
        rel="stylesheet">
  <link rel="stylesheet" href="../dashboard/css/base-styles.css">
  <link rel="stylesheet" href="../dashboard/css/lecturer-header.css">
  <link rel="stylesheet" href="../dashboard/css/lecturer-sidebar.css">
  <link rel="stylesheet" href="../dashboard/css/lecturer-footer.css">
  <link rel="stylesheet" href="../dashboard/css/student-dashboard-enhanced.css">
  <link rel="stylesheet" href="../dashboard/css/register-courses.css">
</head>
<body>
  <!-- HEADER -->
  <div class="header">
    <div class="header-left">
      <img src="../assets/images/logo-uthm2.png" alt="UTHM Logo" class="logo">
    </div>
    <div class="header-right">
      <a href="../modules/qr_scan.php" class="qr-button">
        <i class="fas fa-qrcode"></i> Scan QR
      </a>
      <span class="user-id"><?= htmlspecialchars($student_userid) ?></span>
    </div>
  </div>

  <div class="container">
    <!-- SIDEBAR -->
    <div class="sidebar">
      <div class="profile">
        <img src="<?= $photo_url ?>" alt="Profile Photo" class="profile-pic">
        <p class="profile-name"><?= htmlspecialchars($student_name) ?></p>
        <p class="profile-id"><?= htmlspecialchars($student_userid) ?></p>
      </div>
      <ul class="menu">
        <li><a href="../dashboard/student.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
        <li><a href="../modules/profile_student.php"><i class="fas fa-user"></i> Profile</a></li>
        <li><a href="register_courses.php" class="active"><i class="fas fa-edit"></i> Register Courses</a></li>
        <li><a href="report.php"><i class="fas fa-book"></i> Attendance Details</a></li>
        <li><a href="../dashboard/student_blockchain_records.php"><i class="fas fa-link"></i> Blockchain Records</a></li>
        <li><a href="../dashboard/student_transaction_lookup.php"><i class="fas fa-search"></i> Transaction Lookup</a></li>
        <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
      </ul>
    </div>

    <!-- MAIN CONTENT -->
    <div class="main-content">
      <div class="page-header">
        <h1><i class="fas fa-edit"></i> Register Courses</h1>
        <p>
          <?php if ($dbCapabilities['course_assignments']): ?>
            Register for specific course sections with assigned lecturers. Each course offers multiple sections - choose the one that fits your schedule.
          <?php else: ?>
            Below are the courses you have already registered, and those you can still register. Use the search box to filter available courses.
          <?php endif; ?>
        </p>
        <?php if (!$registrationEnabled): ?>
        <div class="alert error registration-disabled-banner">
          <i class="fas fa-ban"></i>
          <div>
            <strong>Course Registration & Unregistration Disabled</strong><br>
            Course registration and unregistration are currently disabled by the administrator. You cannot register for new courses or unregister from existing courses at this time.
          </div>
        </div>
        <?php endif; ?>

        <?php if ($dbCapabilities['course_assignments'] && $registrationEnabled): ?>
        <div class="info-banner">
          <i class="fas fa-info-circle"></i>
          <span>You can only register for one section per course. Choose carefully as each section may have different lecturers and schedules.</span>
        </div>
        <?php endif; ?>
      </div>
      <div class="stats-summary">
        <div class="stat-card">
          <div class="stat-icon registered">
            <i class="fas fa-check-circle"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number"><?= $totalRegistered ?></div>
            <div class="stat-label">Registered Courses</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon available">
            <i class="fas fa-book-open"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number"><?= $totalAvailable ?></div>
            <div class="stat-label">Available Courses</div>
          </div>
        </div>
      </div>
      <?php if (!empty($alertMsg)): ?>
        <div class="alert <?= $alertType === 'success' ? 'success' : 'error' ?>">
          <span><?= htmlspecialchars($alertMsg) ?></span>
        </div>
      <?php endif; ?>

      <div class="dual-section">
        <!-- REGISTERED COURSES -->
        <div class="table-card">
          <h2 class="table-title registered-header">Registered Courses</h2>
          <?php if ($registeredCourses->num_rows > 0): ?>
            <div class="table-wrapper">
              <table class="courses-table">
                <thead>
                  <tr>
                    <th>Course Code</th>
                    <th>Course Name</th>
                    <?php if ($dbCapabilities['registration_section']): ?>
                    <th>Section</th>
                    <?php endif; ?>
                    <th>Lecturer</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <?php while ($course = $registeredCourses->fetch_assoc()): ?>
                    <tr>
                      <td data-label="Course Code">
                        <span class="course-code-badge">
                          <?= htmlspecialchars($course['Course_Code']) ?>
                        </span>
                      </td>
                      <td data-label="Course Name">
                        <i class="fas fa-book-open" style="color: var(--primary); margin-right: var(--space-xs);"></i>
                        <strong><?= htmlspecialchars($course['Course_Name']) ?></strong>
                      </td>
                      <?php if ($dbCapabilities['registration_section']): ?>
                      <td data-label="Section">
                        <span class="section-badge">
                          <?= htmlspecialchars($course['Section']) ?>
                        </span>
                      </td>
                      <?php endif; ?>
                      <td data-label="Lecturer">
                        <i class="fas fa-chalkboard-teacher" style="color: var(--info); margin-right: var(--space-xs);"></i>
                        <?= htmlspecialchars($course['LecturerName'] ?? 'Not Assigned') ?>
                        <?php if (!empty($course['LecturerID'])): ?>
                          <br><small style="color: var(--text-secondary);"><?= htmlspecialchars($course['LecturerID']) ?></small>
                        <?php endif; ?>
                      </td>
                      <td data-label="Action">
                        <form method="POST" style="display:inline;" onsubmit="return confirm('Are you sure you want to remove this course?');">
                          <input type="hidden" name="course_id" value="<?= $course['CourseID'] ?>">
                          <?php if ($dbCapabilities['registration_section']): ?>
                          <input type="hidden" name="section" value="<?= htmlspecialchars($course['Section']) ?>">
                          <?php endif; ?>
                          <button type="submit"
                                  name="unregister"
                                  value="1"
                                  class="unregister-btn"
                                  <?php if (!$registrationEnabled): ?>
                                    disabled title="Course registration and unregistration are currently disabled"
                                  <?php endif; ?>>
                            <i class="fas fa-trash-alt"></i> Unregister
                          </button>
                        </form>
                      </td>
                    </tr>
                  <?php endwhile; ?>
                </tbody>
              </table>
            </div>
          <?php else: ?>
            <div class="empty-state">
              <i class="fas fa-info-circle"></i>
              <h3>No Registered Courses</h3>
              <p>You haven’t registered for any courses yet.</p>
              <a href="#available" class="cta-button">
                <i class="fas fa-arrow-right"></i> Browse Available Courses
              </a>
            </div>
          <?php endif; ?>
        </div>
        <!-- AVAILABLE COURSES -->
        <div class="table-card" id="available">
          <h2 class="table-title">Available Courses</h2>
          <div style="display: flex; align-items: center; margin-bottom: var(--spacing-md); padding: 0 var(--spacing-lg);">
            <div class="search-box">
              <i class="fas fa-search"></i>
              <input type="text" id="courseSearch" placeholder="Search courses…">
            </div>
            <div style="margin-left: auto; font-size: 0.875rem; color: var(--text-secondary); font-weight: 500;">
              <?= $availableCourses->num_rows ?> course<?= $availableCourses->num_rows === 1 ? '' : 's' ?> available
            </div>
          </div>
          <?php if ($availableCourses->num_rows > 0): ?>
            <div class="table-wrapper">
              <table class="courses-table" id="coursesTable">
                <thead>
                  <tr>
                    <th>Course Code</th>
                    <th>Course Name</th>
                    <?php if ($dbCapabilities['course_assignments']): ?>
                    <th>Section</th>
                    <?php endif; ?>
                    <th>Lecturer</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <?php while ($course = $availableCourses->fetch_assoc()): ?>
                    <tr>
                      <td data-label="Course Code">
                        <span class="course-code-badge">
                          <?= htmlspecialchars($course['Course_Code']) ?>
                        </span>
                      </td>
                      <td data-label="Course Name">
                        <i class="fas fa-book-open" style="color: var(--primary); margin-right: var(--space-xs);"></i>
                        <strong><?= htmlspecialchars($course['Course_Name']) ?></strong>
                      </td>
                      <?php if ($dbCapabilities['course_assignments']): ?>
                      <td data-label="Section">
                        <span class="section-badge">
                          <?= htmlspecialchars($course['Section']) ?>
                        </span>
                      </td>
                      <?php endif; ?>
                      <td data-label="Lecturer">
                        <i class="fas fa-chalkboard-teacher" style="color: var(--info); margin-right: var(--space-xs);"></i>
                        <?= htmlspecialchars($course['LecturerName'] ?? 'Not Assigned') ?>
                        <?php if (!empty($course['LecturerID'])): ?>
                          <br><small style="color: var(--text-secondary);"><?= htmlspecialchars($course['LecturerID']) ?></small>
                        <?php endif; ?>
                      </td>
                      <td data-label="Action">
                        <form method="POST" style="display:inline;">
                          <input type="hidden" name="assignment_id" value="<?= $course['AssignmentID'] ?>">
                          <button type="submit" class="register-btn"
                                  <?php if (!$registrationEnabled): ?>
                                    disabled title="Course registration is currently disabled"
                                  <?php elseif (empty($course['LecturerName'])): ?>
                                    disabled title="No lecturer assigned"
                                  <?php endif; ?>>
                            <i class="fas fa-plus"></i> Register
                          </button>
                        </form>
                      </td>
                    </tr>
                  <?php endwhile; ?>
                </tbody>
              </table>
            </div>
          <?php else: ?>
            <div class="empty-state">
              <i class="fas fa-info-circle"></i>
              <h3>All courses have been registered</h3>
              <p>There are no new courses available to register at the moment.</p>
              <a href="../dashboard/student.php" class="cta-button">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
              </a>
            </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>

  <footer>
    <p>UNIVERSITI TUN HUSSEIN ONN MALAYSIA</p>
  </footer>

  <style>
    /* Enhanced Registration Disabled Banner */
    .registration-disabled-banner {
      background: linear-gradient(135deg, #fef2f2, #fee2e2);
      border: 2px solid #ef4444;
      border-radius: 16px;
      padding: 1.5rem;
      margin: 1.5rem 0;
      display: flex;
      align-items: center;
      gap: 1.25rem;
      color: #b91c1c;
      font-size: 0.95rem;
      animation: slideInDown 0.6s ease-out;
      box-shadow: 0 8px 25px rgba(239, 68, 68, 0.15);
      position: relative;
      overflow: hidden;
    }

    .registration-disabled-banner::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #ef4444, #f87171, #ef4444);
      background-size: 200% 100%;
      animation: warningPulse 2s ease-in-out infinite;
    }

    @keyframes warningPulse {
      0%, 100% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
    }

    .registration-disabled-banner i {
      color: #ef4444;
      font-size: 1.8rem;
      flex-shrink: 0;
      padding: 0.75rem;
      background: rgba(239, 68, 68, 0.1);
      border-radius: 12px;
      animation: iconBounce 2s ease-in-out infinite;
    }

    @keyframes iconBounce {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.1); }
    }

    @keyframes slideInDown {
      from {
        opacity: 0;
        transform: translateY(-30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Info Banner */
    .info-banner {
      background: linear-gradient(135deg, #e3f2fd, #bbdefb);
      border: 1px solid #2196f3;
      border-radius: var(--border-radius);
      padding: 1rem;
      margin: 1rem 0;
      display: flex;
      align-items: center;
      gap: 0.75rem;
      color: #1565c0;
      font-size: 0.875rem;
    }

    .info-banner i {
      color: #2196f3;
      font-size: 1.25rem;
    }

    /* Course Code and Section Badges */
    .course-code-badge {
      display: inline-block;
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
      color: white;
      padding: 0.25rem 0.75rem;
      border-radius: var(--border-radius);
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .section-badge {
      display: inline-block;
      background: var(--info-color);
      color: white;
      padding: 0.25rem 0.5rem;
      border-radius: var(--border-radius);
      font-size: 0.75rem;
      font-weight: 500;
    }

    /* Enhanced Register Button */
    .register-btn {
      background: linear-gradient(135deg, var(--success-color), #27ae60);
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }

    .register-btn:hover:not(:disabled) {
      background: linear-gradient(135deg, #27ae60, var(--success-color));
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(39, 174, 96, 0.3);
    }

    .register-btn:disabled {
      background: var(--text-secondary);
      cursor: not-allowed;
      opacity: 0.6;
    }

    /* Enhanced Unregister Button */
    .unregister-btn {
      background: linear-gradient(135deg, #ef4444, #dc2626);
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }

    .unregister-btn:hover:not(:disabled) {
      background: linear-gradient(135deg, #dc2626, #b91c1c);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
    }

    .unregister-btn:disabled {
      background: var(--text-secondary);
      cursor: not-allowed;
      opacity: 0.6;
      transform: none;
      box-shadow: none;
    }

    /* Enhanced Unregister Button */
    .unregister-btn {
      background: linear-gradient(135deg, var(--danger-color), #c0392b);
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }

    .unregister-btn:hover {
      background: linear-gradient(135deg, #c0392b, var(--danger-color));
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3);
    }

    /* Responsive table improvements */
    @media (max-width: 768px) {
      .course-code-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.5rem;
      }

      .section-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
      }
    }
  </style>

  <script>
    document.getElementById('courseSearch').addEventListener('keyup', function() {
      const query = this.value.toLowerCase();
      const rows = document.querySelectorAll('#coursesTable tbody tr');
      rows.forEach(row => {
        const codeText     = row.querySelector('td[data-label="Course Code"]')?.textContent.toLowerCase() || '';
        const nameText     = row.querySelector('td[data-label="Course Name"]')?.textContent.toLowerCase() || '';
        const sectionText  = row.querySelector('td[data-label="Section"]')?.textContent.toLowerCase() || '';
        const lecturerText = row.querySelector('td[data-label="Lecturer"]')?.textContent.toLowerCase() || '';

        if (
          codeText.includes(query) ||
          nameText.includes(query) ||
          sectionText.includes(query) ||
          lecturerText.includes(query)
        ) {
          row.style.display = '';
        } else {
          row.style.display = 'none';
        }
      });
    });
  </script>
</body>
</html>

