<?php
// ─────────────────────────────────────────────────────────────────────────────
//  modules/profile_lecturer.php
// ─────────────────────────────────────────────────────────────────────────────

session_start();
require '../config/config.php';

// Only lecturers may access
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'lecturer') {
    header("Location: ../index.php");
    exit();
}

$lect_id     = $_SESSION['user_id'];
$errors      = [];
$success_msg = '';

// ── HANDLE PROFILE UPDATE ─────────────────────────────────────────────────────
if (isset($_POST['update_profile'])) {
    // 1) photo upload
    if (!empty($_FILES['profile_pic']['name']) && $_FILES['profile_pic']['error']===UPLOAD_ERR_OK) {
        $ext     = strtolower(pathinfo($_FILES['profile_pic']['name'], PATHINFO_EXTENSION));
        $allowed = ['jpg','jpeg','png','gif'];
        if (in_array($ext, $allowed)) {
            $newName = "lect_{$lect_id}_" . time() . ".{$ext}";
            $dest    = __DIR__ . "/../assets/images/{$newName}";
            if (move_uploaded_file($_FILES['profile_pic']['tmp_name'], $dest)) {
                $stmt = $conn->prepare("UPDATE lecturer SET profile_pic = ? WHERE LectID = ?");
                $stmt->bind_param("si", $newName, $lect_id);
                $stmt->execute();
                $stmt->close();
            } else {
                $errors['profile_pic'] = "Failed to upload image.";
            }
        } else {
            $errors['profile_pic'] = "Only JPG/PNG/GIF allowed.";
        }
    }

    // 2) other fields
    $name      = trim($_POST['name']);
    $email     = trim($_POST['email']);
    $faculty   = trim($_POST['faculty']);
    $phone     = trim($_POST['phone']);
    $id_type   = $_POST['id_type'];
    $id_number = trim($_POST['id_number']);
    $gender    = $_POST['gender'];

    $stmt = $conn->prepare("
      UPDATE lecturer SET 
        Name       = ?, 
        Email      = ?, 
        Faculty    = ?, 
        PhoneNumber= ?, 
        ID_Type    = ?, 
        ID_Number  = ?, 
        Gender     = ?
      WHERE LectID = ?");
    $stmt->bind_param(
      "sssssssi",
      $name, $email, $faculty,
      $phone, $id_type, $id_number,
      $gender, $lect_id
    );
    $stmt->execute();
    $stmt->close();

    if (empty($errors)) {
      $success_msg = "Profile updated successfully.";
    }
}

// ── HANDLE PASSWORD CHANGE ─────────────────────────────────────────────────────
if (isset($_POST['change_password'])) {
    // fetch existing hash
    $stmt = $conn->prepare("SELECT Password FROM lecturer WHERE LectID = ?");
    $stmt->bind_param("i", $lect_id);
    $stmt->execute();
    $stmt->bind_result($hash_in_db);
    $stmt->fetch();
    $stmt->close();

    $current = $_POST['current_password'];
    $new     = $_POST['new_password'];
    $confirm = $_POST['confirm_password'];

    if (!password_verify($current, $hash_in_db)) {
        $errors['password'] = "Current password incorrect.";
    }
    elseif ($new !== $confirm) {
        $errors['password'] = "New password and confirmation must match.";
    }
    elseif (!preg_match('/^(?=.*[A-Z])(?=.*\d)(?=.*\W).{8,}$/', $new)) {
        $errors['password'] = "Must be ≥8 chars, include uppercase, number & symbol.";
    }
    else {
        $new_hash = password_hash($new, PASSWORD_DEFAULT);
        $stmt = $conn->prepare("UPDATE lecturer SET Password = ? WHERE LectID = ?");
        $stmt->bind_param("si", $new_hash, $lect_id);
        $stmt->execute();
        $stmt->close();
        $success_msg = "Password changed successfully.";
    }
}

// ── FETCH LECTURER DATA ────────────────────────────────────────────────────────
$stmt = $conn->prepare("
  SELECT Name, UserID, Email, Faculty, PhoneNumber,
         ID_Type, ID_Number, Gender, profile_pic
    FROM lecturer
   WHERE LectID = ?");
$stmt->bind_param("i", $lect_id);
$stmt->execute();
$lecturer = $stmt->get_result()->fetch_assoc();
$stmt->close();

// fix broken pic URL: look in assets/images/
$photo_url = !empty($lecturer['profile_pic'])
    ? "../assets/images/".rawurlencode($lecturer['profile_pic'])
    : "../assets/images/user1.png";

// ── FETCH COURSES THIS SEMESTER ───────────────────────────────────────────────
$stmt = $conn->prepare("
  SELECT CourseID, Course_Name
    FROM course
   WHERE LectID = ?");
$stmt->bind_param("i", $lect_id);
$stmt->execute();
$courses = $stmt->get_result();
$stmt->close();
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width,initial-scale=1.0"/>
  <title>Lecturer Profile • UTHM Attendance</title>

  <!-- Google Font & FontAwesome -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>

  <!-- COMMON LAYOUT & THEME -->
  <link rel="stylesheet" href="../dashboard/css/base-styles.css"/>
  <link rel="stylesheet" href="../dashboard/css/lecturer-header.css"/>
  <link rel="stylesheet" href="../dashboard/css/lecturer-sidebar.css"/>
  <link rel="stylesheet" href="../dashboard/css/lecturer-footer.css"/>

  <!-- SHARED CARD & PROFILE STYLES -->
  <link rel="stylesheet" href="../dashboard/css/student-dashboard-enhanced.css"/>
  <link rel="stylesheet" href="../dashboard/css/profile-student-styles.css"/>

  <style>
    /* Tweak card headers a bit */
    .edit-card .card-title i,
    .password-card .card-title i {
      color: var(--primary);
    }
  </style>
</head>
<body>
  <!-- HEADER -->
  <div class="header">
    <div class="header-left">
      <img src="../assets/images/logo-uthm2.png" class="logo" alt="UTHM"/>
    </div>
    <div class="header-right">
      <a href="../modules/qr_generate.php" class="qr-button"><i class="fas fa-qrcode"></i> Generate QR</a>
      <span class="user-id"><?=htmlspecialchars($lecturer['UserID'])?></span>
    </div>
  </div>

  <div class="container">
    <!-- SIDEBAR -->
    <div class="sidebar">
      <div class="profile">
        <img src="<?=$photo_url?>" class="profile-pic" alt="Profile Photo"/>
        <p class="profile-name"><?=htmlspecialchars($lecturer['Name'])?></p>
        <p class="profile-id"><?=htmlspecialchars($lecturer['UserID'])?></p>
      </div>
      <ul class="menu">
        <li><a href="../dashboard/lecturer.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
        <li><a href="../modules/profile_lecturer.php" class="active"><i class="fas fa-user"></i> Profile</a></li>
        <li><a href="qr_generate.php"><i class="fas fa-qrcode"></i> Generate QR</a></li>
         <li><a href="../dashboard/attendance_report.php"><i class="fas fa-book"></i> Attendance Report</a></li>
        <li><a href="../dashboard/blockchain_records.php"><i class="fas fa-link"></i> Blockchain Report</a></li>
        <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
      </ul>
    </div>

    <!-- MAIN CONTENT -->
    <main class="main-content">

      <!-- PAGE HEADER -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-text">
            <h1><i class="fas fa-user"></i> My Profile</h1>
            <p>View and update your personal details below.</p>
          </div>
          <div class="header-date">
            <i class="fas fa-calendar-alt"></i>
            <span><?=date('l, F j, Y')?></span>
          </div>
        </div>
      </div>

      <!-- SUCCESS / ERROR POPUP -->
      <?php 
  // decide if we have an error or a success
  $hasError  = isset($errors['profile_pic']) || isset($errors['password']);
  $hasMsg    = $success_msg || $hasError;
  if ($hasMsg): 
    // pick the right message
    $text = $success_msg 
          ?: ($errors['profile_pic'] ?? $errors['password']);
?>
  <div class="popup-message"
       style="
         background: <?= $hasError ? '#fee2e2' : '#d1fae5'?>;
         color:      <?= $hasError ? '#b91c1c' : '#064e3b'?>;
       ">
    <?= htmlspecialchars($text) ?>
    <button class="close-btn" onclick="closePopup()">&times;</button>
  </div>
<?php endif; ?>


      <div class="card-row">

        <!-- DETAILS CARD -->
        <div class="card-col">
          <div class="detail-card">
            <div class="text-center">
              <img src="<?=$photo_url?>" class="profile-photo" alt="Profile Photo"/>
              <h3><?=htmlspecialchars($lecturer['Name'])?></h3>
              <p>User ID: <?=htmlspecialchars($lecturer['UserID'])?></p>
            </div>
            <hr>
            <?php foreach([
              'Email'     => $lecturer['Email'],
              'Faculty'   => $lecturer['Faculty'],
              'Phone'     => $lecturer['PhoneNumber'],
              'ID Type'   => $lecturer['ID_Type'],
              'ID Number' => $lecturer['ID_Number'],
              'Gender'    => $lecturer['Gender'],
            ] as $label=>$value): ?>
              <div class="detail-row">
                <div class="detail-label"><?=$label?></div>
                <div class="detail-value"><?=htmlspecialchars($value)?></div>
              </div>
            <?php endforeach; ?>
          </div>
        </div>

        <!-- UPDATE & PASSWORD -->
        <div class="card-col">

          <!-- UPDATE PROFILE -->
          <div class="edit-card">
            <h2 class="card-title"><i class="fas fa-edit"></i> Update Profile</h2>
            <form method="POST" enctype="multipart/form-data">
              <div class="form-row">
                <label for="profile_pic">Profile Photo</label>
                <input type="file" name="profile_pic" id="profile_pic" accept=".jpg,.jpeg,.png,.gif"/>
              </div>
              <div class="form-row">
                <label for="name">Name</label>
                <input type="text" name="name" id="name" value="<?=htmlspecialchars($lecturer['Name'])?>"/>
              </div>
              <div class="form-row">
                <label for="email">Email</label>
                <input type="email" name="email" id="email" value="<?=htmlspecialchars($lecturer['Email'])?>"/>
              </div>
              <div class="form-row">
                <label for="faculty">Faculty</label>
                <input type="text" name="faculty" id="faculty" value="<?=htmlspecialchars($lecturer['Faculty'])?>"/>
              </div>
              <div class="form-row">
                <label for="phone">Phone Number</label>
                <input type="text" name="phone" id="phone" value="<?=htmlspecialchars($lecturer['PhoneNumber'])?>"/>
              </div>
              <div class="form-row">
                <label for="id_type">ID Type</label>
                <select name="id_type" id="id_type">
                  <option <?= $lecturer['ID_Type']==='IC'?'selected':''?>>IC</option>
                  <option <?= $lecturer['ID_Type']==='Passport'?'selected':''?>>Passport</option>
                </select>
              </div>
              <div class="form-row">
                <label for="id_number">ID Number</label>
                <input type="text" name="id_number" id="id_number" value="<?=htmlspecialchars($lecturer['ID_Number'])?>"/>
              </div>
              <div class="form-row">
                <label for="gender">Gender</label>
                <select name="gender" id="gender">
                  <option <?= $lecturer['Gender']==='Male'?'selected':''?>>Male</option>
                  <option <?= $lecturer['Gender']==='Female'?'selected':''?>>Female</option>
                </select>
              </div>
              <div class="form-row">
                <button type="submit" name="update_profile" class="btn-submit">
                  <i class="fas fa-save"></i> Save Changes
                </button>
              </div>
            </form>
          </div>

          <!-- CHANGE PASSWORD -->
          <div class="password-card">
            <h2 class="card-title"><i class="fas fa-key"></i> Change Password</h2>
            <form method="POST" id="passwordForm">
              <div class="form-row">
                <label for="current_password">Current Password</label>
                <input type="password" name="current_password" id="current_password" required/>
              </div>
              <div class="form-row">
                <label for="new_password">New Password</label>
                <input type="password" name="new_password" id="new_password"
                       placeholder="≥8 chars, uppercase, number & symbol" required/>
              </div>
              <div class="form-row">
                <label for="confirm_password">Confirm New Password</label>
                <input type="password" name="confirm_password" id="confirm_password" required/>
              </div>
              <div class="form-row">
                <button type="submit" name="change_password" class="btn-submit">
                  <i class="fas fa-check"></i> Update Password
                </button>
              </div>
            </form>
          </div>
        </div>
      </div><!-- /.card-row -->

      <!-- MY COURSES CARD -->
       <!-- <div class="table-card">
        <h2 class="table-title"><i class="fas fa-book"></i> My Courses This Semester</h2>
         <?php if($courses->num_rows>0): ?>
          <div class="table-wrapper">
            <table class="courses-table">
              <thead>
                <tr><th>Course ID</th><th>Course Name</th></tr>
              </thead>
              <tbody>
                <?php while($c=$courses->fetch_assoc()): ?>
                  <tr>
                    <td><?=htmlspecialchars($c['CourseID'])?></td>
                    <td><?=htmlspecialchars($c['Course_Name'])?></td>
                  </tr>
                <?php endwhile; ?>
              </tbody>
            </table>
          </div>
        <?php else: ?>
          <p style="text-align:center;color:var(--gray-400);padding:2rem 0;">
            No courses assigned this semester.
          </p>
        <?php endif; ?>
      </div>
    </main>
  </div> --

  <!-- FOOTER -->
  <footer class="footer">
    <p>UNIVERSITI TUN HUSSEIN ONN MALAYSIA</p>
  </footer>

  <script>
    // auto‐close popups
    function closePopup(){
      const p = document.getElementById('popup-message');
      if(p) p.style.display = 'none';
    }
    document.addEventListener('DOMContentLoaded',()=>{
      const p=document.getElementById('popup-message');
      if(p) setTimeout(closePopup,4000);

      // client‐side password checks
      document.getElementById('passwordForm').addEventListener('submit',e=>{
        document.querySelectorAll('.form-error').forEach(x=>x.remove());
        const np=document.getElementById('new_password').value;
        const cp=document.getElementById('confirm_password').value;
        const rule=/^(?=.*[A-Z])(?=.*\d)(?=.*\W).{8,}$/;
        let ok=true;
        if(!rule.test(np)){
          showErr('new_password','Must be ≥8 chars, uppercase, number & symbol');
          ok=false;
        }
        if(np!==cp){
          showErr('confirm_password','Passwords do not match');
          ok=false;
        }
        if(!ok) e.preventDefault();
      });
      function showErr(id,msg){
        const inp=document.getElementById(id),
              div=document.createElement('div');
        div.className='form-error';
        div.textContent=msg;
        inp.parentNode.appendChild(div);
      }
    });
  </script>
</body>
</html>
