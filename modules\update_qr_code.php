<?php
session_start();
require '../config/config.php';

// Only require QR library if GD is available
if (extension_loaded('gd') && function_exists('imagecreate')) {
    require '../phpqrcode/qrlib.php';
}

// Headers for JSON response
header('Content-Type: application/json');

// Check if user is logged in and is a lecturer
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'lecturer') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Check if there's an active attendance session
if (!isset($_SESSION['active_attendance']) || !isset($_SESSION['qr_token'])) {
    echo json_encode(['success' => false, 'message' => 'No active attendance session']);
    exit;
}

// Generate a new QR code with consistent format
$attendance = $_SESSION['active_attendance'];
$timestamp = time();

// Generate a new token for each refresh (for security)
$token = bin2hex(random_bytes(8));
$_SESSION['qr_token'] = $token;

// Set expiration time (30 seconds from now to match user preference)
$expires_at = $timestamp + 30;

// QR Data includes all necessary information in the same format as main generation
$qr_data = http_build_query([
    'course_id' => $attendance['course_id'],
    'lecturer_id' => $attendance['lecturer_id'],
    'assignment_id' => $attendance['assignment_id'] ?? 0,
    'section' => $attendance['section'] ?? '',
    'token' => $token,
    'expires_at' => $expires_at
]);

// Generate unique filename for each QR code
$file_path = "../assets/images/qr_temp_" . $attendance['course_id'] . "_" . $timestamp . ".png";

// Delete old QR code files if they exist
$old_files = glob("../assets/images/qr_temp_" . $attendance['course_id'] . "_*.png");
foreach ($old_files as $old_file) {
    if ($old_file != $file_path && file_exists($old_file)) {
        unlink($old_file);
    }
}

// Check generation method and generate QR code accordingly
$generation_method = $_SESSION['qr_generation_method'] ?? 'local';

if ($generation_method === 'fallback' || !extension_loaded('gd') || !function_exists('imagecreate')) {
    // Use fallback method - return QR data for online generation
    echo json_encode([
        'success' => true,
        'qr_payload' => $qr_data,
        'generation_method' => 'fallback',
        'expires_in' => 30
    ]);
} else {
    // Use local QR generation
    try {
        QRcode::png($qr_data, $file_path, QR_ECLEVEL_L, 10);

        // Return the file path
        echo json_encode([
            'success' => true,
            'file_path' => $file_path,
            'generation_method' => 'local',
            'expires_in' => 30
        ]);
    } catch (Exception $e) {
        // If local generation fails, fall back to online method
        echo json_encode([
            'success' => true,
            'qr_payload' => $qr_data,
            'generation_method' => 'fallback',
            'expires_in' => 30,
            'fallback_reason' => 'Local generation failed: ' . $e->getMessage()
        ]);
    }
}
?>