/* ===================================================================
   LECTURER FOOTER STYLES
   Professional footer component for lecturer dashboard pages
   ================================================================= */

/* CSS Custom Properties for Footer */
:root {
  --footer-bg: #ffffff;
  --footer-border: #e2e8f0;
  --footer-text: #64748b;
  --footer-text-muted: #94a3b8;
  --footer-padding: 2rem;
  --footer-margin-top: 3rem;
  --transition-smooth: all 0.3s ease;
}

/* Footer Container */
footer {
  background: var(--footer-bg);
  border-top: 1px solid var(--footer-border);
  color: var(--footer-text);
  text-align: center;
  padding: var(--footer-padding);
  font-weight: 400;
  font-size: 0.875rem;
  margin-top: var(--footer-margin-top);
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

/* Footer Text */
footer p {
  margin: 0;
  color: var(--footer-text-muted);
  line-height: 1.5;
}

/* Footer Links (if needed) */
footer a {
  color: var(--footer-text);
  text-decoration: none;
  transition: var(--transition-smooth);
}

footer a:hover {
  color: #6366f1;
  text-decoration: underline;
}

/* Footer Divider (if needed) */
footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--footer-border), transparent);
}

/* Responsive Design */
@media (max-width: 768px) {
  footer {
    padding: 1.5rem 1rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  footer {
    padding: 1rem;
    font-size: 0.75rem;
  }
}
