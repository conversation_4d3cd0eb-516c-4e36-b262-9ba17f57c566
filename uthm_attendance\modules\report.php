<?php
// ─────────────────────────────────────────────────────────────────────────────
//  modules/report.php  (Student Attendance Report)
//  - Displays all attendance records for the logged‐in student.
//  - Uses the same header/sidebar/footer and card/table styles as the Student Dashboard.
//  - Allows filtering by course via a dropdown.
// ─────────────────────────────────────────────────────────────────────────────

session_start();
require '../config/config.php';

// ─────────────────────────────────────────────────────────────────────────────
// 1) SECURITY CHECK: Only 'student' role can access
// ─────────────────────────────────────────────────────────────────────────────
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header("Location: ../index.php");
    exit();
}

// ─────────────────────────────────────────────────────────────────────────────
// 2) FETCH STUDENT INFO (for header/sidebar display)
// ─────────────────────────────────────────────────────────────────────────────
$student_id = $_SESSION['user_id'];
$sStmt = $conn->prepare("SELECT Name, UserID, Photo FROM student WHERE StudentID = ?");
$sStmt->bind_param("i", $student_id);
$sStmt->execute();
$sRes = $sStmt->get_result();
$student = $sRes->fetch_assoc();
$student_name   = $student['Name']   ?? 'Student Name';
$student_userid = $student['UserID'] ?? 'N/A';
$sStmt->close();

// build photo URL (fallback to default avatar)
$photo_url = !empty($student['Photo'])
    ? "../uploads/" . rawurlencode($student['Photo'])
    : "../assets/images/user1.png";

// ─────────────────────────────────────────────────────────────────────────────
// 3) HANDLE COURSE FILTER (optional)
// ─────────────────────────────────────────────────────────────────────────────
$filterClause = "";
$selectedCourseID = "";
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['course_id']) && $_POST['course_id'] !== "") {
    $selectedCourseID = intval($_POST['course_id']);
    $filterClause     = "AND a.CourseID = {$selectedCourseID}";
}

// ─────────────────────────────────────────────────────────────────────────────
// Check database capabilities for new course structure
// ─────────────────────────────────────────────────────────────────────────────
$checkCodeColumn = "SHOW COLUMNS FROM course LIKE 'Course_Code'";
$codeColumnExists = $conn->query($checkCodeColumn)->num_rows > 0;

$checkAssignmentsTable = "SHOW TABLES LIKE 'course_assignments'";
$assignmentsTableExists = $conn->query($checkAssignmentsTable)->num_rows > 0;

$checkSectionColumn = "SHOW COLUMNS FROM course_registration LIKE 'Section'";
$sectionColumnExists = $conn->query($checkSectionColumn)->num_rows > 0;

// ─────────────────────────────────────────────────────────────────────────────
// 4) FETCH ATTENDANCE RECORDS FOR LOGGED‐IN STUDENT
// ─────────────────────────────────────────────────────────────────────────────
if ($codeColumnExists && $assignmentsTableExists && $sectionColumnExists) {
    // New system with course codes, assignments, and sections
    $sql = "
        SELECT
            a.AttendanceID,
            c.Course_Name,
            c.Course_Code,
            cr.Section,
            l.Name AS LecturerName,
            a.Att_Date,
            a.Att_Status,
            a.Remark,
            a.Timestamp
        FROM attendance_report a
        JOIN course c ON a.CourseID = c.CourseID
        LEFT JOIN course_registration cr ON a.StudentID = cr.StudentID AND a.CourseID = cr.CourseID
        LEFT JOIN course_assignments ca ON c.CourseID = ca.CourseID AND cr.Section = ca.Section
        LEFT JOIN lecturer l ON ca.LectID = l.LectID
        WHERE a.StudentID = {$student_id}
          {$filterClause}
        ORDER BY a.Att_Date DESC, c.Course_Name ASC
    ";
} else if ($codeColumnExists) {
    // Legacy system with course codes but no sections
    $sql = "
        SELECT
            a.AttendanceID,
            c.Course_Name,
            c.Course_Code,
            '' as Section,
            l.Name AS LecturerName,
            a.Att_Date,
            a.Att_Status,
            a.Remark,
            a.Timestamp
        FROM attendance_report a
        JOIN course c ON a.CourseID = c.CourseID
        LEFT JOIN lecturer l ON c.LectID = l.LectID
        WHERE a.StudentID = {$student_id}
          {$filterClause}
        ORDER BY a.Att_Date DESC, c.Course_Name ASC
    ";
} else {
    // Original system
    $sql = "
        SELECT
            a.AttendanceID,
            c.Course_Name,
            c.CourseID as Course_Code,
            '' as Section,
            l.Name AS LecturerName,
            a.Att_Date,
            a.Att_Status,
            a.Remark,
            a.Timestamp
        FROM attendance_report a
        JOIN course c ON a.CourseID = c.CourseID
        LEFT JOIN lecturer l ON c.LectID = l.LectID
        WHERE a.StudentID = {$student_id}
          {$filterClause}
        ORDER BY a.Att_Date DESC, c.Course_Name ASC
    ";
}
$result = $conn->query($sql);

// ─────────────────────────────────────────────────────────────────────────────
// 5) CALCULATE SUMMARY STATISTICS
// ─────────────────────────────────────────────────────────────────────────────
$totalRecords = $result->num_rows;
$presentCount = 0;
$absentCount = 0;

// Count present/absent for summary
if ($totalRecords > 0) {
    $tempResult = $conn->query($sql);
    while ($row = $tempResult->fetch_assoc()) {
        if ($row['Att_Status'] === 'Present') {
            $presentCount++;
        } else {
            $absentCount++;
        }
    }
}

$attendancePercentage = $totalRecords > 0 ? round(($presentCount / $totalRecords) * 100, 2) : 0;

// ─────────────────────────────────────────────────────────────────────────────
// 6) FETCH DISTINCT COURSES THAT STUDENT HAS ANY ATTENDANCE FOR (to populate filter dropdown)
// ─────────────────────────────────────────────────────────────────────────────
if ($codeColumnExists) {
    $courses = $conn->query("
        SELECT DISTINCT c.CourseID, c.Course_Name, c.Course_Code
        FROM course c
        JOIN attendance_report a ON c.CourseID = a.CourseID
        WHERE a.StudentID = {$student_id}
        ORDER BY c.Course_Code ASC, c.Course_Name ASC
    ");
} else {
    $courses = $conn->query("
        SELECT DISTINCT c.CourseID, c.Course_Name, c.CourseID as Course_Code
        FROM course c
        JOIN attendance_report a ON c.CourseID = a.CourseID
        WHERE a.StudentID = {$student_id}
        ORDER BY c.Course_Name ASC
    ");
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>My Attendance Report</title>

  <!-- FontAwesome for icons -->
  <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Google Font (Inter) -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap"
        rel="stylesheet">

  <!-- Base + Modular CSS -->
  <link rel="stylesheet" href="../dashboard/css/base-styles.css">
  <link rel="stylesheet" href="../dashboard/css/lecturer-header.css">
  <link rel="stylesheet" href="../dashboard/css/lecturer-sidebar.css">
  <link rel="stylesheet" href="../dashboard/css/lecturer-footer.css">

  <!-- Student Dashboard Enhanced CSS (cards, tables, etc.) -->
  <link rel="stylesheet" href="../dashboard/css/student-dashboard-enhanced.css">

  <style>
    /* ────────────────────────────────────────────────────────────────────
       Enhanced Page Styles - Consistent with Azia Template Design
       ──────────────────────────────────────────────────────────────────── */

    /* Enhanced Page Header - Matching Student Dashboard */
    .page-header {
      background: var(--card-bg);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-lg);
      margin-bottom: var(--spacing-lg);
      border: 1px solid var(--border-color);
      box-shadow: var(--shadow-sm);
      position: relative;
      overflow: hidden;
    }

    .page-header::before {
      content: '';
      position: absolute;
      top: 0; left: 0; right: 0;
      height: 4px;
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    }

    .page-header h1 {
      font-size: 1.75rem;
      font-weight: 700;
      color: var(--text-primary);
      margin: 0 0 var(--spacing-xs) 0;
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .page-header h1 i {
      color: var(--primary-color);
      -webkit-text-fill-color: var(--primary-color);
    }

    .page-header p {
      font-size: 0.875rem;
      color: var(--text-secondary);
      margin: 0;
      font-weight: 400;
    }

    /* Enhanced Filter Section - Professional Azia Style */
    .filter-section {
      background: var(--card-bg);
      padding: var(--spacing-lg);
      border-radius: var(--border-radius-lg);
      margin-bottom: var(--spacing-lg);
      border: 1px solid var(--border-color);
      box-shadow: var(--shadow-sm);
    }

    .filter-section form {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      flex-wrap: wrap;
    }

    .filter-section label {
      font-weight: 500;
      color: var(--text-primary);
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      font-size: 0.875rem;
    }

    .filter-section label::before {
      content: '\f0b0';
      font-family: 'Font Awesome 6 Free';
      font-weight: 900;
      color: var(--primary-color);
      font-size: 1rem;
    }

    .filter-section select {
      padding: var(--spacing-sm) var(--spacing-md);
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius);
      background: var(--card-bg);
      color: var(--text-primary);
      font-size: 0.875rem;
      min-width: 220px;
      transition: all 0.3s ease;
      font-weight: 500;
    }

    .filter-section select:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }

    .filter-section button {
      background: var(--primary-color);
      color: var(--card-bg);
      border: none;
      padding: var(--spacing-sm) var(--spacing-lg);
      border-radius: var(--border-radius);
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      font-size: 0.875rem;
    }

    .filter-section button::before {
      content: '\f0b0';
      font-family: 'Font Awesome 6 Free';
      font-weight: 900;
    }

    .filter-section button:hover {
      background: var(--primary-dark);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }

    /* Enhanced Table Styling - Professional Azia Design */
    .table-card {
      background: var(--card-bg);
      border-radius: var(--border-radius-lg);
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--border-color);
      overflow: hidden;
      margin-bottom: var(--spacing-lg);
    }

    .table-wrapper {
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
    }

    .attendance-table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
      margin: 0;
      background: var(--card-bg);
      font-size: 0.875rem;
    }

    .attendance-table th {
      background: #f8fafc;
      color: #64748b;
      padding: 1rem 0.75rem;
      text-align: left;
      font-weight: 600;
      font-size: 0.75rem;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      border-bottom: 2px solid var(--border-color);
      position: sticky;
      top: 0;
      z-index: 10;
      white-space: nowrap;
    }

    .attendance-table th:first-child {
      padding-left: 1.5rem;
    }

    .attendance-table th:last-child {
      padding-right: 1.5rem;
    }

    .attendance-table td {
      padding: 1.25rem 0.75rem;
      text-align: left;
      border-bottom: 1px solid #f1f5f9;
      vertical-align: middle;
      color: var(--text-primary);
      background: var(--card-bg);
      transition: all 0.2s ease;
    }

    .attendance-table td:first-child {
      padding-left: 1.5rem;
    }

    .attendance-table td:last-child {
      padding-right: 1.5rem;
    }

    .attendance-table tbody tr:nth-child(even) {
      background: #fafbfc;
    }

    .attendance-table tbody tr:hover {
      background: #f1f5f9;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    /* Course Code and Section Badges */
    .course-code-badge {
      display: inline-block;
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
      color: white;
      padding: 0.25rem 0.75rem;
      border-radius: var(--border-radius);
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .section-badge {
      display: inline-block;
      background: var(--info-color);
      color: white;
      padding: 0.25rem 0.5rem;
      border-radius: var(--border-radius);
      font-size: 0.75rem;
      font-weight: 500;
    }

    /* Enhanced Status Badges - Consistent with Dashboard */
    .status-badge {
      display: inline-flex;
      align-items: center;
      gap: 0.375rem;
      padding: 0.375rem 0.75rem;
      border-radius: 0.375rem;
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: capitalize;
      border: 1px solid transparent;
      transition: all 0.2s ease;
    }

    .status-badge.excellent {
      background: #f0fdf4;
      color: #166534;
      border-color: #bbf7d0;
    }

    .status-badge.excellent::before {
      content: '\f00c';
      font-family: 'Font Awesome 6 Free';
      font-weight: 900;
      color: #10b981;
    }

    .status-badge.warning {
      background: #fef2f2;
      color: #991b1b;
      border-color: #fecaca;
    }

    .status-badge.warning::before {
      content: '\f071';
      font-family: 'Font Awesome 6 Free';
      font-weight: 900;
      color: #ef4444;
    }

    /* Enhanced Empty State */
    .empty-state {
      text-align: center;
      padding: 3rem 2rem;
      color: var(--text-secondary);
      background: var(--card-bg);
      border-radius: var(--border-radius-lg);
    }

    .empty-state i {
      font-size: 4rem;
      color: #e2e8f0;
      margin-bottom: 1.5rem;
      opacity: 0.7;
    }

    .empty-state h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0 0 0.75rem 0;
    }

    .empty-state p {
      font-size: 0.875rem;
      color: var(--text-secondary);
      margin: 0 0 2rem 0;
      max-width: 400px;
      margin-left: auto;
      margin-right: auto;
    }

    .empty-state .cta-button {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
      color: var(--card-bg);
      text-decoration: none;
      padding: 0.75rem 1.5rem;
      border-radius: 0.5rem;
      font-weight: 600;
      font-size: 0.875rem;
      transition: all 0.3s ease;
      box-shadow: var(--shadow-sm);
    }

    .empty-state .cta-button:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
      background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    }

    /* Summary Statistics Section */
    .stats-summary {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-lg);
    }

    .stat-card {
      background: var(--card-bg);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-lg);
      border: 1px solid var(--border-color);
      box-shadow: var(--shadow-sm);
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      transition: all 0.3s ease;
    }

    .stat-card:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }

    .stat-icon {
      width: 3rem;
      height: 3rem;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
      color: var(--card-bg);
      font-size: 1.25rem;
    }

    .stat-icon.present {
      background: linear-gradient(135deg, var(--success-color) 0%, #34d399 100%);
    }

    .stat-icon.absent {
      background: linear-gradient(135deg, var(--danger-color) 0%, #f87171 100%);
    }

    .stat-icon.percentage {
      background: linear-gradient(135deg, var(--info-color) 0%, #38bdf8 100%);
    }

    .stat-content {
      flex: 1;
    }

    .stat-number {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--text-primary);
      line-height: 1;
      margin-bottom: 0.25rem;
    }

    .stat-label {
      font-size: 0.875rem;
      color: var(--text-secondary);
      font-weight: 500;
    }

    /* Enhanced Responsive Design */
    @media (max-width: 1024px) {
      .main-content {
        padding: var(--spacing-md);
      }

      .page-header {
        padding: var(--spacing-md);
      }

      .filter-section {
        padding: var(--spacing-md);
      }
    }

    @media (max-width: 768px) {
      .main-content {
        padding: var(--spacing-sm);
      }

      .page-header {
        padding: var(--spacing-sm);
      }

      .page-header h1 {
        font-size: 1.5rem;
      }

      .filter-section {
        padding: var(--spacing-sm);
      }

      .filter-section form {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
      }

      .filter-section select {
        min-width: auto;
      }

      .stats-summary {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: var(--spacing-sm);
      }

      .stat-card {
        padding: var(--spacing-md);
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
      }

      .stat-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
      }

      .stat-number {
        font-size: 1.25rem;
      }

      .stat-label {
        font-size: 0.75rem;
      }

      .attendance-table {
        font-size: 0.75rem;
      }

      .attendance-table th,
      .attendance-table td {
        padding: 0.75rem 0.375rem;
      }

      .attendance-table th:first-child,
      .attendance-table td:first-child {
        padding-left: 1rem;
      }

      .attendance-table th:last-child,
      .attendance-table td:last-child {
        padding-right: 1rem;
      }

      .status-badge {
        font-size: 0.625rem;
        padding: 0.25rem 0.5rem;
      }

      .empty-state {
        padding: 2rem 1rem;
      }

      .empty-state i {
        font-size: 3rem;
      }
    }

    @media (max-width: 480px) {
      .page-header h1 {
        font-size: 1.25rem;
      }

      .stats-summary {
        grid-template-columns: 1fr;
      }

      .stat-card {
        padding: var(--spacing-sm);
      }

      .stat-icon {
        width: 2rem;
        height: 2rem;
        font-size: 0.875rem;
      }

      .stat-number {
        font-size: 1.125rem;
      }

      .attendance-table th,
      .attendance-table td {
        padding: 0.5rem 0.25rem;
      }
    }
  </style>
</head>
<body>
  <!-- ─────────── HEADER ─────────── -->
  <div class="header">
    <div class="header-left">
      <img src="../assets/images/logo-uthm2.png" alt="UTHM Logo" class="logo">
    </div>
    <div class="header-right">
      <a href="../modules/qr_scan.php" class="qr-button">
        <i class="fas fa-qrcode"></i> Scan QR
      </a>
      <span class="user-id"><?= htmlspecialchars($student_userid) ?></span>
    </div>
  </div>

  <!-- ─────────── CONTAINER (SIDEBAR + MAIN) ─────────── -->
  <div class="container">
    <!-- ─────────── SIDEBAR (identical to Student Dashboard) ─────────── -->
    <div class="sidebar">
      <div class="profile">
        <!-- DYNAMIC PROFILE PHOTO -->
        <img src="<?= $photo_url ?>" alt="Profile Photo" class="profile-pic">
        <p class="profile-name"><?= htmlspecialchars($student_name) ?></p>
        <p class="profile-id"><?= htmlspecialchars($student_userid) ?></p>
      </div>
      <ul class="menu">
        <li><a href="../dashboard/student.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
        <li><a href="../modules/profile_student.php"><i class="fas fa-user"></i> Profile</a></li>
        <li><a href="../modules/register_courses.php"><i class="fas fa-edit"></i> Register Courses</a></li>
        <li><a href="report.php" class="active"><i class="fas fa-book"></i> Attendance Details</a></li>
        <li><a href="../dashboard/student_blockchain_records.php"><i class="fas fa-link"></i> Blockchain Records</a></li>
        <li><a href="../dashboard/student_transaction_lookup.php"><i class="fas fa-search"></i> Transaction Lookup</a></li>
        <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
      </ul>
    </div>

    <!-- ─────────── MAIN CONTENT ─────────── -->
    <div class="main-content">
      <!-- ─────────── PAGE HEADER ─────────── -->
      <div class="page-header">
        <h1><i class="fas fa-book"></i> My Attendance</h1>
        <p>Review your attendance records below. You can filter by course to see specific sessions.</p>
      </div>

      <!-- ─────────── SUMMARY STATISTICS ─────────── -->
      <?php if ($totalRecords > 0): ?>
        <div class="stats-summary">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-list-alt"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number"><?= $totalRecords ?></div>
              <div class="stat-label">Total Records</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon present">
              <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number"><?= $presentCount ?></div>
              <div class="stat-label">Present</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon absent">
              <i class="fas fa-times-circle"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number"><?= $absentCount ?></div>
              <div class="stat-label">Absent</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon percentage">
              <i class="fas fa-percentage"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number"><?= $attendancePercentage ?>%</div>
              <div class="stat-label">Attendance Rate</div>
            </div>
          </div>
        </div>
      <?php endif; ?>

      <!-- ─────────── ENHANCED FILTER SECTION ─────────── -->
      <div class="filter-section">
        <form method="POST" action="">
          <label for="course">Filter by Course:</label>
          <select name="course_id" id="course">
            <option value="">All Courses</option>
            <?php while ($course = $courses->fetch_assoc()): ?>
              <option value="<?= $course['CourseID'] ?>"
                <?= ($selectedCourseID == $course['CourseID']) ? 'selected' : '' ?>>
                <?php if ($codeColumnExists && $course['Course_Code'] !== $course['CourseID']): ?>
                  <?= htmlspecialchars($course['Course_Code']) ?> - <?= htmlspecialchars($course['Course_Name']) ?>
                <?php else: ?>
                  <?= htmlspecialchars($course['Course_Name']) ?>
                <?php endif; ?>
              </option>
            <?php endwhile; ?>
          </select>
          <button type="submit">Apply Filter</button>
        </form>
      </div>

      <!-- ─────────── ATTENDANCE TABLE CARD ─────────── -->
      <div class="table-card">
        <?php if ($result->num_rows > 0): ?>
          <div class="table-wrapper">
            <table class="attendance-table">
              <thead>
                <tr>
                  <th>Course</th>
                  <?php if ($sectionColumnExists): ?>
                  <th>Section</th>
                  <?php endif; ?>
                  <?php if ($assignmentsTableExists): ?>
                  <th>Lecturer</th>
                  <?php endif; ?>
                  <th>Date</th>
                  <th>Status</th>
                  <th>Remark</th>
                  <th>Timestamp</th>
                </tr>
              </thead>
              <tbody>
                <?php while ($row = $result->fetch_assoc()): ?>
                  <tr>
                    <td data-label="Course">
                      <div style="display: flex; flex-direction: column; gap: 0.25rem;">
                        <?php if ($codeColumnExists && $row['Course_Code'] !== $row['Course_Name']): ?>
                          <span class="course-code-badge" style="align-self: flex-start;">
                            <?= htmlspecialchars($row['Course_Code']) ?>
                          </span>
                        <?php endif; ?>
                        <strong><?= htmlspecialchars($row['Course_Name']) ?></strong>
                      </div>
                    </td>
                    <?php if ($sectionColumnExists): ?>
                    <td data-label="Section">
                      <?php if (!empty($row['Section'])): ?>
                        <span class="section-badge">
                          <?= htmlspecialchars($row['Section']) ?>
                        </span>
                      <?php else: ?>
                        <span style="color: var(--text-secondary); font-style: italic;">–</span>
                      <?php endif; ?>
                    </td>
                    <?php endif; ?>
                    <?php if ($assignmentsTableExists): ?>
                    <td data-label="Lecturer">
                      <?php if (!empty($row['LecturerName'])): ?>
                        <i class="fas fa-chalkboard-teacher" style="color: var(--info); margin-right: var(--space-xs);"></i>
                        <?= htmlspecialchars($row['LecturerName']) ?>
                      <?php else: ?>
                        <span style="color: var(--text-secondary); font-style: italic;">Not Assigned</span>
                      <?php endif; ?>
                    </td>
                    <?php endif; ?>
                    <td data-label="Date">
                      <i class="fas fa-calendar-alt" style="color: var(--info); margin-right: var(--space-xs);"></i>
                      <?= htmlspecialchars(date('Y-m-d', strtotime($row['Att_Date']))) ?>
                    </td>
                    <td data-label="Status">
                      <?php if ($row['Att_Status'] === 'Present'): ?>
                        <span class="status-badge excellent">Present</span>
                      <?php else: ?>
                        <span class="status-badge warning">Absent</span>
                      <?php endif; ?>
                    </td>
                    <td data-label="Remark">
                      <span style="color: var(--gray-400); font-style: italic;">–</span>
                    </td>
                    <td data-label="Timestamp">
                      <i class="fas fa-clock" style="color: var(--gray-500); margin-right: var(--space-xs);"></i>
                      <?= htmlspecialchars(date('Y-m-d H:i', strtotime($row['Timestamp']))) ?>
                    </td>
                  </tr>
                <?php endwhile; ?>
              </tbody>
            </table>
          </div>
        <?php else: ?>
          <div class="empty-state">
            <i class="fas fa-info-circle"></i>
            <h3>No Attendance Records</h3>
            <p>You have no attendance records yet. Attend a class to see your details appear here.</p>
            <a href="../dashboard/student.php" class="cta-button">
              <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
          </div>
        <?php endif; ?>
      </div>
    </div> <!-- end .main-content -->
  </div>   <!-- end .container -->

  <!-- ─────────── FOOTER ─────────── -->
  <footer>
    <p>UNIVERSITI TUN HUSSEIN ONN MALAYSIA</p>
  </footer>
</body>
</html>
