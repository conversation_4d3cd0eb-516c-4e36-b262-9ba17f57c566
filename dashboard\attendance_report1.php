<?php
// ─────────────────────────────────────────────────────────────────────────────
//  dashboard/attendance_report.php
//  - Upload “Absent Letter” files to C:\xampp\htdocs\uthm_attendance\uploads
//  - Generate + email PDF “Warning Letter” via PHPMailer
//  - Filter by course, update status/remark, delete records
// ─────────────────────────────────────────────────────────────────────────────

session_start();
require '../config/config.php';             // your DB connection
require_once __DIR__ . '/../vendor/autoload.php';     // PHPMailer + any other Composer packages
require_once __DIR__ . '/../fpdf186/fpdf.php';       // FPDF library
require_once __DIR__ . '/../includes/EmailService.php'; // Enhanced Email Service

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// ─────────────────────────────────────────────────────────────────────────────
// 1) SECURITY CHECK: only 'lecturer' role can access this page
// ─────────────────────────────────────────────────────────────────────────────
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'lecturer') {
    header("Location: ../index.php");
    exit();
}

// ─────────────────────────────────────────────────────────────────────────────
// 2) FETCH LECTURER INFO (for header/sidebar display)
// ─────────────────────────────────────────────────────────────────────────────
// ─────────────────────────────────────────────────────────────────────────────
// DATABASE CAPABILITIES DETECTION
// ─────────────────────────────────────────────────────────────────────────────
function checkDatabaseCapabilities($conn) {
    $capabilities = [
        'course_code' => false,
        'course_status' => false,
        'course_assignments' => false,
        'registration_section' => false
    ];

    // Check if Course_Code column exists
    $checkCodeColumn = "SHOW COLUMNS FROM course LIKE 'Course_Code'";
    $result = $conn->query($checkCodeColumn);
    $capabilities['course_code'] = $result && $result->num_rows > 0;

    // Check if Status column exists
    $checkStatusColumn = "SHOW COLUMNS FROM course LIKE 'Status'";
    $result = $conn->query($checkStatusColumn);
    $capabilities['course_status'] = $result && $result->num_rows > 0;

    // Check if course_assignments table exists
    $checkAssignmentsTable = "SHOW TABLES LIKE 'course_assignments'";
    $result = $conn->query($checkAssignmentsTable);
    $capabilities['course_assignments'] = $result && $result->num_rows > 0;

    // Check if Section column exists in course_registration
    $checkSectionColumn = "SHOW COLUMNS FROM course_registration LIKE 'Section'";
    $result = $conn->query($checkSectionColumn);
    $capabilities['registration_section'] = $result && $result->num_rows > 0;

    return $capabilities;
}

$dbCapabilities = checkDatabaseCapabilities($conn);

$lecturer_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT Name, UserID, profile_pic FROM lecturer WHERE LectID = ?");
$stmt->bind_param("s", $lecturer_id);
$stmt->execute();
$res = $stmt->get_result();
$lecturer = $res->fetch_assoc();
$lecturer_name   = $lecturer['Name']   ?? 'Lecturer Name';
$lecturer_userid = $lecturer['UserID'] ?? 'N/A';
$stmt->close();

// Profile picture URL
$photo_url = !empty($lecturer['profile_pic'])
    ? "../assets/images/" . rawurlencode($lecturer['profile_pic'])
    : "../assets/images/user1.png";

// ─────────────────────────────────────────────────────────────────────────────
// 3) ENSURE GLOBAL “uploads/” DIRECTORY EXISTS
//    Path: C:\xampp\htdocs\uthm_attendance\uploads
// ─────────────────────────────────────────────────────────────────────────────
$globalUploadDir = __DIR__ . '/../uploads';
if (!is_dir($globalUploadDir)) {
    mkdir($globalUploadDir, 0775, true);
}

// ─────────────────────────────────────────────────────────────────────────────
// 4) HANDLE “UPLOAD ABSENT LETTER” FORM SUBMISSION
// ─────────────────────────────────────────────────────────────────────────────
$errorMsg   = "";
$successMsg = "";

if (isset($_POST['upload_letter']) && isset($_POST['attendance_id'])) {
    $attendanceID = (int) $_POST['attendance_id'];

    // Check that a file was uploaded without errors
    if (isset($_FILES['absent_letter']) && $_FILES['absent_letter']['error'] === UPLOAD_ERR_OK) {
        $fileTmpPath   = $_FILES['absent_letter']['tmp_name'];
        $origName      = $_FILES['absent_letter']['name'];
        $fileExtension = strtolower(pathinfo($origName, PATHINFO_EXTENSION));
        $allowedExts   = ['pdf','doc','docx'];

        if (!in_array($fileExtension, $allowedExts)) {
            $errorMsg = "Upload failed: only PDF, DOC, or DOCX files are allowed.";
        } else {
            // Create a unique filename: absent_{AttendanceID}_{timestamp}.{ext}
            $newFileName = "absent_{$attendanceID}_" . time() . "." . $fileExtension;
            $destPath    = $globalUploadDir . "/{$newFileName}";

            if (move_uploaded_file($fileTmpPath, $destPath)) {
                // Update the database row to store the filename
                $uStmt = $conn->prepare("
                    UPDATE attendance_report
                    SET AbsentLetter = ?
                    WHERE AttendanceID = ?
                ");
                $uStmt->bind_param("si", $newFileName, $attendanceID);
                $uStmt->execute();
                if ($uStmt->affected_rows >= 0) {
                    $successMsg = "Absent letter uploaded successfully.";
                } else {
                    $errorMsg = "Database update failed after file upload.";
                }
                $uStmt->close();
            } else {
                $errorMsg = "Failed to move the uploaded file to the server directory.";
            }
        }
    } else {
        $errorMsg = "No file selected or error during upload.";
    }
}

// ─────────────────────────────────────────────────────────────────────────────
// 4b) HANDLE "DELETE ABSENT LETTER" FORM SUBMISSION
// ─────────────────────────────────────────────────────────────────────────────
if (isset($_POST['delete_letter']) && isset($_POST['attendance_id'])) {
    $attendanceID = (int) $_POST['attendance_id'];

    // First, get the current file name from database
    $getFileStmt = $conn->prepare("SELECT AbsentLetter FROM attendance_report WHERE AttendanceID = ?");
    $getFileStmt->bind_param("i", $attendanceID);
    $getFileStmt->execute();
    $fileResult = $getFileStmt->get_result();

    if ($fileResult->num_rows > 0) {
        $fileRow = $fileResult->fetch_assoc();
        $currentFile = $fileRow['AbsentLetter'];

        if (!empty($currentFile)) {
            // Delete the physical file
            $filePath = $globalUploadDir . "/" . $currentFile;
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            // Update database to remove file reference
            $deleteFileStmt = $conn->prepare("UPDATE attendance_report SET AbsentLetter = NULL WHERE AttendanceID = ?");
            $deleteFileStmt->bind_param("i", $attendanceID);
            $deleteFileStmt->execute();

            if ($deleteFileStmt->affected_rows > 0) {
                $successMsg = "Absent letter deleted successfully.";
            } else {
                $errorMsg = "Failed to update database after file deletion.";
            }
            $deleteFileStmt->close();
        } else {
            $errorMsg = "No file found to delete.";
        }
    } else {
        $errorMsg = "Attendance record not found.";
    }
    $getFileStmt->close();
}
// ─────────────────────────────────────────────────────────────────────────────
// 5) HANDLE “GENERATE WARNING LETTER” FORM SUBMISSION (using PHPMailer)
// ─────────────────────────────────────────────────────────────────────────────
if (isset($_POST['generate_warning'], $_POST['attendance_id'])) {
    $attendanceID = (int) $_POST['attendance_id'];

    // ─────────────────────────────────────────────────────────────────────────
    // 5a) FETCH ATTENDANCE RECORD + STUDENT DATA
    // ─────────────────────────────────────────────────────────────────────────
    $gStmt = $conn->prepare("
        SELECT
          ar.Att_Date,
          s.Name    AS student_name,
          s.Email   AS student_email,
          c.Course_Name
        FROM attendance_report ar
        JOIN student s ON ar.StudentID = s.StudentID
        JOIN course  c ON ar.CourseID  = c.CourseID
        WHERE ar.AttendanceID = ?
    ");
    $gStmt->bind_param("i", $attendanceID);
    $gStmt->execute();
    $gRes = $gStmt->get_result();
    if ($gRes->num_rows === 0) {
        $errorMsg = "Attendance record not found (ID = {$attendanceID}).";
        $gStmt->close();
    } else {
        $row = $gRes->fetch_assoc();
        $gStmt->close();

        $studentName  = $row['student_name'];
        $studentEmail = $row['student_email'];
        $courseName   = $row['Course_Name'];
        $attDate      = $row['Att_Date'];

        // ─────────────────────────────────────────────────────────────────────
        // 5b) GENERATE PDF “WARNING LETTER” VIA FPDF
        // ─────────────────────────────────────────────────────────────────────
        $pdf = new FPDF();
        $pdf->AddPage();
        $pdf->SetFont('Arial', 'B', 16);
        $pdf->Cell(0, 10, "Warning Letter", 0, 1, 'C');
        $pdf->Ln(10);

        $pdf->SetFont('Arial', '', 12);
        $pdf->MultiCell(0, 8, "Dear {$studentName},");
        $pdf->Ln(5);

        $body  = "This letter serves as a formal warning that you were marked ";
        $body .= "'ABSENT' for the course \"{$courseName}\" on {$attDate}, ";
        $body .= "without submitting a valid excuse beyond any absent letter you may have provided. ";
        $body .= "Your continued absence may negatively affect your final grade and academic standing. ";
        $body .= "Please submit any further documentation immediately or contact the lecturer to avoid further action.";

        $pdf->MultiCell(0, 6, $body);
        $pdf->Ln(10);

        $pdf->MultiCell(0, 6, "Sincerely,");
        $pdf->Ln(5);
        $pdf->MultiCell(0, 6, "{$lecturer_name} (Lecturer)");
        $pdf->Ln(5);
        $pdf->MultiCell(0, 6, "Faculty of Computer Science & IT");
        $pdf->Ln(10);

        $pdf->SetFont('Arial', 'I', 10);
        $pdf->Cell(0, 6, "Generated on " . date('Y-m-d H:i:s'), 0, 1, 'R');

        // Save the PDF under /uploads/
        $warningFileName = "warning_{$attendanceID}_" . time() . ".pdf";
        $warningPath     = $globalUploadDir . "/{$warningFileName}";
        $pdf->Output('F', $warningPath);

        // ─────────────────────────────────────────────────────────────────────
        // 5c) EMAIL THE PDF VIA Enhanced Email Service
        // ─────────────────────────────────────────────────────────────────────

        // Validate email configuration before attempting to send
        if (!defined('SMTP_USER') || SMTP_USER === '<EMAIL>' ||
            !defined('SMTP_PASS') || SMTP_PASS === 'your-app-password') {
            $errorMsg = "Email configuration is incomplete. Please configure SMTP settings in config.php. " .
                       "<a href='../modules/test_email_config.php' style='color: #007bff;'>Test Email Configuration</a>";
        } else {
            $emailService = new EmailService();

            // Validate student email before sending
            if (!$emailService->validateEmail($studentEmail)) {
                $errorMsg = "Invalid student email address: " . htmlspecialchars($studentEmail) .
                           ". Please update the student's email address before sending warning letter.";
            } else {
                // Send warning letter email
                $emailResult = $emailService->sendWarningLetter(
                    $studentEmail,
                    $studentName,
                    $courseName,
                    $attDate,
                    $lecturer_name,
                    $warningPath,
                    $warningFileName
                );

                if ($emailResult['success']) {
                    $successMsg = $emailResult['message'];

                    // Update database to track email sending
                    $updateStmt = $conn->prepare("
                        UPDATE attendance_report
                        SET Remark = CONCAT(COALESCE(Remark, ''),
                                          CASE WHEN COALESCE(Remark, '') = '' THEN '' ELSE '; ' END,
                                          'Warning letter sent on ', NOW())
                        WHERE AttendanceID = ?
                    ");
                    $updateStmt->bind_param("i", $attendanceID);
                    $updateStmt->execute();
                    $updateStmt->close();

                    // Log successful email sending
                    error_log("Warning letter successfully sent to {$studentName} ({$studentEmail}) for course {$courseName}");

                    // Optionally delete the PDF file after successful sending
                    // unlink($warningPath);

                } else {
                    $errorMsg = $emailResult['message'];

                    // Provide helpful error message based on common issues
                    if (strpos($emailResult['message'], 'Invalid address') !== false) {
                        $errorMsg .= " <br><strong>Tip:</strong> Check your SMTP configuration in config.php. " .
                                   "<a href='../modules/test_email_config.php' style='color: #007bff;'>Test Email Configuration</a>";
                    } elseif (strpos($emailResult['message'], 'Authentication failed') !== false) {
                        $errorMsg .= " <br><strong>Tip:</strong> Verify your email password. For Gmail, use App Password instead of regular password.";
                    } elseif (strpos($emailResult['message'], 'Connection') !== false) {
                        $errorMsg .= " <br><strong>Tip:</strong> Check your SMTP host and port settings.";
                    }

                    // Log detailed error information
                    error_log("Failed to send warning letter: " . $emailResult['message']);
                    if ($emailService->getLastError()) {
                        error_log("Email Service Error Details: " . $emailService->getLastError());
                    }

                    // In debug mode, show additional information
                    if (defined('EMAIL_DEBUG_MODE') && EMAIL_DEBUG_MODE) {
                        $debugLog = $emailService->getDebugLog();
                        if (!empty($debugLog)) {
                            error_log("Email Debug Log: " . implode("\n", $debugLog));
                            $errorMsg .= "<br><small>Debug information has been logged.</small>";
                        }
                    }
                }
            }
        }

        // optionally unlink($warningPath);
    }
}

// ─────────────────────────────────────────────────────────────────────────────
// 6) HANDLE FILTERING & FETCH ATTENDANCE RECORDS (Enhanced with Date Filter)
// ─────────────────────────────────────────────────────────────────────────────
$filter  = "";
$params  = [];
$assignment_filter = "";
$date_filter = "";
$selected_date = "";
$selected_assignment_id = "";
$selected_course_id = "";

// Handle form submission for filtering
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Handle date filter
    if (isset($_POST['filter_date']) && !empty($_POST['filter_date'])) {
        $selected_date = $_POST['filter_date'];
        $date_filter = " AND DATE(ar.Att_Date) = ?";
    }

    // Handle assignment/course filter
    if (isset($_POST['assignment_id']) && $_POST['assignment_id'] !== "") {
        $selected_assignment_id = intval($_POST['assignment_id']);
        if ($dbCapabilities['course_assignments']) {
            $assignment_filter = "WHERE ca.AssignmentID = ? AND ca.LectID = ?";
            $params = [$selected_assignment_id, $lecturer_id];
        }
    } elseif (isset($_POST['course_id']) && $_POST['course_id'] !== "") {
        $selected_course_id = intval($_POST['course_id']);
        if ($dbCapabilities['course_assignments']) {
            $assignment_filter = "WHERE ca.CourseID = ? AND ca.LectID = ?";
            $params = [$selected_course_id, $lecturer_id];
        } else {
            $filter = "WHERE ar.CourseID = ?";
            $params = [$selected_course_id];
        }
    }
} else {
    // Default: show only records for courses/assignments taught by this lecturer
    if ($dbCapabilities['course_assignments']) {
        $assignment_filter = "WHERE ca.LectID = ?";
        $params = [$lecturer_id];
    } else {
        $filter = "WHERE c.LectID = ?";
        $params = [$lecturer_id];
    }
}

// Build the SQL query to show ALL registered students with their attendance status
if ($dbCapabilities['course_assignments'] && $dbCapabilities['registration_section']) {
    // Enhanced system: Show all registered students with section-based registration
    $sql = "
        SELECT
            ar.AttendanceID,
            s.StudentID,
            s.Name AS StudentName,
            s.Email AS StudentEmail,
            " . ($dbCapabilities['course_code'] ? "c.Course_Code," : "") . "
            c.Course_Name,
            c.CourseID,
            ca.Section,
            ca.AssignmentID,
            COALESCE(ar.Att_Date, ?) AS Att_Date,
            COALESCE(ar.Att_Status, 'Absent') AS Att_Status,
            ar.Remark,
            ar.Timestamp,
            ar.AbsentLetter
        FROM course_assignments ca
        JOIN course c ON ca.CourseID = c.CourseID
        JOIN course_registration cr ON ca.CourseID = cr.CourseID AND ca.Section = cr.Section
        JOIN student s ON cr.StudentID = s.StudentID
        LEFT JOIN attendance_report ar ON s.StudentID = ar.StudentID
            AND ca.CourseID = ar.CourseID
            " . (!empty($selected_date) ? "AND DATE(ar.Att_Date) = ?" : "") . "
        $assignment_filter
        " . (!empty($selected_date) ? "" : $date_filter) . "
        ORDER BY ca.Section ASC, s.Name ASC
    ";
} elseif ($dbCapabilities['course_assignments']) {
    // Course assignments without section-based registration
    $sql = "
        SELECT
            ar.AttendanceID,
            s.StudentID,
            s.Name AS StudentName,
            s.Email AS StudentEmail,
            " . ($dbCapabilities['course_code'] ? "c.Course_Code," : "") . "
            c.Course_Name,
            c.CourseID,
            ca.Section,
            ca.AssignmentID,
            COALESCE(ar.Att_Date, ?) AS Att_Date,
            COALESCE(ar.Att_Status, 'Absent') AS Att_Status,
            ar.Remark,
            ar.Timestamp,
            ar.AbsentLetter
        FROM course_assignments ca
        JOIN course c ON ca.CourseID = c.CourseID
        JOIN course_registration cr ON ca.CourseID = cr.CourseID
        JOIN student s ON cr.StudentID = s.StudentID
        LEFT JOIN attendance_report ar ON s.StudentID = ar.StudentID
            AND ca.CourseID = ar.CourseID
            " . (!empty($selected_date) ? "AND DATE(ar.Att_Date) = ?" : "") . "
        $assignment_filter
        " . (!empty($selected_date) ? "" : $date_filter) . "
        ORDER BY ca.Section ASC, s.Name ASC
    ";
} else {
    // Legacy system: course-based registration
    $sql = "
        SELECT
            ar.AttendanceID,
            s.StudentID,
            s.Name AS StudentName,
            s.Email AS StudentEmail,
            " . ($dbCapabilities['course_code'] ? "c.Course_Code," : "") . "
            c.Course_Name,
            c.CourseID,
            '' as Section,
            NULL as AssignmentID,
            COALESCE(ar.Att_Date, ?) AS Att_Date,
            COALESCE(ar.Att_Status, 'Absent') AS Att_Status,
            ar.Remark,
            ar.Timestamp,
            ar.AbsentLetter
        FROM course c
        JOIN course_registration cr ON c.CourseID = cr.CourseID
        JOIN student s ON cr.StudentID = s.StudentID
        LEFT JOIN attendance_report ar ON s.StudentID = ar.StudentID
            AND c.CourseID = ar.CourseID
            " . (!empty($selected_date) ? "AND DATE(ar.Att_Date) = ?" : "") . "
        $filter
        " . (!empty($selected_date) ? "" : $date_filter) . "
        ORDER BY s.Name ASC
    ";
}

// Execute the query with proper parameter binding
$fStmt = $conn->prepare($sql);

// Build parameter array for binding
$bind_params = [];
$bind_types = "";

// Add date parameter for COALESCE (current date if no specific date selected)
$current_date = !empty($selected_date) ? $selected_date : date('Y-m-d');
$bind_params[] = $current_date;
$bind_types .= "s";

// Add date filter parameter if specific date is selected
if (!empty($selected_date)) {
    $bind_params[] = $selected_date;
    $bind_types .= "s";
}

// Add assignment/course filter parameters
if (!empty($params)) {
    foreach ($params as $param) {
        $bind_params[] = $param;
        $bind_types .= "i";
    }
}

// Bind parameters and execute
if (!empty($bind_params)) {
    $fStmt->bind_param($bind_types, ...$bind_params);
}
$fStmt->execute();
$result = $fStmt->get_result();

?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Attendance Report</title>
  <!--<link rel="stylesheet" href="../test-styles.css"> -->

  <!-- FontAwesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Modular CSS Architecture -->
  <link rel="stylesheet" href="css/base-styles.css">
<link rel="stylesheet" href="css/lecturer-header.css">
<link rel="stylesheet" href="css/lecturer-sidebar.css">
<link rel="stylesheet" href="css/lecturer-footer.css">
<link rel="stylesheet" href="css/attendance-report-styles.css">


  <!-- Optional: Uncomment to use Bootstrap 4 styling
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css">
  -->

  <style>
    /* Minimal table styling if you’re not using Bootstrap */
    .attendance-table { width: 100%; border-collapse: collapse; margin-top: 1rem; }
    .attendance-table th, .attendance-table td {
      border: 1px solid #ccc;
      padding: 0.5rem;
      text-align: center;
    }
    .attendance-table th { background: #eee; }
    .msg { margin: 1rem 0; padding: 0.75rem; border-radius: 4px; }
    .msg.error   { background: #f8d7da; color: #721c24; }
    .msg.success { background: #d4edda; color: #155724; }
    .action-btn, .upload-btn, .warn-btn {
      padding: 0.3rem 0.6rem;
      font-size: 0.9rem;
      border: none;
      border-radius: 3px;
      cursor: pointer;
    }
    .action-btn.update   { background: #007bff; color: #fff; }
    .action-btn.delete   { background: #dc3545; color: #fff; margin-left: 0.5rem; }
    .upload-btn          { background: #17a2b8; color: #fff; }
    .warn-btn            { background: #ffc107; color: #212529; }
    select, input[type="text"], input[type="file"] {
      font-size: 0.9rem;
      padding: 0.25rem;
    }
    .file-actions {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
    .delete-file-btn {
      background: #dc3545;
      color: #fff;
      border: none;
      padding: 0.3rem 0.5rem;
      border-radius: 3px;
      cursor: pointer;
      font-size: 0.8rem;
    }
    .delete-file-btn:hover {
      background: #c82333;
    }
  </style>
</head>
<body>
  <!-- ─────────── HEADER & SIDEBAR ─────────── -->
  <div class="header">
    <div class="header-left">
      <img src="../assets/images/logo-uthm2.png" alt="UTHM Logo" class="logo">
    </div>
    <div class="header-right">
      <span class="user-id"><?= htmlspecialchars($lecturer_userid) ?></span>
    </div>
  </div>

  <div class="container">
    <div class="sidebar">
      <div class="profile">
        <img src="<?= $photo_url ?>" alt="Profile" class="profile-pic">
        <p class="profile-name"><?= htmlspecialchars($lecturer_name) ?></p>
        <p class="profile-id"><?= htmlspecialchars($lecturer_userid) ?></p>
      </div>
      <ul class="menu">
        <li><a href="lecturer.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
        <li><a href="../modules/profile_lecturer.php"><i class="fas fa-user"></i> Profile</a></li>
        <li><a href="../modules/qr_generate.php"><i class="fas fa-qrcode"></i> Generate QR Code</a></li>
        <li><a href="attendance_report.php" class="active"><i class="fas fa-book"></i> Attendance Report</a></li>
        <li><a href="../modules/courses_details.php"><i class="fas fa-graduation-cap"></i> Course Details</a></li>
        <li><a href="blockchain_records.php"><i class="fas fa-link"></i> Blockchain Report</a></li>
        <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
      </ul>
    </div>

    <div class="main-content">
      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">Attendance Report</h1>
        <p class="page-subtitle">Manage student attendance records, upload absent letters, and generate warning letters</p>

      </div>

      <div class="attendance-report-container">

        <!-- ─────────── Display Error / Success Messages ─────────── -->
        <?php if (!empty($errorMsg)): ?>
          <div class="msg error"><?= $errorMsg ?></div>
        <?php endif; ?>
        <?php if (!empty($successMsg)): ?>
          <div class="msg success"><?= htmlspecialchars($successMsg) ?></div>
        <?php endif; ?>



        <!-- ─────────── Enhanced Filter Section with Date Filter ─────────── -->
        <div class="filter-section">
          <form method="POST" action="" class="filter-form">
            <div class="filter-grid">
              <!-- Date Filter -->
              <div class="filter-group">
                <label for="filter_date">
                  <i class="fas fa-calendar-alt"></i> Filter by Date:
                </label>
                <input type="date"
                       name="filter_date"
                       id="filter_date"
                       value="<?= htmlspecialchars($selected_date) ?>"
                       class="filter-input">
              </div>

              <!-- Course/Section Filter -->
              <div class="filter-group">
                <label for="course">
                  <i class="fas fa-book"></i> Filter by <?= $dbCapabilities['course_assignments'] ? 'Course Section:' : 'Course:' ?>
                </label>
                <select name="<?= $dbCapabilities['course_assignments'] ? 'assignment_id' : 'course_id' ?>"
                        id="course"
                        class="filter-select">
                  <option value="">All <?= $dbCapabilities['course_assignments'] ? 'Course Sections' : 'Courses' ?></option>
              <?php
              if ($dbCapabilities['course_assignments']) {
                  // Fetch course assignments with sections
                  $course_query = "
                      SELECT
                          ca.AssignmentID,
                          " . ($dbCapabilities['course_code'] ? "c.Course_Code," : "c.CourseID as Course_Code,") . "
                          c.Course_Name,
                          ca.Section
                      FROM course_assignments ca
                      JOIN course c ON ca.CourseID = c.CourseID
                      WHERE ca.LectID = ? AND ca.Status = 'Active'
                      " . ($dbCapabilities['course_status'] ? "AND c.Status = 'Active'" : "") . "
                      ORDER BY c.Course_Name ASC, ca.Section ASC
                  ";
                  $cStmt = $conn->prepare($course_query);
                  $cStmt->bind_param("i", $_SESSION['user_id']);
                  $cStmt->execute();
                  $courses = $cStmt->get_result();
                  while ($course = $courses->fetch_assoc()) {
                      $sel = ($selected_assignment_id == $course['AssignmentID']) ? 'selected' : '';
                      $displayName = $course['Course_Code'] . ' - ' . $course['Course_Name'] . ' (' . $course['Section'] . ')';
                      echo "<option value=\"{$course['AssignmentID']}\" $sel>"
                         . htmlspecialchars($displayName)
                         . "</option>";
                  }
                  $cStmt->close();
              } else {
                  // Legacy system: fetch courses directly
                  $course_query = "
                      SELECT
                          CourseID,
                          " . ($dbCapabilities['course_code'] ? "Course_Code," : "CourseID as Course_Code,") . "
                          Course_Name
                      FROM course
                      WHERE LectID = ?
                      " . ($dbCapabilities['course_status'] ? "AND Status = 'Active'" : "") . "
                      ORDER BY Course_Name
                  ";
                  $cStmt = $conn->prepare($course_query);
                  $cStmt->bind_param("i", $_SESSION['user_id']);
                  $cStmt->execute();
                  $courses = $cStmt->get_result();
                  while ($course = $courses->fetch_assoc()) {
                      $sel = ($selected_course_id == $course['CourseID']) ? 'selected' : '';
                      $displayName = $course['Course_Code'] . ' - ' . $course['Course_Name'];
                      echo "<option value=\"{$course['CourseID']}\" $sel>"
                         . htmlspecialchars($displayName)
                         . "</option>";
                  }
                  $cStmt->close();
              }
              ?>
                </select>
              </div>

              <!-- Filter Actions -->
              <div class="filter-actions">
                <button type="submit" class="filter-button">
                  <i class="fas fa-filter"></i> Apply Filters
                </button>
                <a href="attendance_report.php" class="clear-button">
                  <i class="fas fa-times"></i> Clear
                </a>
              </div>
            </div>
          </form>
        </div>

        <!-- ─────────── Enhanced Attendance Table ─────────── -->
        <div class="table-container">
          <?php if (!empty($selected_date)): ?>
          <div class="table-header">
            <h3 class="table-title">
              <i class="fas fa-calendar-check"></i>
              Attendance for <?= date('F j, Y', strtotime($selected_date)) ?>
            </h3>
            <p class="table-subtitle">
              Showing all registered students with their attendance status for the selected date
            </p>
          </div>
          <?php endif; ?>

          <table class="attendance-table">
            <thead>
              <tr>
                <th>Student Name</th>
                <th>Course</th>
                <?php if ($dbCapabilities['course_assignments']): ?>
                <th>Section</th>
                <?php endif; ?>
                <th>Date</th>
                <th>Status</th>
                <th>Remark</th>
                <th>Absent Letter</th>
                <th>Timestamp</th>
                <th>Actions</th>
              </tr>
            </thead>
          <tbody>
            <?php while ($row = $result->fetch_assoc()):
              $attendanceID = $row['AttendanceID'] ? (int) $row['AttendanceID'] : null;
              $studentID = (int) $row['StudentID'];
              $studentEmail = $row['StudentEmail'];
              $status = $row['Att_Status'];
              $remark = ''; // Always start with empty remark field for lecturer input
              $absentFile = $row['AbsentLetter'];
              $courseID = (int) $row['CourseID'];
              $assignmentID = $row['AssignmentID'] ? (int) $row['AssignmentID'] : null;
              $isAbsent = ($status === 'Absent');
              $hasAttendanceRecord = !is_null($attendanceID);
            ?>
            <tr class="<?= $isAbsent ? 'absent-row' : 'present-row' ?>">
              <td>
                <div class="student-info">
                  <div class="student-name"><?= htmlspecialchars($row['StudentName']) ?></div>
                  <div class="student-email"><?= htmlspecialchars($studentEmail) ?></div>
                </div>
              </td>
              <td>
                <div class="course-info">
                  <?php if ($dbCapabilities['course_code'] && !empty($row['Course_Code'])): ?>
                  <span class="course-code-badge"><?= htmlspecialchars($row['Course_Code']) ?></span>
                  <?php endif; ?>
                  <span class="course-name"><?= htmlspecialchars($row['Course_Name']) ?></span>
                </div>
              </td>
              <?php if ($dbCapabilities['course_assignments']): ?>
              <td>
                <span class="section-badge"><?= htmlspecialchars($row['Section']) ?></span>
              </td>
              <?php endif; ?>
              <td>
                <span class="date-badge"><?= date('M j', strtotime($row['Att_Date'])) ?></span>
              </td>

              <!-- ─── STATUS ─── -->
              <td>
                <?php if ($hasAttendanceRecord): ?>
                <select name="status" class="status-select"
                        data-attendance-id="<?= $attendanceID ?>"
                        data-student-id="<?= $studentID ?>"
                        data-course-id="<?= $courseID ?>"
                        data-assignment-id="<?= $assignmentID ?>">
                  <option value="Present" <?= $status === 'Present' ? 'selected' : '' ?>>Present</option>
                  <option value="Absent"  <?= $status === 'Absent'  ? 'selected' : '' ?>>Absent</option>
                </select>
                <?php else: ?>
                <select name="status" class="status-select"
                        data-attendance-id=""
                        data-student-id="<?= $studentID ?>"
                        data-course-id="<?= $courseID ?>"
                        data-assignment-id="<?= $assignmentID ?>">
                  <option value="Present">Present</option>
                  <option value="Absent" selected>Absent</option>
                </select>
                <?php endif; ?>
              </td>

              <!-- ─── REMARK ─── -->
              <td>
                <input type="text" name="remark" value="<?= $remark ?>" placeholder="Enter custom remark..."
                       class="remark-input"
                       data-attendance-id="<?= $attendanceID ?>"
                       data-student-id="<?= $studentID ?>"
                       data-course-id="<?= $courseID ?>"
                       data-assignment-id="<?= $assignmentID ?>">
              </td>

              <!-- ─── UPLOAD / VIEW ABSENT LETTER ─── -->
              <td>
                <?php if ($isAbsent): ?>
                  <?php if (!empty($absentFile)): ?>
                    <div class="file-actions">
                      <a href="../uploads/<?= rawurlencode($absentFile) ?>" target="_blank" class="view-letter-link">
                        <i class="fas fa-file-pdf"></i> View
                      </a>
                      <form method="post" style="display: inline; margin-left: 0.5rem;">
                        <input type="hidden" name="attendance_id" value="<?= $attendanceID ?>">
                        <button type="submit" name="delete_letter" class="delete-file-btn" title="Delete file"
                                onclick="return confirm('Are you sure you want to delete this absent letter file?')">
                          <i class="fas fa-trash-alt"></i>
                        </button>
                      </form>
                    </div>
                  <?php elseif ($hasAttendanceRecord): ?>
                    <div class="upload-container">
                      <form method="post" enctype="multipart/form-data" class="upload-form">
                        <input type="hidden" name="attendance_id" value="<?= $attendanceID ?>">
                        <input type="file" name="absent_letter" accept=".pdf,.doc,.docx" required class="file-input">
                        <button type="submit" name="upload_letter" class="upload-btn">
                          <i class="fas fa-upload"></i> Upload
                        </button>
                      </form>
                    </div>
                  <?php else: ?>
                    <span class="text-muted">
                      <i class="fas fa-info-circle"></i> Mark absent first
                    </span>
                  <?php endif; ?>
                <?php else: ?>
                  <span class="text-muted">—</span>
                <?php endif; ?>
              </td>

              <!-- ─── TIMESTAMP ─── -->
              <td>
                <?php if ($hasAttendanceRecord && $row['Timestamp']): ?>
                <div class="timestamp">
                  <?= date('M j, Y', strtotime($row['Timestamp'])) ?><br>
                  <small style="color: #94a3b8;"><?= date('g:i A', strtotime($row['Timestamp'])) ?></small>
                </div>
                <?php else: ?>
                <span class="text-muted">
                  <i class="fas fa-clock"></i> Not recorded
                </span>
                <?php endif; ?>
              </td>

              <!-- ─── ACTIONS ─── -->
              <td>
                <div class="action-buttons">
                  <!-- Update Button -->
                  <button type="button"
                          class="action-btn update"
                          onclick="updateRecord('<?= $attendanceID ?>', '<?= $studentID ?>', '<?= $courseID ?>', '<?= $assignmentID ?>')"
                          title="Save changes">
                    <i class="fas fa-save"></i>
                  </button>

                  <!-- Warning Letter Button (For Absent Students) -->
                  <?php if ($isAbsent): ?>
                    <?php if ($hasAttendanceRecord): ?>
                    <!-- Student has attendance record - show warning button -->
                    <form method="post" style="display: inline;">
                      <input type="hidden" name="attendance_id" value="<?= $attendanceID ?>">
                      <button type="submit" name="generate_warning" class="action-btn warning-btn" title="Send warning letter">
                        <i class="fas fa-exclamation-triangle"></i>
                      </button>
                    </form>
                    <?php else: ?>
                    <!-- Student has no attendance record - show create record button -->
                    <button type="button"
                            class="action-btn create-record-btn"
                            onclick="createAbsentRecord('<?= $studentID ?>', '<?= $courseID ?>', '<?= $assignmentID ?>')"
                            title="Create absent record first">
                      <i class="fas fa-plus"></i>
                    </button>
                    <?php endif; ?>
                  <?php endif; ?>


                  <!-- Delete Button (Only for existing records) -->
                  <?php if ($hasAttendanceRecord): ?>
                  <form method="post" action="../modules/edit_attendance.php" style="display: inline;">
                    <input type="hidden" name="attendance_id" value="<?= $attendanceID ?>">
                    <button type="submit" name="delete" class="action-btn delete" title="Delete record"
                            onclick="return confirm('Are you sure you want to delete this attendance record?')">
                      <i class="fas fa-trash-alt"></i>
                    </button>
                  </form>
                  <?php endif; ?>
                </div>
              </td>
            </tr>
            <?php endwhile; ?>
          </tbody>
        </table>
        </div>
      </div>
    </div> <!-- end .main-content -->
  </div>   <!-- end .container -->

  <footer>
    <p>UNIVERSITI TUN HUSSEIN ONN MALAYSIA</p>
  </footer>

  <script>
    // Enhanced auto-save functionality for status and remark changes
    function updateRecord(attendanceId, studentId, courseId, assignmentId) {
      const statusSelect = document.querySelector(`select[data-attendance-id="${attendanceId}"][data-student-id="${studentId}"]`);
      const remarkInput = document.querySelector(`input[data-attendance-id="${attendanceId}"][data-student-id="${studentId}"]`);

      if (!statusSelect || !remarkInput) {
        console.error('Could not find form elements');
        return;
      }

      const formData = new FormData();

      // Handle both existing and new attendance records
      if (attendanceId && attendanceId !== '') {
        formData.append('attendance_id', attendanceId);
      } else {
        // Create new attendance record
        formData.append('student_id', studentId);
        formData.append('course_id', courseId);
        if (assignmentId && assignmentId !== '') {
          formData.append('assignment_id', assignmentId);
        }
      }

      formData.append('status', statusSelect.value);
      formData.append('remark', remarkInput.value);
      formData.append('update', '1');

      // Show loading state
      const button = document.querySelector(`button[onclick*="${studentId}"]`);
      if (button) {
        button.classList.add('loading');
        button.disabled = true;
      }

      fetch('../modules/edit_attendance.php', {
        method: 'POST',
        body: formData
      })
      .then(response => response.text())
      .then(data => {
        // Show success feedback
        if (button) {
          button.classList.remove('loading');
          button.disabled = false;
          const originalHTML = button.innerHTML;
          button.innerHTML = '<i class="fas fa-check"></i>';
          button.style.background = '#10b981';

          setTimeout(() => {
            button.innerHTML = originalHTML;
            button.style.background = '';
            // Refresh page to show updated data
            window.location.reload();
          }, 1500);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        if (button) {
          button.classList.remove('loading');
          button.disabled = false;
        }
        alert('Error updating record. Please try again.');
      });
    }

    // Auto-save on status change
    document.addEventListener('change', function(e) {
      if (e.target.classList.contains('status-select')) {
        const attendanceId = e.target.getAttribute('data-attendance-id');
        // Auto-update after a short delay
        setTimeout(() => updateRecord(attendanceId), 500);
      }
    });

    // Auto-save on remark blur
    document.addEventListener('blur', function(e) {
      if (e.target.classList.contains('remark-input')) {
        const attendanceId = e.target.getAttribute('data-attendance-id');
        updateRecord(attendanceId);
      }
    }, true);



    // Create absent record function
    function createAbsentRecord(studentId, courseId, assignmentId) {
      if (!confirm('Create absent record for this student? This will allow you to send a warning letter.')) {
        return;
      }

      const formData = new FormData();
      formData.append('student_id', studentId);
      formData.append('course_id', courseId);
      if (assignmentId && assignmentId !== '') {
        formData.append('assignment_id', assignmentId);
      }
      formData.append('status', 'Absent');
      formData.append('remark', 'Absent record created for warning letter');
      formData.append('update', '1');

      fetch('../modules/edit_attendance.php', {
        method: 'POST',
        body: formData
      })
      .then(response => response.text())
      .then(data => {
        alert('Absent record created successfully! Page will refresh.');
        window.location.reload();
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Error creating absent record. Please try again.');
      });
    }

    // Enhanced table interactions
    document.addEventListener('DOMContentLoaded', function() {

      // Add loading states to buttons
      const buttons = document.querySelectorAll('.action-btn, .upload-btn, .warn-btn');
      buttons.forEach(button => {
        button.addEventListener('click', function() {
          if (this.type === 'submit' && !this.classList.contains('menu-toggle')) {
            this.style.opacity = '0.7';
            this.style.pointerEvents = 'none';
            const originalText = this.textContent;
            this.textContent = 'Loading...';

            setTimeout(() => {
              this.style.opacity = '';
              this.style.pointerEvents = '';
              this.textContent = originalText;
            }, 3000);
          }
        });
      });

      // Auto-save on status change with enhanced logic
      document.addEventListener('change', function(e) {
        if (e.target.classList.contains('status-select')) {
          const attendanceId = e.target.getAttribute('data-attendance-id');
          const studentId = e.target.getAttribute('data-student-id');
          const courseId = e.target.getAttribute('data-course-id');
          const assignmentId = e.target.getAttribute('data-assignment-id');

          // Auto-update after a short delay
          setTimeout(() => {
            updateRecord(attendanceId, studentId, courseId, assignmentId);
          }, 500);
        }
      });

      // Auto-save on remark blur with enhanced logic
      document.addEventListener('blur', function(e) {
        if (e.target.classList.contains('remark-input')) {
          const attendanceId = e.target.getAttribute('data-attendance-id');
          const studentId = e.target.getAttribute('data-student-id');
          const courseId = e.target.getAttribute('data-course-id');
          const assignmentId = e.target.getAttribute('data-assignment-id');

          updateRecord(attendanceId, studentId, courseId, assignmentId);
        }
      }, true);
    });
  </script>
</body>
</html>
