<?php
// ─────────────────────────────────────────────────────────────────────────────
//  dashboard/lecturer.php
//  - Lecturer Dashboard: shows summary cards and a table of courses taught
//  - Uses the same header/sidebar/footer structure as attendance_report.php
//  - Dynamically fetches courses and registration counts
// ─────────────────────────────────────────────────────────────────────────────

session_start();
require '../config/config.php';             // your DB connection

// ─────────────────────────────────────────────────────────────────────────────
// 1) SECURITY CHECK: only 'lecturer' role can access this page
// ─────────────────────────────────────────────────────────────────────────────
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'lecturer') {
    header("Location: ../index.php");
    exit();
}

// ─────────────────────────────────────────────────────────────────────────────
// 2) DATABASE CAPABILITIES DETECTION
// ─────────────────────────────────────────────────────────────────────────────
function checkDatabaseCapabilities($conn) {
    $capabilities = [
        'course_code' => false,
        'course_status' => false,
        'course_assignments' => false,
        'registration_section' => false
    ];

    // Check if Course_Code column exists
    $checkCodeColumn = "SHOW COLUMNS FROM course LIKE 'Course_Code'";
    $result = $conn->query($checkCodeColumn);
    $capabilities['course_code'] = $result && $result->num_rows > 0;

    // Check if Status column exists
    $checkStatusColumn = "SHOW COLUMNS FROM course LIKE 'Status'";
    $result = $conn->query($checkStatusColumn);
    $capabilities['course_status'] = $result && $result->num_rows > 0;

    // Check if course_assignments table exists
    $checkAssignmentsTable = "SHOW TABLES LIKE 'course_assignments'";
    $result = $conn->query($checkAssignmentsTable);
    $capabilities['course_assignments'] = $result && $result->num_rows > 0;

    // Check if Section column exists in course_registration
    $checkSectionColumn = "SHOW COLUMNS FROM course_registration LIKE 'Section'";
    $result = $conn->query($checkSectionColumn);
    $capabilities['registration_section'] = $result && $result->num_rows > 0;

    return $capabilities;
}

$dbCapabilities = checkDatabaseCapabilities($conn);

// ─────────────────────────────────────────────────────────────────────────────
// 3) FETCH LECTURER INFO (for header/sidebar display)
// ─────────────────────────────────────────────────────────────────────────────
$lecturer_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT Name, UserID, profile_pic FROM lecturer WHERE LectID = ?");
$stmt->bind_param("i", $lecturer_id);
$stmt->execute();
$res = $stmt->get_result();
$lecturer = $res->fetch_assoc();
$lecturer_name   = $lecturer['Name']   ?? 'Lecturer Name';
$lecturer_userid = $lecturer['UserID'] ?? 'N/A';
$stmt->close();

// Profile picture URL
$photo_url = !empty($lecturer['profile_pic'])
    ? "../assets/images/" . rawurlencode($lecturer['profile_pic'])
    : "../assets/images/user1.png";

// ─────────────────────────────────────────────────────────────────────────────
// 3) FETCH SUMMARY DATA:
//    - Total number of courses taught by this lecturer
//    - Total number of unique students across all those courses
// ─────────────────────────────────────────────────────────────────────────────
// 3a) Total courses
$coursesCount = 0;
$sectionsCount = 0;

if ($dbCapabilities['course_assignments']) {
    // New system: count course assignments (sections)
    $countStmt = $conn->prepare("
        SELECT COUNT(*) AS cnt
        FROM course_assignments ca
        JOIN course c ON ca.CourseID = c.CourseID
        WHERE ca.LectID = ? AND ca.Status = 'Active'
        " . ($dbCapabilities['course_status'] ? "AND c.Status = 'Active'" : "") . "
    ");
    $countStmt->bind_param("i", $lecturer_id);
    $countStmt->execute();
    $countRes = $countStmt->get_result();
    if ($row = $countRes->fetch_assoc()) {
        $sectionsCount = (int)$row['cnt'];
    }
    $countStmt->close();

    // Count unique courses
    $uniqueCoursesStmt = $conn->prepare("
        SELECT COUNT(DISTINCT ca.CourseID) AS cnt
        FROM course_assignments ca
        JOIN course c ON ca.CourseID = c.CourseID
        WHERE ca.LectID = ? AND ca.Status = 'Active'
        " . ($dbCapabilities['course_status'] ? "AND c.Status = 'Active'" : "") . "
    ");
    $uniqueCoursesStmt->bind_param("i", $lecturer_id);
    $uniqueCoursesStmt->execute();
    $uniqueCoursesRes = $uniqueCoursesStmt->get_result();
    if ($row = $uniqueCoursesRes->fetch_assoc()) {
        $coursesCount = (int)$row['cnt'];
    }
    $uniqueCoursesStmt->close();
} else {
    // Legacy system
    $countStmt = $conn->prepare("SELECT COUNT(*) AS cnt FROM course WHERE LectID = ?");
    $countStmt->bind_param("i", $lecturer_id);
    $countStmt->execute();
    $countRes = $countStmt->get_result();
    if ($row = $countRes->fetch_assoc()) {
        $coursesCount = (int)$row['cnt'];
    }
    $countStmt->close();
}

// 3b) Total unique students across all lecturer’s courses
$studentsCount = 0;
$recentAttendance = 0;

if ($dbCapabilities['course_assignments']) {
    // Count students with section-based registration
    if ($dbCapabilities['registration_section']) {
        $uniqStmt = $conn->prepare("
            SELECT COUNT(DISTINCT cr.StudentID) AS cnt
            FROM course_registration cr
            JOIN course_assignments ca ON cr.CourseID = ca.CourseID AND cr.Section = ca.Section
            WHERE ca.LectID = ? AND ca.Status = 'Active'
        ");
    } else {
        $uniqStmt = $conn->prepare("
            SELECT COUNT(DISTINCT cr.StudentID) AS cnt
            FROM course_registration cr
            JOIN course_assignments ca ON cr.CourseID = ca.CourseID
            WHERE ca.LectID = ? AND ca.Status = 'Active'
        ");
    }
    $uniqStmt->bind_param("i", $lecturer_id);
    $uniqStmt->execute();
    $uniqRes = $uniqStmt->get_result();
    if ($row = $uniqRes->fetch_assoc()) {
        $studentsCount = (int)$row['cnt'];
    }
    $uniqStmt->close();
} else {
    // Legacy system
    $uniqStmt = $conn->prepare("
        SELECT COUNT(DISTINCT cr.StudentID) AS cnt
        FROM course_registration cr
        JOIN course c ON cr.CourseID = c.CourseID
        WHERE c.LectID = ?
    ");
    $uniqStmt->bind_param("i", $lecturer_id);
    $uniqStmt->execute();
    $uniqRes = $uniqStmt->get_result();
    if ($row = $uniqRes->fetch_assoc()) {
        $studentsCount = (int)$row['cnt'];
    }
    $uniqStmt->close();
}

// Recent attendance count (last 7 days)
$recentAttStmt = $conn->prepare("
    SELECT COUNT(*) AS RecentCount
    FROM attendance_report ar
    " . ($dbCapabilities['course_assignments'] ?
        "JOIN course_assignments ca ON ar.CourseID = ca.CourseID WHERE ca.LectID = ?" :
        "JOIN course c ON ar.CourseID = c.CourseID WHERE c.LectID = ?") . "
    AND ar.Att_Date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
");
$recentAttStmt->bind_param("i", $lecturer_id);
$recentAttStmt->execute();
$recentAttRes = $recentAttStmt->get_result();
if ($row = $recentAttRes->fetch_assoc()) {
    $recentAttendance = (int)$row['RecentCount'];
}
$recentAttStmt->close();

// ─────────────────────────────────────────────────────────────────────────────
// 5) FETCH COURSE ASSIGNMENTS (Enhanced System)
// ─────────────────────────────────────────────────────────────────────────────
$courses = [];

if ($dbCapabilities['course_assignments']) {
    // New system: fetch course assignments with sections
    $courseStmt = $conn->prepare("
        SELECT
            ca.AssignmentID,
            c.CourseID,
            " . ($dbCapabilities['course_code'] ? "c.Course_Code," : "c.CourseID as Course_Code,") . "
            c.Course_Name AS CourseName,
            ca.Section,
            ca.Status,
            COUNT(cr.StudentID) AS RegCount
        FROM course_assignments ca
        JOIN course c ON ca.CourseID = c.CourseID
        LEFT JOIN course_registration cr ON c.CourseID = cr.CourseID
            " . ($dbCapabilities['registration_section'] ? "AND cr.Section = ca.Section" : "") . "
        WHERE ca.LectID = ? AND ca.Status = 'Active'
        " . ($dbCapabilities['course_status'] ? "AND c.Status = 'Active'" : "") . "
        GROUP BY ca.AssignmentID, c.CourseID, c.Course_Name, ca.Section
        ORDER BY c.Course_Name ASC, ca.Section ASC
    ");
    $courseStmt->bind_param("i", $lecturer_id);
    $courseStmt->execute();
    $courseRes = $courseStmt->get_result();
    while ($row = $courseRes->fetch_assoc()) {
        $courses[] = [
            'AssignmentID' => $row['AssignmentID'],
            'CourseID'     => $row['CourseID'],
            'Course_Code'  => $row['Course_Code'],
            'CourseName'   => $row['CourseName'],
            'Section'      => $row['Section'],
            'RegCount'     => (int)$row['RegCount']
        ];
    }
    $courseStmt->close();
} else {
    // Legacy system: fetch courses directly
    $courseStmt = $conn->prepare("
        SELECT
            c.CourseID,
            " . ($dbCapabilities['course_code'] ? "c.Course_Code," : "c.CourseID as Course_Code,") . "
            c.Course_Name AS CourseName,
            '' as Section,
            'Active' as Status,
            COUNT(cr.StudentID) AS RegCount
        FROM course c
        LEFT JOIN course_registration cr ON c.CourseID = cr.CourseID
        WHERE c.LectID = ?
        " . ($dbCapabilities['course_status'] ? "AND c.Status = 'Active'" : "") . "
        GROUP BY c.CourseID, c.Course_Name
        ORDER BY c.Course_Name
    ");
    $courseStmt->bind_param("i", $lecturer_id);
    $courseStmt->execute();
    $courseRes = $courseStmt->get_result();
    while ($row = $courseRes->fetch_assoc()) {
        $courses[] = [
            'CourseID'    => $row['CourseID'],
            'Course_Code' => $row['Course_Code'],
            'CourseName'  => $row['CourseName'],
            'Section'     => $row['Section'],
            'RegCount'    => (int)$row['RegCount']
        ];
    }
    $courseStmt->close();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Lecturer Dashboard</title>

  <!-- FontAwesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Base + Modular CSS -->
  <link rel="stylesheet" href="css/base-styles.css">
<link rel="stylesheet" href="css/lecturer-header.css">
<link rel="stylesheet" href="css/lecturer-sidebar.css"> 
<link rel="stylesheet" href="css/lecturer-footer.css">
<link rel="stylesheet" href="css/lecturer-dashboard-styles.css">

</head>
<body>
  <!-- ─────────── HEADER ─────────── -->
  <div class="header">
    <div class="header-left">
      <img src="../assets/images/logo-uthm2.png" alt="UTHM Logo" class="logo">
    </div>
    <div class="header-right">
      <span class="user-id"><?= htmlspecialchars($lecturer_userid) ?></span>
    </div>
  </div>

  <!-- ─────────── MAIN CONTAINER (SIDEBAR + CONTENT) ─────────── -->
  <div class="container">
    <!-- ─────────── SIDEBAR ─────────── -->
    <div class="sidebar">
      <div class="profile">
        <img src="<?= $photo_url ?>" alt="Profile" class="profile-pic">
        <p class="profile-name"><?= htmlspecialchars($lecturer_name) ?></p>
        <p class="profile-id"><?= htmlspecialchars($lecturer_userid) ?></p>
      </div>
      <ul class="menu">
        <li><a href="lecturer.php" class="active"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
        <li><a href="../modules/profile_lecturer.php"><i class="fas fa-user"></i> Profile</a></li>
        <li><a href="../modules/qr_generate.php"><i class="fas fa-qrcode"></i> Generate QR Code</a></li>
        <li><a href="attendance_report.php"><i class="fas fa-book"></i> Attendance Report</a></li>
        <li><a href="../modules/courses_details.php"><i class="fas fa-graduation-cap"></i> Course Details</a></li>
        <li><a href="blockchain_records.php"><i class="fas fa-link"></i> Blockchain Report</a></li>
        <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
      </ul>
    </div>

    <!-- ─────────── MAIN CONTENT ─────────── -->
    <div class="main-content">
      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">Dashboard</h1>
        <p class="page-subtitle">Welcome back, <?= htmlspecialchars($lecturer_name) ?>. Here’s your current summary.</p>
      </div>

      <!-- ─────────── ENHANCED SUMMARY CARDS ─────────── -->
      <div class="stats-container">
        <div class="stats-card">
          <div class="stats-icon">
            <i class="fas fa-book-open"></i>
          </div>
          <div class="stats-info">
            <span class="stats-number"><?= $coursesCount ?></span>
            <span class="stats-label">Courses Taught</span>
          </div>
        </div>
        <div class="stats-card">
          <div class="stats-icon">
            <i class="fas fa-user-graduate"></i>
          </div>
          <div class="stats-info">
            <span class="stats-number"><?= $studentsCount ?></span>
            <span class="stats-label">Total Students</span>
          </div>
        </div>
        <?php if ($dbCapabilities['course_assignments']): ?>
        <div class="stats-card">
          <div class="stats-icon">
            <i class="fas fa-layer-group"></i>
          </div>
          <div class="stats-info">
            <span class="stats-number"><?= $sectionsCount ?></span>
            <span class="stats-label">Course Sections</span>
          </div>
        </div>
        <?php endif; ?>
        <div class="stats-card">
          <div class="stats-icon">
            <i class="fas fa-calendar-check"></i>
          </div>
          <div class="stats-info">
            <span class="stats-number"><?= $recentAttendance ?></span>
            <span class="stats-label">Recent Attendance</span>
          </div>
        </div>
      </div>

      <!-- ─────────── ENHANCED COURSES TABLE ─────────── -->
      <div class="courses-container">
        <div class="section-header">
          <h2 class="section-title"><i class="fas fa-chalkboard-teacher"></i> Your Course Assignments</h2>
          <p class="section-subtitle">
            <?php if ($dbCapabilities['course_assignments']): ?>
              Browse all course sections you are assigned to teach. Each section can have different students and schedules.
            <?php else: ?>
              Browse all courses you are teaching and see enrollment counts.
            <?php endif; ?>
          </p>
        </div>

        <?php if (empty($courses)): ?>
          <div class="no-data-msg">
            <i class="fas fa-exclamation-circle"></i>
            You are not assigned to any courses yet.
            <a href="../modules/qr_generate.php">Generate a QR code</a> or contact admin to assign courses.
          </div>
        <?php else: ?>
          <div class="table-container">
            <table class="courses-table">
              <thead>
                <tr>
                  <th>Course Code</th>
                  <th>Course Name</th>
                  <?php if ($dbCapabilities['course_assignments']): ?>
                  <th>Section</th>
                  <?php endif; ?>
                  <th>Enrolled Students</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <?php foreach ($courses as $course): ?>
                  <tr>
                    <td data-label="Course Code">
                      <span class="course-code-badge">
                        <?= htmlspecialchars($course['Course_Code']) ?>
                      </span>
                    </td>
                    <td data-label="Course Name">
                      <i class="fas fa-book-open" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                      <strong><?= htmlspecialchars($course['CourseName']) ?></strong>
                    </td>
                    <?php if ($dbCapabilities['course_assignments']): ?>
                    <td data-label="Section">
                      <span class="section-badge">
                        <?= htmlspecialchars($course['Section']) ?>
                      </span>
                    </td>
                    <?php endif; ?>
                    <td data-label="Enrolled Students">
                      <span class="student-count">
                        <i class="fas fa-users" style="margin-right: 0.25rem;"></i>
                        <?= htmlspecialchars($course['RegCount']) ?>
                      </span>
                    </td>
                    <td data-label="Actions">
                      <a
                        href="../modules/qr_generate.php?<?= $dbCapabilities['course_assignments'] ? 'assignmentID=' . urlencode($course['AssignmentID']) : 'courseID=' . urlencode($course['CourseID']) ?>"
                        class="action-btn generate-qr"
                        title="Generate QR Code for this <?= $dbCapabilities['course_assignments'] ? 'course section' : 'course' ?>">
                        <i class="fas fa-qrcode"></i> QR
                      </a>
                      <a
                        href="attendance_report.php?<?= $dbCapabilities['course_assignments'] ? 'assignment_id=' . urlencode($course['AssignmentID']) : 'course_id=' . urlencode($course['CourseID']) ?>"
                        class="action-btn view-att"
                        title="View Attendance Report">
                        <i class="fas fa-book"></i> View
                      </a>
                    </td>
                  </tr>
                <?php endforeach; ?>
              </tbody>
            </table>
          </div>
        <?php endif; ?>
      </div>
    </div> <!-- end .main-content -->
  </div>   <!-- end .container -->

  <!-- ─────────── FOOTER ─────────── -->
  <footer>
    <p>UNIVERSITI TUN HUSSEIN ONN MALAYSIA</p>
  </footer>

</body>
</html>
