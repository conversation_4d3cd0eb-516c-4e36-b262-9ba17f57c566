<?php
/**
 * ===================================================================
 * SESSION SECURITY UTILITY CLASS
 * Provides consistent session management and security functions
 * ===================================================================
 */

class SessionSecurity {
    
    /**
     * Secure session destruction with comprehensive cleanup
     */
    public static function destroySession() {
        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // Log the session destruction for audit
        if (isset($_SESSION['user_id']) && isset($_SESSION['role'])) {
            $user_id = $_SESSION['user_id'];
            $role = $_SESSION['role'];
            $timestamp = date('Y-m-d H:i:s');
            error_log("SESSION DESTROY: User ID $user_id (Role: $role) session destroyed at $timestamp");
        }
        
        // 1. Unset all session variables
        session_unset();
        
        // 2. Destroy the session
        session_destroy();
        
        // 3. Clear session cookie securely
        if (isset($_COOKIE[session_name()])) {
            setcookie(
                session_name(),           // Session cookie name
                '',                       // Empty value
                time() - 3600,           // Expire in the past
                '/',                     // Path
                '',                      // Domain (empty for current domain)
                isset($_SERVER['HTTPS']), // Secure flag (true if HTTPS)
                true                     // HttpOnly flag
            );
        }
        
        // 4. Clear any other authentication-related cookies
        $authCookies = ['remember_token', 'auth_token', 'user_session', 'qr_token'];
        foreach ($authCookies as $cookieName) {
            if (isset($_COOKIE[$cookieName])) {
                setcookie(
                    $cookieName,
                    '',
                    time() - 3600,
                    '/',
                    '',
                    isset($_SERVER['HTTPS']),
                    true
                );
            }
        }
    }
    
    /**
     * Regenerate session ID to prevent session fixation
     */
    public static function regenerateSessionId() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // Regenerate session ID and delete old session
        session_regenerate_id(true);
        
        // Log session regeneration
        if (isset($_SESSION['user_id']) && isset($_SESSION['role'])) {
            $user_id = $_SESSION['user_id'];
            $role = $_SESSION['role'];
            error_log("SESSION REGENERATE: User ID $user_id (Role: $role) session ID regenerated");
        }
    }
    
    /**
     * Initialize secure session for authenticated user
     */
    public static function initializeSecureSession($user_id, $role) {
        // Regenerate session ID
        self::regenerateSessionId();
        
        // Set session variables
        $_SESSION['user_id'] = $user_id;
        $_SESSION['role'] = $role;
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();
        $_SESSION['session_token'] = bin2hex(random_bytes(32));
        
        // Log successful session initialization
        error_log("SESSION INIT: User ID $user_id (Role: $role) session initialized at " . date('Y-m-d H:i:s'));
    }
    
    /**
     * Validate session and check for timeout
     */
    public static function validateSession($timeout_minutes = 30) {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // Check if user is logged in
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['role'])) {
            return false;
        }
        
        // Check session timeout
        if (isset($_SESSION['last_activity'])) {
            $timeout_seconds = $timeout_minutes * 60;
            if ((time() - $_SESSION['last_activity']) > $timeout_seconds) {
                // Session expired
                error_log("SESSION TIMEOUT: User ID {$_SESSION['user_id']} session expired");
                self::destroySession();
                return false;
            }
        }
        
        // Update last activity
        $_SESSION['last_activity'] = time();
        
        return true;
    }
    
    /**
     * Secure logout with redirect
     */
    public static function secureLogout($redirect_url = 'index.php') {
        // Destroy session
        self::destroySession();
        
        // Set cache prevention headers
        header("Cache-Control: no-cache, no-store, must-revalidate");
        header("Pragma: no-cache");
        header("Expires: 0");
        
        // Redirect to login page
        header("Location: $redirect_url?logout=success");
        exit();
    }
    
    /**
     * Clear session data on login page access
     */
    public static function clearSessionOnLogin() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // Only clear if not submitting login form
        if (!isset($_POST['username'])) {
            session_unset();
            session_regenerate_id(true);
        }
    }
    
    /**
     * Secure password change session handling
     */
    public static function handlePasswordChangeSession($user_id, $role) {
        // Log password change
        error_log("PASSWORD CHANGE: $role ID $user_id changed password at " . date('Y-m-d H:i:s'));
        
        // Destroy current session
        self::destroySession();
        
        // Start new session with regenerated ID
        session_start();
        session_regenerate_id(true);
        
        // Set cache prevention headers
        header("Cache-Control: no-cache, no-store, must-revalidate");
        header("Pragma: no-cache");
        header("Expires: 0");
    }
    
    /**
     * Check if user has required role
     */
    public static function requireRole($required_role, $redirect_url = '../index.php') {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== $required_role) {
            error_log("ACCESS DENIED: Unauthorized access attempt to $required_role area");
            header("Location: $redirect_url");
            exit();
        }
        
        // Validate session timeout
        if (!self::validateSession()) {
            header("Location: $redirect_url");
            exit();
        }
    }
    
    /**
     * Get current user info safely
     */
    public static function getCurrentUser() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['role'])) {
            return null;
        }
        
        return [
            'user_id' => $_SESSION['user_id'],
            'role' => $_SESSION['role'],
            'login_time' => $_SESSION['login_time'] ?? null,
            'last_activity' => $_SESSION['last_activity'] ?? null
        ];
    }
    
    /**
     * Set secure session configuration
     */
    public static function configureSecureSession() {
        // Set secure session configuration
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_only_cookies', 1);
        ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
        ini_set('session.cookie_samesite', 'Strict');
        
        // Set session timeout
        ini_set('session.gc_maxlifetime', 1800); // 30 minutes
        
        // Regenerate session ID periodically
        if (isset($_SESSION['last_regeneration'])) {
            if (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
                session_regenerate_id(true);
                $_SESSION['last_regeneration'] = time();
            }
        } else {
            $_SESSION['last_regeneration'] = time();
        }
    }
}
?>
