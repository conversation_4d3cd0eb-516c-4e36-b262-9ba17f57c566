<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit017c5201b421d99f90b020e446d1b958
{
    public static $prefixLengthsPsr4 = array (
        'R' => 
        array (
            '<PERSON><PERSON><PERSON>ee\\Auth\\' => 14,
        ),
        'P' => 
        array (
            '<PERSON><PERSON><PERSON><PERSON><PERSON>\\PHPMailer\\' => 20,
        ),
    );

    public static $prefixDirsPsr4 = array (
        '<PERSON><PERSON>hree\\Auth\\' => 
        array (
            0 => __DIR__ . '/..' . '/robthree/twofactorauth/lib',
        ),
        'PHPMailer\\PHPMailer\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmailer/phpmailer/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit017c5201b421d99f90b020e446d1b958::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit017c5201b421d99f90b020e446d1b958::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit017c5201b421d99f90b020e446d1b958::$classMap;

        }, null, ClassLoader::class);
    }
}
