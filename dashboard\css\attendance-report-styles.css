/* ===================================================================
   ATTENDANCE REPORT STYLES
   Specific styles for the attendance report page and table
   ================================================================= */



/* Page Header */
.page-header {
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.page-title {
  color: var(--text-primary);
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 var(--spacing-xs) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.page-title::before {
  content: '\f0ca';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  color: var(--primary-color);
  font-size: 1.5rem;
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0;
  font-weight: 400;
}

.attendance-report-container {
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

/* Enhanced Messages */
.msg {
  margin: 1.5rem 2rem;
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  border-left: 4px solid;
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.msg.error {
  background: #fef2f2;
  color: #991b1b;
  border-left-color: var(--danger-color);
}

.msg.error::before {
  content: '\f071';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
}

.msg.success {
  background: #f0fdf4;
  color: #166534;
  border-left-color: var(--success-color);
}

.msg.success::before {
  content: '\f00c';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
}

/* Enhanced Filter Section - Azia Style */
.filter-section {
  background: var(--card-bg);
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  margin: 0;
}

.filter-form {
  width: 100%;
}

.filter-grid {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: var(--spacing-lg);
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.filter-group label {
  font-weight: 500;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.875rem;
}

.filter-input,
.filter-select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--card-bg);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.3s ease;
  font-weight: 500;
}

.filter-select {
  min-width: 220px;
}

.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.filter-actions {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.filter-section label {
  font-weight: 500;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.875rem;
}

.filter-section label::before {
  content: '\f0b0';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  color: var(--primary-color);
  font-size: 1rem;
}

.filter-section select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--card-bg);
  color: var(--text-primary);
  font-size: 0.875rem;
  min-width: 220px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.filter-section select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.filter-button {
  background: var(--primary-color);
  color: var(--card-bg);
  border: none;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.875rem;
}

.filter-button::before {
  content: '\f0b0';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
}

.filter-button:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.clear-button {
  background: #6b7280;
  color: var(--card-bg);
  border: none;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.875rem;
  text-decoration: none;
}

.clear-button:hover {
  background: #4b5563;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
  color: var(--card-bg);
}

/* Table Header Section */
.table-header {
  background: var(--card-bg);
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  margin: 0;
}

.table-title {
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-xs) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.table-subtitle {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0;
  font-weight: 400;
}

/* Professional Table Styling - Azia Inspired */
.table-container {
  background: var(--card-bg);
  border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
  overflow-x: auto;
  box-shadow: var(--shadow-sm);
  max-width: 100%;
  position: relative;
}

.attendance-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin: 0;
  background: var(--card-bg);
  font-size: 0.875rem;
  table-layout: fixed;
}

/* Column Width Distribution */
.attendance-table th:nth-child(1), /* Student Name */
.attendance-table td:nth-child(1) {
  width: 18%;
}

.attendance-table th:nth-child(2), /* Course */
.attendance-table td:nth-child(2) {
  width: 12%;
}

.attendance-table th:nth-child(3), /* Section */
.attendance-table td:nth-child(3) {
  width: 8%;
}

.attendance-table th:nth-child(4), /* Date */
.attendance-table td:nth-child(4) {
  width: 8%;
}

.attendance-table th:nth-child(5), /* Status */
.attendance-table td:nth-child(5) {
  width: 10%;
}

.attendance-table th:nth-child(6), /* Remark */
.attendance-table td:nth-child(6) {
  width: 13%;
}

.attendance-table th:nth-child(7), /* Absent Letter */
.attendance-table td:nth-child(7) {
  width: 11%;
}

.attendance-table th:nth-child(8), /* Timestamp */
.attendance-table td:nth-child(8) {
  width: 10%;
}

.attendance-table th:nth-child(9), /* Actions */
.attendance-table td:nth-child(9) {
  width: 15%;
  display: table-cell !important;
  visibility: visible !important;
  min-width: 120px !important;
}

/* Enhanced Table Headers */
.attendance-table th {
  background: #f8fafc;
  color: #64748b;
  padding: 1rem 0.75rem;
  text-align: left;
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 2px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.attendance-table th:first-child {
  padding-left: 1.5rem;
  border-top-left-radius: var(--border-radius-lg);
}

.attendance-table th:last-child {
  padding-right: 1.5rem;
  border-top-right-radius: var(--border-radius-lg);
}

/* Enhanced Table Cells */
.attendance-table td {
  padding: 1.25rem 0.75rem;
  text-align: left;
  border-bottom: 1px solid #f1f5f9;
  vertical-align: middle;
  color: var(--text-primary);
  background: var(--card-bg);
  transition: all 0.2s ease;
}

.attendance-table td:first-child {
  padding-left: 1.5rem;
}

.attendance-table td:last-child {
  padding-right: 1.5rem;
}

/* Enhanced Row Styling with Status Indicators */
.attendance-table tbody tr:nth-child(even) {
  background: #fafbfc;
}

.attendance-table tbody tr:hover {
  background: #f1f5f9;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Status-based Row Styling */
.attendance-table tbody tr.absent-row {
  border-left: 3px solid #ef4444;
  background: #fef2f2;
}

.attendance-table tbody tr.absent-row:nth-child(even) {
  background: #fef2f2;
}

.attendance-table tbody tr.absent-row:hover {
  background: #fee2e2;
}

.attendance-table tbody tr.present-row {
  border-left: 3px solid #10b981;
  background: #f0fdf4;
}

.attendance-table tbody tr.present-row:nth-child(even) {
  background: #f0fdf4;
}

.attendance-table tbody tr.present-row:hover {
  background: #ecfdf5;
}

.attendance-table tbody tr:last-child td {
  border-bottom: none;
}

.attendance-table tbody tr:last-child td:first-child {
  border-bottom-left-radius: var(--border-radius-lg);
}

.attendance-table tbody tr:last-child td:last-child {
  border-bottom-right-radius: var(--border-radius-lg);
}

/* Professional Form Controls */
.attendance-table select,
.attendance-table input[type="text"],
.attendance-table input[type="file"] {
  padding: 0.5rem 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  background: var(--card-bg);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
  width: 100%;
  font-weight: 400;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.attendance-table select {
  max-width: 100px;
  cursor: pointer;
}

.attendance-table input[type="text"] {
  max-width: 140px;
}

.attendance-table input[type="file"] {
  max-width: 120px;
  font-size: 0.75rem;
  padding: 0.375rem 0.5rem;
}

.attendance-table select:focus,
.attendance-table input[type="text"]:focus,
.attendance-table input[type="file"]:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), 0 1px 2px rgba(0, 0, 0, 0.05);
}

.attendance-table input[type="text"]::placeholder {
  color: #94a3b8;
  font-style: normal;
}

/* Status Select Styling */
.status-select {
  background-color: var(--card-bg);
  border: 1px solid #e2e8f0;
}

.status-select option[value="Present"] {
  background-color: #f0fdf4;
  color: #166534;
}

.status-select option[value="Absent"] {
  background-color: #fef2f2;
  color: #991b1b;
}

/* Professional Action Buttons */
.action-btn, .upload-btn, .warn-btn, .view-btn {
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  text-decoration: none;
  margin: 0.125rem;
  min-width: 70px;
  justify-content: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  text-transform: capitalize;
}

/* Update Button - Blue */
.action-btn.update {
  background: #3b82f6;
  color: white;
}

.action-btn.update::before {
  content: '\f00c';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  font-size: 0.75rem;
}

.action-btn.update:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.25);
}

/* Delete Button - Red */
.action-btn.delete {
  background: #ef4444;
  color: white;
}

.action-btn.delete::before {
  content: '\f2ed';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  font-size: 0.75rem;
}

.action-btn.delete:hover {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(239, 68, 68, 0.25);
}

/* Upload Button - Cyan */
.upload-btn {
  background: #06b6d4;
  color: white;
}

.upload-btn::before {
  content: '\f093';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  font-size: 0.75rem;
}

.upload-btn:hover {
  background: #0891b2;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(6, 182, 212, 0.25);
}

/* Warning Button - Yellow */
.warn-btn, .warning-btn {
  background: #f59e0b;
  color: white;
}

.warn-btn::before, .warning-btn::before {
  content: '\f071';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  font-size: 0.75rem;
}

.warn-btn:hover, .warning-btn:hover {
  background: #d97706;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(245, 158, 11, 0.25);
}

/* Action Button Styling */
.action-btn {
  min-width: 32px;
  height: 32px;
  padding: 0.5rem;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex !important;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  margin: 0.125rem;
}

.action-btn.warning-btn {
  background: #f59e0b !important;
  color: white !important;
}

.action-btn.warning-btn:hover {
  background: #d97706 !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(245, 158, 11, 0.25);
}

/* Save Button */
.action-btn.update,
.action-btn.save-btn {
  background: #3b82f6 !important;
  color: white !important;
}

.action-btn.update:hover,
.action-btn.save-btn:hover {
  background: #2563eb !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.25);
}

/* Create Record Button */
.action-btn.create-record-btn {
  background: #10b981 !important;
  color: white !important;
}

.action-btn.create-record-btn:hover {
  background: #059669 !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(16, 185, 129, 0.25);
}

/* Delete Button */
.action-btn.delete,
.action-btn.delete-btn {
  background: #ef4444 !important;
  color: white !important;
}

.action-btn.delete:hover,
.action-btn.delete-btn:hover {
  background: #dc2626 !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(239, 68, 68, 0.25);
}

/* View Button - Purple */
.view-btn {
  background: #8b5cf6;
  color: white;
}

.view-btn::before {
  content: '\f06e';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  font-size: 0.75rem;
}

.view-btn:hover {
  background: #7c3aed;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(139, 92, 246, 0.25);
}

/* File Upload Styling */
.attendance-table input[type="file"] {
  padding: 0.25rem;
  font-size: 0.75rem;
  max-width: 120px;
}

/* Link Styling */
.attendance-table a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  background: rgba(37, 99, 235, 0.1);
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.attendance-table a::before {
  content: '\f06e';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
}

.attendance-table a:hover {
  background: var(--primary-color);
  color: var(--white);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Enhanced Student Info Display */
.student-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.student-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.student-email {
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-weight: 400;
}

/* Course Info Display */
.course-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.course-code-badge {
  background: var(--primary-color);
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  width: fit-content;
}

.course-name {
  font-size: 0.875rem;
  color: var(--text-primary);
  font-weight: 500;
}

.section-badge {
  background: #f3f4f6;
  color: #374151;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  width: fit-content;
}

.date-badge {
  background: #eff6ff;
  color: #1d4ed8;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  width: fit-content;
}



/* Upload Form Styling */
.upload-form {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: stretch;
}

.file-input {
  font-size: 0.75rem;
  padding: 0.25rem;
}

/* Text Muted */
.text-muted {
  color: var(--text-secondary);
  font-size: 0.75rem;
  font-style: italic;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* View Letter Link */
.view-letter-link {
  background: #8b5cf6;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  text-decoration: none;
  font-size: 0.75rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  transition: all 0.2s ease;
}

.view-letter-link:hover {
  background: #7c3aed;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(139, 92, 246, 0.25);
  color: white;
}



/* Professional Data Presentation */
.student-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.student-name {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 0.875rem;
  line-height: 1.2;
}

.course-name {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}

.date-badge {
  background: #f1f5f9;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: #475569;
  border: 1px solid #e2e8f0;
  display: inline-block;
  white-space: nowrap;
}

.timestamp {
  color: #94a3b8;
  font-size: 0.75rem;
  font-weight: 400;
  line-height: 1.4;
}

.text-muted {
  color: #94a3b8;
  font-style: italic;
  font-size: 0.875rem;
}

/* Action Buttons Container */
.action-buttons {
  display: flex !important;
  flex-direction: row;
  gap: 0.25rem;
  align-items: center;
  justify-content: center;
  min-width: 120px;
  visibility: visible !important;
  opacity: 1 !important;
}

.action-buttons form {
  display: inline-block;
  margin: 0;
}

.action-buttons .action-btn,
.action-buttons .warn-btn {
  width: auto;
  margin: 0;
  min-width: 32px;
  height: 32px;
  padding: 0.5rem;
}

/* File Upload Container */
.upload-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: center;
}

.upload-container input[type="file"] {
  margin-bottom: 0.25rem;
}

/* View Letter Link */
.view-letter-link {
  background: #8b5cf6;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.75rem;
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  min-width: 70px;
  justify-content: center;
}

.view-letter-link::before {
  content: '\f06e';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  font-size: 0.75rem;
}

.view-letter-link:hover {
  background: #7c3aed;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(139, 92, 246, 0.25);
}

.view-letter-link {
  background: linear-gradient(135deg, var(--info-color) 0%, #0891b2 100%);
  color: var(--white);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  text-decoration: none;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
}

.view-letter-link::before {
  content: '\f1c1';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
}

.view-letter-link:hover {
  background: linear-gradient(135deg, #0891b2 0%, var(--info-color) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Table Cell Improvements */
.attendance-table td {
  position: relative;
}

.attendance-table td em {
  font-style: italic;
  opacity: 0.7;
}

/* Status Badge Styling */
.attendance-table select[name="status"] option[value="Present"] {
  background: #f0fdf4;
  color: #166534;
}

.attendance-table select[name="status"] option[value="Absent"] {
  background: #fef2f2;
  color: #991b1b;
}

/* Loading Animation for Buttons */
.action-btn:active,
.upload-btn:active,
.warn-btn:active {
  transform: scale(0.98);
}

/* Tooltip Styling */
[title] {
  position: relative;
}

/* Status Indicators */
.attendance-table select[name="status"] {
  border: 1px solid var(--border-color);
  background: var(--card-bg);
}

.attendance-table select[name="status"] option[value="Present"] {
  background: #f0fdf4;
  color: #166534;
}

.attendance-table select[name="status"] option[value="Absent"] {
  background: #fef2f2;
  color: #991b1b;
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
  .attendance-table th:nth-child(5), /* Remark */
  .attendance-table td:nth-child(5) {
    width: 12%;
  }

  .attendance-table th:nth-child(6), /* Absent Letter */
  .attendance-table td:nth-child(6) {
    width: 10%;
  }
}

@media (max-width: 1024px) {
  .main-content {
    padding: var(--spacing-md);
  }

  .page-header {
    padding: var(--spacing-md);
  }

  .filter-section {
    padding: var(--spacing-md);
  }

  .attendance-table {
    font-size: 0.8rem;
  }

  .attendance-table th,
  .attendance-table td {
    padding: 1rem 0.5rem;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: var(--spacing-sm);
  }

  .page-header {
    padding: var(--spacing-sm);
  }

  .page-title {
    font-size: 1.5rem;
  }

  .filter-section {
    padding: var(--spacing-sm);
  }

  .filter-section form {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .filter-section select {
    min-width: auto;
  }

  /* Mobile Table Layout */
  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    max-width: 100%;
  }

  .attendance-table {
    min-width: 1000px;
    font-size: 0.75rem;
    table-layout: fixed;
  }

  .attendance-table th,
  .attendance-table td {
    padding: 0.75rem 0.375rem;
  }

  .attendance-table th:first-child,
  .attendance-table td:first-child {
    padding-left: 1rem;
  }

  .attendance-table th:last-child,
  .attendance-table td:last-child {
    padding-right: 1rem;
  }

  .action-btn, .upload-btn, .warn-btn, .view-btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.625rem;
    min-width: 55px;
  }

  .action-buttons {
    gap: 0.125rem;
  }

  .date-badge {
    font-size: 0.625rem;
    padding: 0.25rem 0.5rem;
  }

  .student-name {
    font-size: 0.8rem;
  }

  .course-name {
    font-size: 0.75rem;
  }

  .timestamp {
    font-size: 0.625rem;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.25rem;
  }

  .attendance-table {
    min-width: 700px;
  }

  .action-btn, .upload-btn, .warn-btn, .view-btn {
    padding: 0.25rem 0.375rem;
    font-size: 0.625rem;
    min-width: 50px;
  }
}

/* Print Styles */
@media print {
  .header, .sidebar, .filter-section, footer, .action-buttons {
    display: none !important;
  }

  .main-content {
    padding: 0;
    background: white;
  }

  .page-header {
    margin-bottom: 1rem;
  }

  .attendance-report-container {
    box-shadow: none;
    border: 1px solid #000;
  }

  .attendance-table {
    font-size: 0.75rem;
  }

  .attendance-table th,
  .attendance-table td {
    padding: 0.5rem 0.25rem;
    border: 1px solid #000;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.attendance-report-container {
  animation: fadeIn 0.6s ease-out;
}

.attendance-table tbody tr {
  animation: slideIn 0.4s ease-out;
}

.attendance-table tbody tr:nth-child(even) {
  animation-delay: 0.1s;
}

.msg {
  animation: fadeIn 0.5s ease-out;
}

/* Loading State */
.loading {
  position: relative;
  pointer-events: none;
  opacity: 0.7;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Print Styles */
@media print {
  .header, .sidebar, .filter-section, footer {
    display: none;
  }

  .main-content {
    padding: 0;
    background: white;
  }

  .attendance-report-container {
    box-shadow: none;
    border: none;
  }

  .attendance-table {
    font-size: 0.75rem;
  }

  .action-btn, .upload-btn, .warn-btn {
    display: none;
  }
}
